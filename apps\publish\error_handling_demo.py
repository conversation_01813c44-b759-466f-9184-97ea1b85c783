"""
Demo script showing how to use the comprehensive error handling system.

This script demonstrates the various error handling components and how they work together.
"""

import logging
from typing import Optional

from .exceptions import (
    PublishError, ValidationError, E3ConnectionError, ExportError,
    ConfigurationError, FileOperationError, ServiceError
)
from .error_handler import (
    ErrorHandler, GUIErrorHandler, ErrorMiddleware, ErrorRecovery,
    error_handler_decorator, gui_error_handler
)


def setup_logging():
    """Set up logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def demo_basic_error_handling():
    """Demonstrate basic error handling."""
    print("\n=== Basic Error Handling Demo ===")
    
    logger = setup_logging()
    error_handler = ErrorHandler(logger)
    
    # Create various types of errors
    errors = [
        ValidationError("Invalid field value", field="username", value=""),
        E3ConnectionError("Failed to connect to E3", operation="GetApplication"),
        ExportError("PDF export failed", export_type="PDF", output_path="/test/output.pdf"),
        ConfigurationError("Missing config file", config_file="config.json"),
        FileOperationError("Cannot create directory", file_path="/test/dir", operation="mkdir")
    ]
    
    for error in errors:
        print(f"\nHandling {type(error).__name__}:")
        user_message = error_handler.handle_error(error)
        print(f"User message: {user_message}")


def demo_error_context():
    """Demonstrate error context handling."""
    print("\n=== Error Context Demo ===")
    
    logger = setup_logging()
    error_handler = ErrorHandler(logger)
    
    # Create error with context
    error = PublishError("Operation failed")
    error.add_context("operation", "publish_project")
    error.add_context("project_id", "12345")
    error.add_context("user", "test_user")
    
    print("Error with context:")
    print(f"Error: {error}")
    
    # Handle with additional context
    additional_context = {"timestamp": "2024-01-01 12:00:00", "retry_count": 3}
    user_message = error_handler.handle_error(error, additional_context)
    print(f"User message: {user_message}")


@error_handler_decorator()
def demo_function_with_error():
    """Function that demonstrates decorator error handling."""
    raise ValidationError("Demo validation error", field="demo_field")


def demo_decorators():
    """Demonstrate error handling decorators."""
    print("\n=== Decorator Demo ===")
    
    print("Calling function with error handler decorator:")
    result = demo_function_with_error()
    print(f"Result: {result}")  # Should be None due to error


def demo_error_recovery():
    """Demonstrate error recovery utilities."""
    print("\n=== Error Recovery Demo ===")
    
    logger = setup_logging()
    recovery = ErrorRecovery(logger)
    
    # Simulate an operation that fails a few times then succeeds
    attempt_count = 0
    
    def flaky_operation():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise ConnectionError(f"Connection failed (attempt {attempt_count})")
        return f"Success on attempt {attempt_count}"
    
    print("Testing retry operation:")
    try:
        result = recovery.retry_operation(flaky_operation, max_retries=3, delay=0.1)
        print(f"Operation result: {result}")
    except Exception as e:
        print(f"Operation failed: {e}")
    
    # Demonstrate safe cleanup
    print("\nTesting safe cleanup:")
    cleanup_calls = []
    
    def cleanup1():
        cleanup_calls.append("cleanup1")
        print("Cleanup 1 executed")
    
    def cleanup2():
        cleanup_calls.append("cleanup2")
        raise Exception("Cleanup 2 failed")
    
    def cleanup3():
        cleanup_calls.append("cleanup3")
        print("Cleanup 3 executed")
    
    recovery.safe_cleanup([cleanup1, cleanup2, cleanup3])
    print(f"Cleanup calls executed: {cleanup_calls}")


def demo_gui_error_handling():
    """Demonstrate GUI error handling (without actual GUI)."""
    print("\n=== GUI Error Handling Demo ===")
    
    logger = setup_logging()
    
    # Mock dialog function
    def mock_show_dialog(title: str, message: str):
        print(f"DIALOG - {title}: {message}")
    
    gui_handler = GUIErrorHandler(logger, mock_show_dialog)
    
    # Test GUI error handling
    error = ValidationError("Invalid input data", field="user_input")
    print("Handling GUI error:")
    gui_handler.handle_gui_error(error, "Input Validation Error")


def demo_error_middleware():
    """Demonstrate error middleware."""
    print("\n=== Error Middleware Demo ===")
    
    logger = setup_logging()
    middleware = ErrorMiddleware(logger)
    
    # Mock GUI handler
    def mock_show_dialog(title: str, message: str):
        print(f"MIDDLEWARE DIALOG - {title}: {message}")
    
    gui_handler = GUIErrorHandler(logger, mock_show_dialog)
    middleware.set_gui_error_handler(gui_handler)
    
    # Test middleware error handling
    error = ServiceError("Service operation failed", service="PublishService", operation="publish_project")
    print("Handling error through middleware:")
    middleware.handle_operation_error(error, "Publish Operation", show_dialog=True)


def main():
    """Run all error handling demos."""
    print("Comprehensive Error Handling System Demo")
    print("=" * 50)
    
    try:
        demo_basic_error_handling()
        demo_error_context()
        demo_decorators()
        demo_error_recovery()
        demo_gui_error_handling()
        demo_error_middleware()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()