"""
Unit tests for ProjectData model and related classes.
"""

import unittest
import tempfile
import os
from unittest.mock import patch

# Add the parent directory to the path to import from apps
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.models.project_data import ProjectData, TitleBlockData, ValidationResult


class TestProjectData(unittest.TestCase):
    """Test cases for ProjectData class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.valid_data = ProjectData(
            gss_parent="GSS-12345",
            serial_number="SN-001",
            customer="Test Customer",
            location="Test Location",
            title="Test Project",
            sales_order="SO-12345",
            model="TestModel",
            folder_path="/test/path"
        )
    
    def test_project_data_creation(self):
        """Test ProjectData creation with valid data."""
        data = ProjectData(
            gss_parent="GSS-12345",
            serial_number="SN-001",
            customer="Test Customer"
        )
        self.assertEqual(data.gss_parent, "GSS-12345")
        self.assertEqual(data.serial_number, "SN-001")
        self.assertEqual(data.customer, "Test Customer")
    
    def test_project_data_post_init_strips_whitespace(self):
        """Test that __post_init__ strips whitespace from fields."""
        data = ProjectData(
            gss_parent="  GSS-12345  ",
            serial_number="  SN-001  ",
            customer="  Test Customer  "
        )
        self.assertEqual(data.gss_parent, "GSS-12345")
        self.assertEqual(data.serial_number, "SN-001")
        self.assertEqual(data.customer, "Test Customer")
    
    def test_validate_valid_data(self):
        """Test validation with valid data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            self.valid_data.folder_path = temp_dir
            result = self.valid_data.validate()
            if not result.is_valid:
                print(f"Validation errors: {result.errors}")
                print(f"Validation warnings: {result.warnings}")
            self.assertTrue(result.is_valid)
            self.assertEqual(len(result.errors), 0)
    
    def test_validate_missing_required_fields(self):
        """Test validation with missing required fields."""
        data = ProjectData()
        result = data.validate()
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
        
        # Check for specific required field errors
        error_messages = " ".join(result.errors)
        self.assertIn("GSS Parent", error_messages)
        self.assertIn("Serial number", error_messages)
        self.assertIn("Model", error_messages)
        self.assertIn("folder path", error_messages)
    
    def test_validate_field_length_limits(self):
        """Test validation with fields exceeding length limits."""
        data = ProjectData(
            gss_parent="A" * 51,  # Too long
            serial_number="B" * 51,  # Too long
            customer="C" * 101,  # Warning threshold
            title="D" * 201,  # Warning threshold
            model="TestModel",
            folder_path="/test/path"
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            data.folder_path = temp_dir
            result = data.validate()
            self.assertFalse(result.is_valid)  # Should fail due to length errors
            
            error_messages = " ".join(result.errors)
            self.assertIn("too long", error_messages)
            
            warning_messages = " ".join(result.warnings)
            self.assertIn("very long", warning_messages)
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        data_dict = self.valid_data.to_dict()
        self.assertIsInstance(data_dict, dict)
        self.assertEqual(data_dict["gss_parent"], "GSS-12345")
        self.assertEqual(data_dict["serial_number"], "SN-001")
        self.assertIn("timestamp", data_dict)
    
    def test_from_dict(self):
        """Test creation from dictionary."""
        data_dict = {
            "gss_parent": "GSS-12345",
            "serial_number": "SN-001",
            "customer": "Test Customer",
            "model": "TestModel"
        }
        data = ProjectData.from_dict(data_dict)
        self.assertEqual(data.gss_parent, "GSS-12345")
        self.assertEqual(data.serial_number, "SN-001")
        self.assertEqual(data.customer, "Test Customer")
        self.assertEqual(data.model, "TestModel")
    
    def test_is_empty(self):
        """Test empty data detection."""
        empty_data = ProjectData()
        self.assertTrue(empty_data.is_empty())
        
        non_empty_data = ProjectData(gss_parent="GSS-123")
        self.assertFalse(non_empty_data.is_empty())


class TestTitleBlockData(unittest.TestCase):
    """Test cases for TitleBlockData class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.title_block = TitleBlockData(
            document_number="DOC-12345",
            model="TestModel",
            description="Test Description",
            customer="Test Customer",
            location="Test Location",
            sales_order="SO-12345",
            serial_number="SN-001"
        )
    
    def test_title_block_creation(self):
        """Test TitleBlockData creation."""
        self.assertEqual(self.title_block.document_number, "DOC-12345")
        self.assertEqual(self.title_block.model, "TestModel")
        self.assertEqual(self.title_block.serial_number, "SN-001")
    
    def test_title_block_post_init_strips_whitespace(self):
        """Test that __post_init__ strips whitespace."""
        data = TitleBlockData(
            document_number="  DOC-12345  ",
            model="  TestModel  ",
            serial_number="  SN-001  "
        )
        self.assertEqual(data.document_number, "DOC-12345")
        self.assertEqual(data.model, "TestModel")
        self.assertEqual(data.serial_number, "SN-001")
    
    def test_to_project_data(self):
        """Test conversion to ProjectData."""
        project_data = self.title_block.to_project_data(
            folder_path="/test/path",
            model_override="OverrideModel"
        )
        
        self.assertIsInstance(project_data, ProjectData)
        self.assertEqual(project_data.gss_parent, "DOC-12345")
        self.assertEqual(project_data.serial_number, "SN-001")
        self.assertEqual(project_data.customer, "Test Customer")
        self.assertEqual(project_data.model, "OverrideModel")
        self.assertEqual(project_data.folder_path, "/test/path")
    
    def test_to_project_data_no_override(self):
        """Test conversion to ProjectData without model override."""
        project_data = self.title_block.to_project_data(folder_path="/test/path")
        self.assertEqual(project_data.model, "TestModel")
    
    def test_is_valid(self):
        """Test validity check."""
        self.assertTrue(self.title_block.is_valid())
        
        invalid_data = TitleBlockData()
        self.assertFalse(invalid_data.is_valid())
        
        partial_data = TitleBlockData(document_number="DOC-123")
        self.assertFalse(partial_data.is_valid())
        
        minimal_valid = TitleBlockData(
            document_number="DOC-123",
            serial_number="SN-001"
        )
        self.assertTrue(minimal_valid.is_valid())


class TestValidationResult(unittest.TestCase):
    """Test cases for ValidationResult class."""
    
    def test_validation_result_creation(self):
        """Test ValidationResult creation."""
        result = ValidationResult(is_valid=True, errors=["error1"], warnings=["warning1"])
        self.assertTrue(result.is_valid)
        self.assertEqual(result.errors, ["error1"])
        self.assertEqual(result.warnings, ["warning1"])
    
    def test_add_error(self):
        """Test adding errors."""
        result = ValidationResult(is_valid=True)
        result.add_error("Test error")
        self.assertFalse(result.is_valid)
        self.assertIn("Test error", result.errors)
    
    def test_add_warning(self):
        """Test adding warnings."""
        result = ValidationResult(is_valid=True)
        result.add_warning("Test warning")
        self.assertTrue(result.is_valid)  # Warnings don't affect validity
        self.assertIn("Test warning", result.warnings)
    
    def test_has_errors(self):
        """Test error detection."""
        result = ValidationResult()
        self.assertFalse(result.has_errors())
        
        result.add_error("Test error")
        self.assertTrue(result.has_errors())
    
    def test_has_warnings(self):
        """Test warning detection."""
        result = ValidationResult()
        self.assertFalse(result.has_warnings())
        
        result.add_warning("Test warning")
        self.assertTrue(result.has_warnings())
    
    def test_get_summary(self):
        """Test summary generation."""
        # Valid with no warnings
        result = ValidationResult(is_valid=True)
        self.assertEqual(result.get_summary(), "Validation passed")
        
        # Valid with warnings
        result = ValidationResult(is_valid=True, warnings=["warning1"])
        self.assertIn("warning", result.get_summary())
        
        # Invalid with errors
        result = ValidationResult(is_valid=False, errors=["error1", "error2"])
        summary = result.get_summary()
        self.assertIn("failed", summary)
        self.assertIn("2 error", summary)


if __name__ == '__main__':
    unittest.main()