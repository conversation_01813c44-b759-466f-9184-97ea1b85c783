"""
Project data model for the publish application.

This module contains the ProjectData dataclass and related functionality
for managing project information.
"""

from dataclasses import dataclass
from typing import Optional, List
import os
import re
from datetime import datetime


@dataclass
class ProjectData:
    """Data structure for project information with validation."""
    
    gss_parent: str = ""
    serial_number: str = ""
    customer: str = ""
    location: str = ""
    title: str = ""
    sales_order: str = ""
    model: str = ""
    folder_path: str = ""
    
    def __post_init__(self):
        """Perform post-initialization validation and cleanup."""
        # Strip whitespace from all string fields
        self.gss_parent = self.gss_parent.strip()
        self.serial_number = self.serial_number.strip()
        self.customer = self.customer.strip()
        self.location = self.location.strip()
        self.title = self.title.strip()
        self.sales_order = self.sales_order.strip()
        self.model = self.model.strip()
        self.folder_path = self.folder_path.strip()
    
    def validate(self) -> 'ValidationResult':
        """
        Validate the project data.
        
        Returns:
            ValidationResult indicating if data is valid
        """
        from .validation import (
            validate_gss_parent, 
            validate_serial_number, 
            validate_file_path
        )
        
        errors = []
        warnings = []
        
        # Validate required fields
        errors.extend(validate_gss_parent(self.gss_parent))
        errors.extend(validate_serial_number(self.serial_number))
        
        # Validate optional but important fields
        if not self.customer:
            warnings.append("Customer field is empty")
        if not self.title:
            warnings.append("Title field is empty")
        if not self.model:
            errors.append("Model selection is required")
            
        # Validate folder path if provided
        if self.folder_path:
            errors.extend(validate_file_path(self.folder_path))
        else:
            errors.append("Output folder path is required")
            
        # Validate field lengths
        if len(self.gss_parent) > 50:
            errors.append("GSS Parent number is too long (max 50 characters)")
        if len(self.serial_number) > 50:
            errors.append("Serial number is too long (max 50 characters)")
        if len(self.customer) > 100:
            warnings.append("Customer name is very long (over 100 characters)")
        if len(self.title) > 200:
            warnings.append("Title is very long (over 200 characters)")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def to_dict(self) -> dict:
        """
        Convert ProjectData to dictionary.
        
        Returns:
            Dictionary representation of project data
        """
        return {
            "gss_parent": self.gss_parent,
            "serial_number": self.serial_number,
            "customer": self.customer,
            "location": self.location,
            "title": self.title,
            "sales_order": self.sales_order,
            "model": self.model,
            "folder_path": self.folder_path,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ProjectData':
        """
        Create ProjectData from dictionary.
        
        Args:
            data: Dictionary containing project data
            
        Returns:
            ProjectData instance
        """
        return cls(
            gss_parent=data.get("gss_parent", ""),
            serial_number=data.get("serial_number", ""),
            customer=data.get("customer", ""),
            location=data.get("location", ""),
            title=data.get("title", ""),
            sales_order=data.get("sales_order", ""),
            model=data.get("model", ""),
            folder_path=data.get("folder_path", "")
        )
    
    def is_empty(self) -> bool:
        """
        Check if all fields are empty.
        
        Returns:
            True if all fields are empty
        """
        return not any([
            self.gss_parent, self.serial_number, self.customer,
            self.location, self.title, self.sales_order,
            self.model, self.folder_path
        ])


@dataclass
class TitleBlockData:
    """Data structure for title block information from E3 sheets."""
    
    document_number: str = ""
    model: str = ""
    description: str = ""
    customer: str = ""
    location: str = ""
    sales_order: str = ""
    serial_number: str = ""
    
    def __post_init__(self):
        """Perform post-initialization cleanup."""
        # Strip whitespace from all string fields
        self.document_number = self.document_number.strip()
        self.model = self.model.strip()
        self.description = self.description.strip()
        self.customer = self.customer.strip()
        self.location = self.location.strip()
        self.sales_order = self.sales_order.strip()
        self.serial_number = self.serial_number.strip()
    
    def to_project_data(self, folder_path: str = "", model_override: str = "") -> ProjectData:
        """
        Convert TitleBlockData to ProjectData.
        
        Args:
            folder_path: Output folder path
            model_override: Override model selection
            
        Returns:
            ProjectData instance
        """
        return ProjectData(
            gss_parent=self.document_number,
            serial_number=self.serial_number,
            customer=self.customer,
            location=self.location,
            title=self.description,
            sales_order=self.sales_order,
            model=model_override or self.model,
            folder_path=folder_path
        )
    
    def is_valid(self) -> bool:
        """
        Check if title block data contains essential information.
        
        Returns:
            True if essential fields are present
        """
        return bool(self.document_number and self.serial_number)


class ValidationResult:
    """Result of data validation operations."""
    
    def __init__(self, is_valid: bool = False, errors: List[str] = None, warnings: List[str] = None):
        """
        Initialize validation result.
        
        Args:
            is_valid: Whether the validation passed
            errors: List of error messages
            warnings: List of warning messages
        """
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str):
        """Add an error message."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add a warning message."""
        self.warnings.append(warning)
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0
    
    def get_summary(self) -> str:
        """Get a summary of validation results."""
        if self.is_valid and not self.has_warnings():
            return "Validation passed"
        elif self.is_valid and self.has_warnings():
            return f"Validation passed with {len(self.warnings)} warning(s)"
        else:
            return f"Validation failed with {len(self.errors)} error(s)"