<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Internal Identifier Handling" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Local Objects - Internal_Identifier_Handling</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h4><a name="kanchor1232"></a>Local Objects</h4>
            <p>It is possible to create objects local to a subroutine or function. 
 Please keep in mind though, that these local objects have to be initialized 
 by <span style="font-family: 'Courier New', monospace;font-weight: bold;">any.SetId</span> 
 before they can be used. </p>
            <p>Local objects are automatically destroyed when leaving the subroutine 
 or the function. </p>
            <p>The following example shows a function, that returns the fully qualified 
 name of a device, given by an Id:</p>
            <table style="x-border-left: 1px table-solid;x-border-top: 1px table-solid;x-border-right: 1px table-solid;x-border-bottom: 1px table-solid;x-border-left: 1px table-solid;x-border-top: 1px table-solid;x-border-right: 1px table-solid;x-border-bottom: 1px table-solid;border-collapse: separate;" border="1">
                <tr>
                    <td><pre xml:space="preserve">
<span style="font-family: 'Courier New', monospace;font-size: 10pt;">...
 <spaces>&#160;&#160;</spaces>devnam = FullName( devid )
 <spaces>&#160;&#160;</spaces>MsgBox "Device " &amp; devnam &amp; ": "
...
Function FullName( devid )
 <spaces>&#160;&#160;&#160;</spaces>Dim dev
 <spaces>&#160;&#160;&#160;</spaces>Set dev = prj.CreateDeviceObject
 <spaces>&#160;&#160;&#160;</spaces>dev.SetId devid
 <spaces>&#160;&#160;&#160;</spaces>FullName = dev.GetAssignment _
 <spaces>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</spaces>&amp; dev.GetLocation _
 <spaces>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</spaces>&amp; dev.GetName
End Function</span>
</pre>
                    </td>
                </tr>
            </table>
            <p>&#160;</p>
            <table wrapperparagraphselector="P" style="width: 100%;margin-top: 14pt;border-spacing: 0px;border-spacing: 0px;" cellspacing="0" width="100%">
                <tr>
                    <td style="width: 20%;padding-left: 2px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;" valign="top" width="20%">
                        <p style="margin-bottom: 0;font-weight: bold;">See also:</p>
                    </td>
                    <td style="width: 80%;padding-left: 2px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;" valign="top" width="80%">
                        <ul style="list-style: disc;" type="disc">
                            <li><a href="Introduction.htm">Handling Internal Identifiers - Introduction</a>
                            </li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>