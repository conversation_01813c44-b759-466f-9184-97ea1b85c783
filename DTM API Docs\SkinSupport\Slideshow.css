/**
 * BxSlider v4.0 - Fully loaded, responsive content slider
 * http://bxslider.com
 *
 * Written by: <PERSON>, 2012
 * http://stevenwanderski.com
 * (while drinking Belgian ales and listening to jazz)
 *
 * CEO and founder of bxCreative, LTD
 * http://bxcreative.com
 */


/** RESET AND LAYOUT
===================================*/

.mc-wrapper {
	position: relative;
	margin: 0 auto 60px;
	padding: 0;
	zoom: 1;
    clear: both;
}

/** THEME
===================================*/

.mc-wrapper .mc-viewport {
    box-sizing: content-box; 
    z-index: 0;
}

.mc-wrapper .mc-controls {
    z-index: 1;
}

.mc-wrapper .mc-pager,
.mc-wrapper .mc-controls-auto {
	position: absolute;
	bottom: -30px;
	width: 100%;
}

.mc-wrapper img,
.mc-wrapper object,
.mc-wrapper table{ 
    max-width: 100%;
    word-wrap: break-word;
    table-layout: fixed;
}

div.MCSlide{
    word-wrap: break-word;
    visibility: hidden;
    -webkit-margin-collapse: separate;
}

/* LOADER */

.mc-wrapper .mc-loading {
	min-height: 50px;
    background-image: url(Images/MCSlider_loader.gif);
    background-repeat:no-repeat;
    background-color:#fff;
    background-position: center center;
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: auto;
}

/* PAGER */

.mc-wrapper .mc-pager {
	text-align: center;
	font-size: .85em;
	font-family: Arial;
	font-weight: bold;
	color: #666;
	padding-top: 20px;
}

.mc-wrapper .mc-pager .mc-pager-item,
.mc-wrapper .mc-controls-auto .mc-controls-auto-item {
	display: inline-block;
	zoom: 1;
}

.mc-wrapper .mc-pager.mc-default-pager a {
	background: #666;
	text-indent: -9999px;
	display: block;
	width: 10px;
	height: 10px;
	margin: 0 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
    cursor: pointer;
}

.mc-wrapper .mc-pager.mc-default-pager a:hover,
.mc-wrapper .mc-pager.mc-default-pager a.active {
	background: #000;
}

/* DIRECTION CONTROLS (NEXT / PREV) */

.mc-wrapper .mc-prev {
	/*left: 10px;*/
    background-image: url(Images/MCSlider_controls.png);
    background-repeat:no-repeat;
    background-position: 0 -32px;
}

.mc-wrapper .mc-next {
	/*right: 10px;*/
    background-image: url(Images/MCSlider_controls.png);
    background-repeat:no-repeat;
    background-position: -43px -32px;
}

.mc-wrapper .mc-prev:hover {
	background-position: 0 0;
}

.mc-wrapper .mc-next:hover {
	background-position: -43px 0;
}

.mc-wrapper .mc-controls-direction a {
	position: absolute;
	top: 50%;
	margin-top: -16px;
	width: 32px;
	height: 32px;
	text-indent: -9999px;
	z-index: auto;
    cursor: pointer;
}

.mc-wrapper .mc-controls-direction a.disabled {
	display: none;
}

/* AUTO CONTROLS (START / STOP) */

.mc-wrapper .mc-controls-auto {
	text-align: center;
}

.mc-wrapper .mc-controls-auto .mc-start {
	display: block;
	text-indent: -9999px;
	width: 10px;
	height: 11px;
    background-image: url(Images/MCSlider_controls.png);
    background-repeat:no-repeat;
    background-position: -86px -11px;
	margin: 0 3px;
    cursor: pointer;
}

.mc-wrapper .mc-controls-auto .mc-start:hover,
.mc-wrapper .mc-controls-auto .mc-start.active {
	background-position: -86px 0;
}

.mc-wrapper .mc-controls-auto .mc-stop {
	display: block;
	text-indent: -9999px;
	width: 9px;
	height: 11px;
    background-image: url(Images/MCSlider_controls.png);
    background-repeat:no-repeat;
    background-position: -86px -44px;
	margin: 0 3px;
    cursor: pointer;
}

.mc-wrapper .mc-controls-auto .mc-stop:hover,
.mc-wrapper .mc-controls-auto .mc-stop.active {
	background-position: -86px -33px;
}

/* PAGER WITH AUTO-CONTROLS HYBRID LAYOUT */

.mc-wrapper .mc-controls.mc-has-controls-auto.mc-has-pager .mc-pager {
	text-align: left;
	width: 100%;
}

.mc-wrapper .mc-controls.mc-has-controls-auto.mc-has-pager .mc-controls-auto {
	right: 0;
	width: 35px;
}

/* IMAGE CAPTIONS */

.mc-wrapper .mc-caption {
	position: absolute;
	bottom: 0;
	left: 0;
	background: #666\9;
	background: rgba(80, 80, 80, 0.75);
	width: 100%;
}

.mc-wrapper .mc-caption div {
	color: #fff;
	font-family: Arial;
	display: block;
	font-size: .85em;
	padding: 10px;
}

/* SLIDE THUMBNAILS */

.mc-thumbnail {
	text-align: center;
	margin-top: -30px;
}

.mc-thumbnail a img {
	margin: 0 3px;
	padding: 3px;
	border: solid 1px #ccc;
	width: 50px;
    cursor: pointer;
}

.mc-thumbnail a:hover img,
.mc-thumbnail a.active img {
	border: solid 1px #5280DD;
}

