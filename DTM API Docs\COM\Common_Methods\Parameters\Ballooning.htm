<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Parameters" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Ballooning</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor723"></a><a name="kanchor724"></a><a name="kanchor725"></a>Ballooning
		</h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">Integer</span> <i>ballooning</i></p>
            <h4>Description</h4>
            <p>Parameter represents a ballooning value as an integer.</p>
            <h4>Possible Values</h4>
            <table style="width: 100%;border-top-left-radius: 1px;border-top-right-radius: 1px;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;border-bottom-style: solid;border-bottom-width: 1px;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 169px;" class="TableStyle-Rows-Column-Column1" />
                <col style="width: 169px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Decimal</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Hexadeciimal</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0x0000</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No ballooning</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">1</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">0x0001</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Circle around text</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">2</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0x0002</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Oval around text</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">4</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">0x0004</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Rectangle around text</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">8</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0x0008</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Ellipse around text</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">16</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">0x0010</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Line to owner</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">32</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0x0020</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Horizontal line on center of text box
				  		<p>Available since v2018-19.00</p></td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">64</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">0x0040</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Horizontal line on bottom of text box
                        <p>Available since v2018-19.00</p></td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyB-Column1-Body2">128</td>
                        <td class="TableStyle-Rows-BodyB-Column1-Body2">0x0080</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body2">Horizontal line on top of text box
                        <p>Available since v2018-19.00</p></td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>Ballooning flags should be used independently with the exception of Line to owner (16) which can be used in combination with circle (1), oval (2), rectangle (4) or ellipse (8). For example a ballooning value of 20 represents a rectangle and Line to owner.</p>
            <p>Line to owner is only applicable if the text has an owner.</p>
            <h4>Version Information</h4>
            <p>Introduced in v2010-9.10.</p>
            <p>Modified in v2018-19.00.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="../../Classes/e3DbeText/GetBallooning.htm">e3DbeText.GetBallooning()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3DbeText/SetBallooning.htm">e3DbeText.SetBallooning()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Text/CalculateBoxAt.htm">e3Text.CalculateBoxAt()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Text/getballooning.htm">e3Text.GetBalllooning()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Text/SetBallooning.htm">e3Text.SetBallooning()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>