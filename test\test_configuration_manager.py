"""
Unit tests for ConfigurationManager.
"""

import json
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, mock_open

from apps.publish.config.configuration_manager import ConfigurationManager
from apps.publish.config.exceptions import (
    ConfigurationError, 
    ConfigurationLoadError, 
    ConfigurationValidationError
)


class TestConfigurationManager(unittest.TestCase):
    """Test cases for ConfigurationManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
        
        # Sample valid configurations
        self.valid_app_config = {
            "folder_to_monitor": "/test/path",
            "target_email": "<EMAIL>"
        }
        
        self.valid_models_config = {
            "TestCategory": {
                "TEST-001": {
                    "template_path": "/test/template.dotx",
                    "drawings_path": "/test/drawings",
                    "asme_flag": True,
                    "controls_parent": "TEST123"
                }
            }
        }
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def _create_config_file(self, filename: str, content: dict):
        """Helper to create a config file."""
        config_file = self.config_dir / filename
        with open(config_file, 'w') as f:
            json.dump(content, f)
            
    def test_init_default_config_dir(self):
        """Test initialization with default config directory."""
        manager = ConfigurationManager()
        self.assertIsNotNone(manager.config_dir)
        # Check that the path contains resources and config (handles both / and \ separators)
        config_path_str = str(manager.config_dir).replace('\\', '/')
        self.assertTrue(config_path_str.endswith("resources/config"))
        
    def test_init_custom_config_dir(self):
        """Test initialization with custom config directory."""
        manager = ConfigurationManager(str(self.config_dir))
        self.assertEqual(manager.config_dir, self.config_dir)
        
    def test_load_app_config_success(self):
        """Test successful loading of application config."""
        self._create_config_file("config.json", self.valid_app_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        config = manager.load_app_config()
        
        self.assertEqual(config, self.valid_app_config)
        
    def test_load_app_config_file_not_found(self):
        """Test loading app config when file doesn't exist."""
        manager = ConfigurationManager(str(self.config_dir))
        
        with self.assertRaises(ConfigurationLoadError) as context:
            manager.load_app_config()
            
        self.assertIn("not found", str(context.exception))
        
    def test_load_app_config_invalid_json(self):
        """Test loading app config with invalid JSON."""
        config_file = self.config_dir / "config.json"
        with open(config_file, 'w') as f:
            f.write("{ invalid json }")
            
        manager = ConfigurationManager(str(self.config_dir))
        
        with self.assertRaises(ConfigurationLoadError) as context:
            manager.load_app_config()
            
        self.assertIn("Invalid JSON", str(context.exception))
        
    def test_load_models_config_success(self):
        """Test successful loading of models config."""
        self._create_config_file("models.json", self.valid_models_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        config = manager.load_models_config()
        
        self.assertEqual(config, self.valid_models_config)
        
    def test_load_models_config_validation_error(self):
        """Test models config validation error."""
        invalid_config = {
            "TestCategory": {
                "TEST-001": {
                    "template_path": "/test/template.dotx",
                    # Missing required fields
                }
            }
        }
        self._create_config_file("models.json", invalid_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        with self.assertRaises(ConfigurationValidationError) as context:
            manager.load_models_config()
            
        self.assertIn("missing required field", str(context.exception))
        
    def test_get_app_setting(self):
        """Test getting specific app setting."""
        self._create_config_file("config.json", self.valid_app_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        # Test existing setting
        email = manager.get_app_setting("target_email")
        self.assertEqual(email, "<EMAIL>")
        
        # Test non-existing setting with default
        default_value = manager.get_app_setting("non_existing", "default")
        self.assertEqual(default_value, "default")
        
    def test_get_model_config(self):
        """Test getting specific model config."""
        self._create_config_file("models.json", self.valid_models_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        # Test existing model
        model_config = manager.get_model_config("TestCategory", "TEST-001")
        expected = self.valid_models_config["TestCategory"]["TEST-001"]
        self.assertEqual(model_config, expected)
        
        # Test non-existing model
        non_existing = manager.get_model_config("NonExisting", "MODEL")
        self.assertIsNone(non_existing)
        
    def test_get_models_for_category(self):
        """Test getting all models for a category."""
        self._create_config_file("models.json", self.valid_models_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        # Test existing category
        models = manager.get_models_for_category("TestCategory")
        expected = self.valid_models_config["TestCategory"]
        self.assertEqual(models, expected)
        
        # Test non-existing category
        empty_models = manager.get_models_for_category("NonExisting")
        self.assertEqual(empty_models, {})
        
    def test_get_all_categories(self):
        """Test getting all model categories."""
        self._create_config_file("models.json", self.valid_models_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        categories = manager.get_all_categories()
        
        self.assertEqual(categories, ["TestCategory"])
        
    def test_reload_config(self):
        """Test configuration reloading."""
        self._create_config_file("config.json", self.valid_app_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        # Load initial config
        config1 = manager.load_app_config()
        
        # Modify config file
        new_config = {"new_setting": "new_value"}
        self._create_config_file("config.json", new_config)
        
        # Reload should get new config
        manager.reload_config()
        config2 = manager.load_app_config()
        
        self.assertNotEqual(config1, config2)
        self.assertEqual(config2, new_config)
        
    def test_validate_model_data_invalid_types(self):
        """Test model data validation with invalid types."""
        invalid_config = {
            "TestCategory": {
                "TEST-001": {
                    "template_path": 123,  # Should be string
                    "drawings_path": "/test/drawings",
                    "asme_flag": True,
                    "controls_parent": "TEST123"
                }
            }
        }
        self._create_config_file("models.json", invalid_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        with self.assertRaises(ConfigurationValidationError) as context:
            manager.load_models_config()
            
        self.assertIn("must be a string", str(context.exception))
        
    def test_caching_behavior(self):
        """Test that configurations are cached after first load."""
        self._create_config_file("config.json", self.valid_app_config)
        self._create_config_file("models.json", self.valid_models_config)
        
        manager = ConfigurationManager(str(self.config_dir))
        
        # First load
        config1 = manager.load_app_config()
        models1 = manager.load_models_config()
        
        # Second load should return same objects (cached)
        config2 = manager.load_app_config()
        models2 = manager.load_models_config()
        
        self.assertIs(config1, config2)
        self.assertIs(models1, models2)


if __name__ == '__main__':
    unittest.main()