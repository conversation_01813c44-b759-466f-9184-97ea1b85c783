"""
PDF Manual Printing Module

This module provides functions for manual printing of PDF sections.
"""

import os
import sys
import logging
import tempfile
import shutil
import subprocess
from typing import List, Dict, Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER
from lib.pdf_printing_engine import PrinterProfile

def extract_sections_to_files(sections: List[PDFSection], output_dir: str) -> Dict[str, str]:
    """
    Extract PDF sections to separate files in the output directory.
    
    Args:
        sections: A list of PDF sections
        output_dir: The directory to save the extracted files
        
    Returns:
        Dict[str, str]: A dictionary mapping section descriptions to file paths
    """
    section_files = {}
    
    for i, section in enumerate(sections):
        if not section.temp_file_path or not os.path.exists(section.temp_file_path):
            logging.error(f"Temporary file for section {section} not found")
            continue
        
        # Create a descriptive filename
        filename = f"Section_{i+1}_{section.page_type}_Pages_{section.start_page+1}"
        if section.start_page != section.end_page:
            filename += f"-{section.end_page+1}"
        filename += ".pdf"
        
        # Copy the temporary file to the output directory
        output_path = os.path.join(output_dir, filename)
        shutil.copy2(section.temp_file_path, output_path)
        
        # Add the file to the dictionary
        section_description = f"Section {i+1}: {section.page_type} (Pages {section.start_page+1}"
        if section.start_page != section.end_page:
            section_description += f"-{section.end_page+1}"
        section_description += ")"
        
        section_files[section_description] = output_path
    
    return section_files

def get_print_instructions(section_description: str, profile: PrinterProfile) -> str:
    """
    Get printing instructions for a section.
    
    Args:
        section_description: The description of the section
        profile: The printer profile to use
        
    Returns:
        str: The printing instructions
    """
    # Get the page type from the section description
    page_type = "Unknown"
    if PAGE_TYPE_LETTER in section_description:
        page_type = PAGE_TYPE_LETTER
    elif PAGE_TYPE_TABLOID in section_description:
        page_type = PAGE_TYPE_TABLOID
    elif PAGE_TYPE_OTHER in section_description:
        page_type = PAGE_TYPE_OTHER
    
    # Build the instructions
    instructions = f"Printing Instructions for {section_description}:\n\n"
    
    instructions += f"1. Printer: {profile.printer_name}\n"
    
    # Paper size
    if page_type == PAGE_TYPE_LETTER:
        instructions += "2. Paper Size: Letter (8.5 x 11 in)\n"
    elif page_type == PAGE_TYPE_TABLOID:
        instructions += "2. Paper Size: Tabloid (11 x 17 in)\n"
    else:
        instructions += "2. Paper Size: Letter (8.5 x 11 in)\n"
    
    # Orientation
    if page_type == PAGE_TYPE_LETTER:
        instructions += "3. Orientation: Portrait\n"
    elif page_type == PAGE_TYPE_TABLOID:
        instructions += "3. Orientation: Landscape\n"
    else:
        instructions += "3. Orientation: Portrait\n"
    
    # Duplex
    if page_type == PAGE_TYPE_LETTER:
        instructions += "4. Duplex: Double-sided (Flip on long edge)\n"
    elif page_type == PAGE_TYPE_TABLOID:
        instructions += "4. Duplex: Single-sided\n"
    else:
        instructions += "4. Duplex: Single-sided\n"
    
    # Color
    instructions += "5. Color: Color\n"
    
    return instructions

def open_pdf_file(pdf_path: str) -> bool:
    """
    Open a PDF file with the default PDF viewer.
    
    Args:
        pdf_path: The path to the PDF file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if sys.platform == 'win32':
            os.startfile(pdf_path)
        elif sys.platform == 'darwin':  # macOS
            subprocess.run(['open', pdf_path], check=True)
        else:  # Linux
            subprocess.run(['xdg-open', pdf_path], check=True)
        
        return True
    except Exception as e:
        logging.error(f"Error opening PDF file: {e}")
        return False
