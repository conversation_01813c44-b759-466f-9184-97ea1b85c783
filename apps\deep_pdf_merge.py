import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import os
from PyPDF2 import PdfMerger
import sys

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")

# Apply the red theme
apply_theme("red", "dark")

class DraggablePDFItem(ctk.CTkFrame):
    def __init__(self, master, text, drag_callback, **kwargs):
        super().__init__(master, **kwargs)
        self.text = text
        self.drag_callback = drag_callback
        self.configure(height=40, corner_radius=8, fg_color=("gray80", "gray20"))

        # Widget elements
        self.drag_handle = ctk.CTkLabel(self, text="≡", width=30)
        self.drag_handle.pack(side="left", padx=(5, 0))

        self.label = ctk.CTkLabel(self, text=text, anchor="w")
        self.label.pack(side="left", fill="both", expand=True, padx=5)

        # Event bindings
        self.drag_handle.bind("<ButtonPress-1>", self.start_drag)
        self.drag_handle.bind("<B1-Motion>", self.do_drag)
        self.drag_handle.bind("<ButtonRelease-1>", self.stop_drag)
        self.label.bind("<ButtonPress-1>", self.start_drag)
        self.label.bind("<B1-Motion>", self.do_drag)
        self.label.bind("<ButtonRelease-1>", self.stop_drag)

    def start_drag(self, event):
        self.drag_callback.start_drag(self, event)
        self.configure(fg_color=("#3B8ED0", "#1F6AA5"))

    def do_drag(self, event):
        self.drag_callback.do_drag(event)

    def stop_drag(self, event):
        self.configure(fg_color=("gray80", "gray20"))
        self.drag_callback.stop_drag()

class DraggablePDFList(ctk.CTkScrollableFrame):
    def __init__(self, master, **kwargs):
        super().__init__(master, **kwargs)
        self.configure(label_text="PDF Files (Drag to reorder)")
        self.items = []
        self.dragging = False
        self.drag_item = None
        self.placeholder = None
        self.drag_start_index = 0

    def add_item(self, text):
        item = DraggablePDFItem(self, text, self)
        item.pack(fill="x", pady=2)
        self.items.append(item)

    def clear_items(self):
        for item in self.items:
            item.destroy()
        self.items.clear()

    def start_drag(self, item, event):
        self.dragging = True
        self.drag_item = item
        self.drag_start_index = self.items.index(item)
        self.placeholder = ctk.CTkFrame(self, height=2, fg_color="white")
        self.update_placeholder(event)

    def do_drag(self, event):
        if self.dragging:
            self.update_placeholder(event)

    def stop_drag(self):
        if self.dragging and self.placeholder:
            current_index = self.items.index(self.drag_item)
            new_index = self.get_placeholder_index()

            if new_index != current_index:
                self.items.insert(new_index, self.items.pop(current_index))
                self.reorder_items()

            self.placeholder.destroy()
            self.placeholder = None
            self.dragging = False

    def update_placeholder(self, event):
        canvas = self._parent_canvas
        y = canvas.canvasy(event.y_root - self.winfo_rooty())

        closest_index = 0
        min_distance = float('inf')

        for i, item in enumerate(self.items):
            item_y = item.winfo_y()
            distance = abs(y - (item_y + item.winfo_height()/2))
            if distance < min_distance:
                min_distance = distance
                closest_index = i

        if self.placeholder:
            self.placeholder.pack_forget()
            try:
                if closest_index < len(self.items):
                    self.placeholder.pack(before=self.items[closest_index], fill="x", pady=2)
                else:
                    self.placeholder.pack(fill="x", pady=2)
            except:
                self.placeholder.pack(fill="x", pady=2)

    def get_placeholder_index(self):
        if not self.placeholder:
            return len(self.items)

        for i, item in enumerate(self.items):
            if item.winfo_y() > self.placeholder.winfo_y():
                return i
        return len(self.items)

    def reorder_items(self):
        for item in self.items:
            item.pack_forget()

        for item in self.items:
            item.pack(fill="x", pady=2)

class PDFMergerApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("PDF Fusion")
        self.root.geometry("800x600")
        self.current_directory = ""

        self.create_widgets()
        self.root.mainloop()

    def create_widgets(self):
        # Configure grid layout
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # Directory selection
        self.dir_frame = ctk.CTkFrame(self.root)
        self.dir_frame.grid(row=0, column=0, padx=20, pady=10, sticky="ew")

        self.btn_select = ctk.CTkButton(
            self.dir_frame,
            text="Select Folder",
            command=self.select_directory,
            width=120,
            corner_radius=8
        )
        self.btn_select.pack(side="left", padx=5)

        self.lbl_directory = ctk.CTkLabel(
            self.dir_frame,
            text="No folder selected",
            anchor="w"
        )
        self.lbl_directory.pack(side="left", fill="x", expand=True, padx=5)

        # File list
        self.file_list = DraggablePDFList(self.root, height=400)
        self.file_list.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")

        # Merge button
        self.btn_merge = ctk.CTkButton(
            self.root,
            text="Merge PDFs",
            command=self.merge_files,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#C53F3F",  # Red color
            hover_color="#A02222"  # Darker red
        )
        self.btn_merge.grid(row=2, column=0, padx=20, pady=20, sticky="ew")

    def select_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.current_directory = directory
            self.lbl_directory.configure(text=directory)
            self.load_pdfs()

    def load_pdfs(self):
        self.file_list.clear_items()
        try:
            pdf_files = [f for f in os.listdir(self.current_directory)
                        if f.lower().endswith('.pdf')]
            pdf_files.sort()
            for pdf in pdf_files:
                self.file_list.add_item(pdf)
        except Exception as e:
            messagebox.showerror("Error", f"Directory access error: {str(e)}")

    def merge_files(self):
        if not self.current_directory:
            messagebox.showerror("Error", "Please select a directory first")
            return

        if not self.file_list.items:
            messagebox.showerror("Error", "No PDF files to merge")
            return

        # Create output directory
        output_dir = os.path.join(self.current_directory, "Merged")
        try:
            os.makedirs(output_dir, exist_ok=True)
        except OSError as e:
            messagebox.showerror("Error", f"Could not create output directory: {str(e)}")
            return

        # Generate output filename
        dir_name = os.path.basename(self.current_directory)
        output_file = f"{dir_name}.pdf"
        output_path = os.path.join(output_dir, output_file)

        # Confirm overwrite
        if os.path.exists(output_path):
            if not messagebox.askyesno("Confirm Overwrite",
                                      "Output file already exists. Overwrite?"):
                return

        # Merge PDFs
        merger = PdfMerger()
        try:
            for item in self.file_list.items:
                file_path = os.path.join(self.current_directory, item.text)
                merger.append(file_path)

            merger.write(output_path)
            merger.close()
            messagebox.showinfo("Success",
                f"PDFs merged successfully!\n\nSaved to:\n{output_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Merge failed: {str(e)}")
        finally:
            if merger:
                merger.close()
def handle_context_menu_launch():
    if len(sys.argv) > 1:
        # Get the directory path from context menu
        selected_path = sys.argv[1]
        if os.path.isdir(selected_path):
            # Automatically load the directory
            app.current_directory = selected_path
            app.lbl_directory.configure(text=selected_path)
            app.load_pdfs()

# In your PDFMergerApp class __init__:
if __name__ == "__main__":
    app = PDFMergerApp()
    handle_context_menu_launch()
    app.root.mainloop()