from win32com.client import Dispatch
e3DbeApplication = Dispatch( "CT.DbeApplication" )
e3DbeJob = e3DbeApplication.CreateDbeJobObject()
e3DbeSymbol = e3DbeJob.CreateDbeSymbolObject()
e3DbeGraphic = e3DbeJob.CreateDbeGraphObject()
e3DbeApplication.ClearOutputWindow()
color = 128        #new color to apply; in this case 0, 218, 85 (greenish)
symbolId = e3DbeJob.GetActiveSymbolId()        #get current symbol
if symbolId < 0:
    e3DbeApplication.PutWarning( 0, "No open symbol" )
else:
    e3DbeSymbol.SetId( symbolId )
    symbolName = e3DbeSymbol.GetName()

    graphicCount, graphicIds = e3DbeSymbol.GetGraphicIds()        #get graphic ids of symbol
    if graphicCount == 0:
        message = "Symbol {} has no graphics".format( symbolName )
        e3DbeApplication.PutWarning( 0, message, symbolId )
    else:
        for graphicIndex in range( 1, graphicCount + 1 ):

            graphicId = e3DbeGraphic.SetId( graphicIds[graphicIndex] )
            result = e3DbeGraphic.SetHatchColour( color )            
            if result == 0:
                message = "Hatch color of graphic {} of symbol {} ( {} ) could not be set".format( graphicId, symbolName, symbolId )
                e3DbeApplication.PutError( 0, message, graphicId )
            else:
                message = "Hatch color of graphic {} of symbol {} ( {} ) set to greenish hue".format( graphicId, symbolName, symbolId )
                e3DbeApplication.PutInfo( 0, message, graphicId )

del e3DbeGraphic
del e3DbeSymbol
del e3DbeJob
del e3DbeApplication