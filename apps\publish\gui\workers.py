"""
Background worker classes for non-blocking GUI operations.

This module provides base classes and implementations for running
long-running operations in background threads.
"""

import threading
import time
import logging
from abc import ABC, abstractmethod
from typing import Optional, Callable, Any, Dict
from .communication import (
    WorkerStatus, ProgressUpdate, WorkerResult, 
    ThreadSafeQueue, EventBus
)


class BackgroundWorker(ABC):
    """
    Abstract base class for background worker threads.
    
    Provides common functionality for progress reporting, cancellation,
    and thread-safe communication with the GUI.
    """
    
    def __init__(self, name: str = "BackgroundWorker"):
        """
        Initialize the background worker.
        
        Args:
            name: Name of the worker for logging and identification
        """
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Thread management
        self._thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._status = WorkerStatus.IDLE
        self._status_lock = threading.Lock()
        
        # Communication
        self._progress_queue = ThreadSafeQueue(maxsize=100)
        self._result_queue = ThreadSafeQueue(maxsize=10)
        self._event_bus = EventBus()
        
        # Progress tracking
        self._current_progress = ProgressUpdate(0, 100, "Initializing", "Setup")
        self._start_time: Optional[float] = None
        
        # Callbacks
        self._progress_callback: Optional[Callable[[ProgressUpdate], None]] = None
        self._status_callback: Optional[Callable[[WorkerStatus], None]] = None
        self._result_callback: Optional[Callable[[WorkerResult], None]] = None
    
    @abstractmethod
    def _do_work(self) -> WorkerResult:
        """
        Perform the actual work in the background thread.
        
        This method should be implemented by subclasses to define
        the specific work to be performed.
        
        Returns:
            WorkerResult containing the operation results
        """
        pass
    
    def start(self) -> bool:
        """
        Start the background worker.
        
        Returns:
            True if worker was started, False if already running
        """
        with self._status_lock:
            if self._status != WorkerStatus.IDLE:
                self.logger.warning(f"Worker {self.name} is already running")
                return False
            
            self._status = WorkerStatus.RUNNING
            self._stop_event.clear()
            self._start_time = time.time()
        
        # Create and start thread
        self._thread = threading.Thread(target=self._run, name=f"{self.name}Thread")
        self._thread.daemon = True
        self._thread.start()
        
        self.logger.info(f"Started background worker: {self.name}")
        self._notify_status_change(WorkerStatus.RUNNING)
        return True
    
    def cancel(self, timeout: float = 5.0) -> bool:
        """
        Cancel the background worker.
        
        Args:
            timeout: Maximum time to wait for cancellation
            
        Returns:
            True if worker was cancelled, False if timeout occurred
        """
        self.logger.info(f"Cancelling worker: {self.name}")
        self._stop_event.set()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=timeout)
            
            if self._thread.is_alive():
                self.logger.warning(f"Worker {self.name} did not stop within timeout")
                return False
        
        with self._status_lock:
            if self._status == WorkerStatus.RUNNING:
                self._status = WorkerStatus.CANCELLED
                self._notify_status_change(WorkerStatus.CANCELLED)
        
        self.logger.info(f"Worker {self.name} cancelled successfully")
        return True
    
    def is_running(self) -> bool:
        """Check if the worker is currently running."""
        with self._status_lock:
            return self._status == WorkerStatus.RUNNING
    
    def get_status(self) -> WorkerStatus:
        """Get the current worker status."""
        with self._status_lock:
            return self._status
    
    def get_progress(self) -> ProgressUpdate:
        """Get the current progress information."""
        return self._current_progress
    
    def get_result(self, timeout: float = 0.1) -> Optional[WorkerResult]:
        """
        Get the worker result if available.
        
        Args:
            timeout: Maximum time to wait for result
            
        Returns:
            WorkerResult if available, None otherwise
        """
        return self._result_queue.get(timeout=timeout)
    
    def set_progress_callback(self, callback: Callable[[ProgressUpdate], None]):
        """Set callback for progress updates."""
        self._progress_callback = callback
    
    def set_status_callback(self, callback: Callable[[WorkerStatus], None]):
        """Set callback for status changes."""
        self._status_callback = callback
    
    def set_result_callback(self, callback: Callable[[WorkerResult], None]):
        """Set callback for operation results."""
        self._result_callback = callback
    
    def _run(self):
        """Main worker thread execution method."""
        try:
            self.logger.debug(f"Worker {self.name} starting execution")

            # Perform the work
            result = self._do_work()

            # Check if we were cancelled
            if self._stop_event.is_set():
                result.success = False
                result.error_message = "Operation was cancelled"
                self._set_status(WorkerStatus.CANCELLED)
            else:
                self._set_status(WorkerStatus.COMPLETED)

            # Send result
            self._result_queue.put(result)
            if self._result_callback:
                self._result_callback(result)

            self.logger.info(f"Worker {self.name} completed successfully")

        except InterruptedError as e:
            self.logger.info(f"Worker {self.name} was cancelled")

            # Create cancellation result
            cancel_result = WorkerResult(
                success=False,
                error_message="Operation was cancelled",
                metadata={"worker_name": self.name, "cancelled": True}
            )

            self._result_queue.put(cancel_result)
            if self._result_callback:
                try:
                    self._result_callback(cancel_result)
                except Exception as callback_error:
                    self.logger.error(f"Error in result callback: {callback_error}")

            self._set_status(WorkerStatus.CANCELLED)

        except Exception as e:
            self.logger.error(f"Error in worker {self.name}: {e}", exc_info=True)

            # Create error result with detailed information
            error_result = WorkerResult(
                success=False,
                error_message=str(e),
                metadata={
                    "exception_type": type(e).__name__,
                    "worker_name": self.name,
                    "error_details": str(e),
                    "traceback": self._get_safe_traceback()
                }
            )

            self._result_queue.put(error_result)
            if self._result_callback:
                try:
                    self._result_callback(error_result)
                except Exception as callback_error:
                    self.logger.error(f"Error in result callback: {callback_error}")

            self._set_status(WorkerStatus.ERROR)

    def _get_safe_traceback(self) -> str:
        """Get a safe traceback string for error reporting."""
        try:
            import traceback
            return traceback.format_exc()
        except Exception:
            return "Traceback not available"
    
    def _set_status(self, status: WorkerStatus):
        """Set the worker status and notify callbacks."""
        with self._status_lock:
            self._status = status
        self._notify_status_change(status)
    
    def _notify_status_change(self, status: WorkerStatus):
        """Notify callbacks of status change."""
        if self._status_callback:
            try:
                self._status_callback(status)
            except Exception as e:
                self.logger.error(f"Error in status callback: {e}")
    
    def _update_progress(self, current: int, total: int, message: str, 
                        step_name: str, eta_seconds: Optional[float] = None,
                        can_cancel: bool = True):
        """
        Update progress information.
        
        Args:
            current: Current progress value
            total: Total progress value
            message: Progress message
            step_name: Name of current step
            eta_seconds: Estimated time remaining
            can_cancel: Whether operation can be cancelled
        """
        if self._stop_event.is_set():
            return
        
        progress = ProgressUpdate(
            current=current,
            total=total,
            message=message,
            step_name=step_name,
            eta_seconds=eta_seconds,
            can_cancel=can_cancel
        )
        
        self._current_progress = progress
        self._progress_queue.put(progress)
        
        if self._progress_callback:
            try:
                self._progress_callback(progress)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")
    
    def _check_cancellation(self):
        """
        Check if cancellation was requested.
        
        Raises:
            InterruptedError: If cancellation was requested
        """
        if self._stop_event.is_set():
            raise InterruptedError("Operation was cancelled")
    
    def _calculate_eta(self, current: int, total: int) -> Optional[float]:
        """
        Calculate estimated time remaining.
        
        Args:
            current: Current progress value
            total: Total progress value
            
        Returns:
            Estimated seconds remaining or None
        """
        if not self._start_time or current <= 0:
            return None
        
        elapsed = time.time() - self._start_time
        rate = current / elapsed
        
        if rate <= 0:
            return None
        
        remaining = total - current
        return remaining / rate


class PublishWorker(BackgroundWorker):
    """
    Background worker for publishing operations.

    NOTE: Due to COM threading limitations with E3 Series, this worker
    is currently disabled. Publishing operations will run synchronously
    on the main thread to avoid COM marshalling errors.
    """

    def __init__(self, publish_service, project_data, publish_config, series_count: int = 1):
        """
        Initialize the publish worker.

        Args:
            publish_service: PublishService instance
            project_data: Project data to publish
            publish_config: Publishing configuration
            series_count: Number of projects in series (1 for single project)
        """
        super().__init__(name="PublishWorker")

        self.publish_service = publish_service
        self.project_data = project_data
        self.publish_config = publish_config
        self.series_count = series_count
        self.is_series = series_count > 1

        # Progress tracking
        self._total_steps = self._calculate_total_steps()
        self._current_step = 0

        # COM threading flag
        self._com_initialized = False

    def _calculate_total_steps(self) -> int:
        """Calculate total number of steps for progress tracking."""
        # Base steps per project
        steps_per_project = 7  # Based on PublishService steps

        if self.is_series:
            # For series: setup + (steps per project * count) + cleanup
            return 1 + (steps_per_project * self.series_count) + 1
        else:
            # For single project: just the project steps
            return steps_per_project

    def _do_work(self) -> WorkerResult:
        """Perform the publishing work."""
        try:
            if self.is_series:
                return self._publish_series()
            else:
                return self._publish_single()

        except InterruptedError:
            # Operation was cancelled
            self._set_status(WorkerStatus.CANCELLED)
            return WorkerResult(
                success=False,
                error_message="Operation was cancelled by user"
            )
        except Exception as e:
            self.logger.error(f"Error in publish worker: {e}", exc_info=True)
            return WorkerResult(
                success=False,
                error_message=str(e),
                metadata={"exception_type": type(e).__name__}
            )

    def _publish_single(self) -> WorkerResult:
        """Publish a single project."""
        self._update_progress(0, self._total_steps, "Starting single project publish", "Initialize")
        self._check_cancellation()

        try:
            # Use the existing publish service
            result = self.publish_service.publish_project(self.project_data, self.publish_config)

            # Convert PublishResult to WorkerResult
            worker_result = WorkerResult(
                success=result.success,
                result_data=result,
                error_message="; ".join(result.errors) if result.errors else None,
                warnings=result.warnings,
                metadata={
                    "serial_number": result.serial_number,
                    "output_path": result.output_path,
                    "steps_completed": result.steps_completed
                }
            )

            # Final progress update
            if result.success:
                self._update_progress(
                    self._total_steps, self._total_steps,
                    f"Project {result.serial_number} published successfully",
                    "Complete"
                )
            else:
                self._update_progress(
                    self._total_steps, self._total_steps,
                    f"Project {result.serial_number} failed",
                    "Error"
                )

            return worker_result

        except Exception as e:
            self.logger.error(f"Error publishing single project: {e}")
            raise

    def _publish_series(self) -> WorkerResult:
        """Publish a series of projects."""
        self._update_progress(0, self._total_steps, "Starting series publish", "Initialize")
        self._check_cancellation()

        try:
            # Use the existing publish service
            results = self.publish_service.publish_series(
                self.project_data,
                self.publish_config,
                self.series_count
            )

            # Aggregate results
            successful_count = sum(1 for r in results if r.success)
            all_errors = []
            all_warnings = []

            for result in results:
                if result.errors:
                    all_errors.extend(result.errors)
                if result.warnings:
                    all_warnings.extend(result.warnings)

            overall_success = successful_count == len(results)

            worker_result = WorkerResult(
                success=overall_success,
                result_data=results,
                error_message="; ".join(all_errors) if all_errors else None,
                warnings=all_warnings,
                metadata={
                    "series_count": self.series_count,
                    "successful_count": successful_count,
                    "failed_count": len(results) - successful_count,
                    "results": results
                }
            )

            # Final progress update
            if overall_success:
                self._update_progress(
                    self._total_steps, self._total_steps,
                    f"Series of {self.series_count} projects completed successfully",
                    "Complete"
                )
            else:
                self._update_progress(
                    self._total_steps, self._total_steps,
                    f"Series completed with {len(results) - successful_count} failures",
                    "Complete with Errors"
                )

            return worker_result

        except Exception as e:
            self.logger.error(f"Error publishing series: {e}")
            raise
