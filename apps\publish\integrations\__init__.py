"""
Integrations Module - External System Interfaces.

This module contains integration classes that provide clean, well-defined interfaces
to external systems used by the publishing workflow. Each integration encapsulates
the complexity of external system communication and provides a consistent API.

The integrations follow the Adapter pattern, wrapping external APIs with:
- Consistent error handling and exception translation
- Resource management and cleanup
- Logging and monitoring capabilities
- Timeout and retry logic where appropriate
- Input validation and sanitization

Available Integrations:
    - **E3Client**: E3 Series COM API wrapper with resource management
    - **ReportGeneratorClient**: Integration with report generation tools

Design Principles:
    - Adapter Pattern: Wrap external APIs with consistent interfaces
    - Resource Management: Proper cleanup of external resources (COM objects, processes)
    - Error Translation: Convert external errors to application-specific exceptions
    - Logging: Comprehensive logging for debugging external system issues
    - Timeout Handling: Prevent hanging on external system failures
    - Context Managers: Use context managers for automatic resource cleanup

Usage Example:
    ```python
    from apps.publish.integrations import E3Client, ReportGeneratorClient
    
    # Use E3 client with automatic resource cleanup
    with E3Client() as e3:
        title_block = e3.read_title_block_data()
        e3.update_attributes(project_data)
        e3.export_pdf(output_path)
    
    # Use report generator
    report_client = ReportGeneratorClient()
    bom_results = report_client.update_bom_reports(e3_process_id)
    ```

Resource Management:
    All integrations implement proper resource management:
    - COM objects are properly released
    - External processes are terminated cleanly
    - File handles are closed
    - Network connections are cleaned up
    - Context managers ensure cleanup even on exceptions

Error Handling:
    Each integration defines specific exception types:
    - ConnectionError: Connection/initialization failures
    - OperationError: Operation execution failures
    - ValidationError: Input validation failures
    - TimeoutError: Operation timeout failures

Performance Considerations:
    - Connection pooling where applicable
    - Lazy initialization of expensive resources
    - Caching of frequently accessed data
    - Asynchronous operations for long-running tasks
    - Progress reporting for user feedback

Security Considerations:
    - Input sanitization to prevent injection attacks
    - Secure handling of credentials and sensitive data
    - Validation of file paths to prevent directory traversal
    - Proper error messages that don't leak sensitive information

Testing:
    Integrations are designed for testability:
    - Mock-friendly interfaces
    - Dependency injection support
    - Clear separation of concerns
    - Comprehensive error condition coverage

Author: E3 Automation Team
"""

from .e3_client import (
    E3Client,
    E3ConnectionError,
    E3OperationError,
    E3ValidationError,
    E3TimeoutError
)

from .report_generator import (
    ReportGeneratorClient,
    ReportGeneratorError,
    ReportGeneratorValidationError,
    ReportGeneratorTimeoutError
)

from .wire_core_sync import (
    WireCoreSyncClient,
    WireCoreSyncError
)

__all__ = [
    # E3 Series integration
    'E3Client',
    'E3ConnectionError',
    'E3OperationError',
    'E3ValidationError',
    'E3TimeoutError',

    # Report Generator integration
    'ReportGeneratorClient',
    'ReportGeneratorError',
    'ReportGeneratorValidationError',
    'ReportGeneratorTimeoutError',

    # Wire Core Sync integration
    'WireCoreSyncClient',
    'WireCoreSyncError',
]