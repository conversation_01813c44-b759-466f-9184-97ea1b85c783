"""
PDF Printing External Tools Module

This module provides functions for printing PDF files using external tools.
"""

import os
import sys
import logging
import subprocess
import tempfile
import urllib.request
import zipfile
import shutil
from typing import Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_printing_engine import PrinterProfile, PAPER_LETTER, PAPER_TABLOID
from lib.pdf_printing_engine import ORIENTATION_PORTRAIT, ORIENTATION_LANDSCAPE
from lib.pdf_printing_engine import DUPLEX_SIMPLEX, DUPLEX_VERTICAL, DUPLEX_HORIZONTAL
from lib.pdf_printing_engine import COLOR_MONOCHROME, COLOR_COLOR

def get_pdftopdf_path() -> Optional[str]:
    """
    Get the path to PDFtoPrinter executable.
    
    Returns:
        Optional[str]: The path to PDFtoPrinter executable, or None if not found
    """
    # Check common installation paths
    paths = [
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin", "PDFtoPrinter.exe")
    ]
    
    for path in paths:
        if os.path.exists(path):
            return path
    
    return None

def download_pdftopdf() -> Optional[str]:
    """
    Download and install PDFtoPrinter to the bin directory.
    
    Returns:
        Optional[str]: The path to PDFtoPrinter executable, or None if download failed
    """
    try:
        # Create bin directory if it doesn't exist
        bin_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin")
        os.makedirs(bin_dir, exist_ok=True)
        
        # Download PDFtoPrinter
        logging.info("Downloading PDFtoPrinter...")
        download_url = "https://www.columbia.edu/~em36/PDFtoPrinter.exe"
        exe_path = os.path.join(bin_dir, "PDFtoPrinter.exe")
        
        urllib.request.urlretrieve(download_url, exe_path)
        
        if os.path.exists(exe_path):
            logging.info(f"PDFtoPrinter installed to {exe_path}")
            return exe_path
        else:
            logging.error("Failed to install PDFtoPrinter")
            return None
    except Exception as e:
        logging.error(f"Error downloading PDFtoPrinter: {e}")
        return None

def ensure_pdftopdf() -> Optional[str]:
    """
    Ensure PDFtoPrinter is installed and return the path to the executable.
    
    Returns:
        Optional[str]: The path to PDFtoPrinter executable, or None if not available
    """
    pdftopdf_path = get_pdftopdf_path()
    if pdftopdf_path:
        return pdftopdf_path
    
    # Try to download PDFtoPrinter
    return download_pdftopdf()

def print_pdf_with_pdftopdf(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using PDFtoPrinter.
    
    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure PDFtoPrinter is installed
        pdftopdf_path = ensure_pdftopdf()
        if not pdftopdf_path:
            logging.warning("PDFtoPrinter not found and could not be installed")
            return False
        
        logging.info(f"Printing {pdf_path} with PDFtoPrinter")
        logging.info(f"Using printer: {profile.printer_name}")
        
        # Build the command
        cmd = [
            pdftopdf_path,
            pdf_path,
            profile.printer_name
        ]
        
        # Add paper size parameter
        if profile.paper_size == PAPER_LETTER:
            cmd.append("letter")
        elif profile.paper_size == PAPER_TABLOID:
            cmd.append("tabloid")
        
        # Add orientation parameter
        if profile.orientation == ORIENTATION_PORTRAIT:
            cmd.append("portrait")
        elif profile.orientation == ORIENTATION_LANDSCAPE:
            cmd.append("landscape")
        
        # Add duplex parameter
        if profile.duplex == DUPLEX_SIMPLEX:
            cmd.append("simplex")
        elif profile.duplex == DUPLEX_VERTICAL:
            cmd.append("duplex")
        elif profile.duplex == DUPLEX_HORIZONTAL:
            cmd.append("duplexshort")
        
        # Log the command
        logging.info(f"PDFtoPrinter command: {' '.join(cmd)}")
        
        # Execute the command
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # Log the output
        if result.stdout:
            logging.info(f"PDFtoPrinter output: {result.stdout}")
        if result.stderr:
            logging.warning(f"PDFtoPrinter error: {result.stderr}")
        
        # Wait for the print job to be processed
        import time
        time.sleep(3)
        
        return True
    except Exception as e:
        logging.error(f"Error printing with PDFtoPrinter: {e}")
        return False
