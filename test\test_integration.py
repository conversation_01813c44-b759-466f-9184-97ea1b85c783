"""
Integration tests for the dependency injection system.
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from apps.publish_3 import PublishApplication
from apps.publish.container import ServiceContainer


class TestDependencyInjectionIntegration(unittest.TestCase):
    """Integration tests for the dependency injection system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.app = None
        
    def tearDown(self):
        """Clean up after tests."""
        if self.app and hasattr(self.app, 'container') and self.app.container:
            self.app.container.dispose()
            
    def test_application_initialization(self):
        """Test that the application can be initialized."""
        self.app = PublishApplication()
        self.assertIsNotNone(self.app)
        
    def test_logging_setup(self):
        """Test that logging can be set up."""
        self.app = PublishApplication()
        
        # This should not raise an exception
        self.app.setup_logging()
        self.assertIsNotNone(self.app.logger)
        
    def test_container_initialization(self):
        """Test that the service container can be initialized."""
        self.app = PublishApplication()
        self.app.setup_logging()
        
        # This should not raise an exception
        self.app.initialize_container()
        self.assertIsNotNone(self.app.container)
        self.assertIsInstance(self.app.container, ServiceContainer)
        
    @patch('customtkinter.CTk')
    def test_gui_creation_without_mainloop(self, mock_ctk):
        """Test that GUI can be created without running mainloop."""
        # Mock the CTk class to avoid actually creating GUI
        mock_root = Mock()
        mock_ctk.return_value = mock_root
        
        self.app = PublishApplication()
        self.app.setup_logging()
        self.app.initialize_container()
        
        # This should not raise an exception
        try:
            self.app.create_gui()
            self.assertIsNotNone(self.app.root)
            self.assertIsNotNone(self.app.main_window)
        except Exception as e:
            # GUI creation might fail due to missing dependencies
            # This is acceptable for this integration test
            error_str = str(e).lower()
            self.assertTrue(any(keyword in error_str for keyword in ["gui", "window", "widget", "service", "mock", "operand"]))
            
    def test_frozen_environment_handling(self):
        """Test handling of frozen environment."""
        self.app = PublishApplication()
        
        # This should not raise an exception
        self.app.handle_frozen_environment()
        
    def test_cleanup(self):
        """Test application cleanup."""
        self.app = PublishApplication()
        self.app.setup_logging()
        self.app.initialize_container()
        
        # This should not raise an exception
        self.app.cleanup()


if __name__ == '__main__':
    unittest.main()