"""
Publish Package - E3 Series Project Publishing System.

This package provides a comprehensive, modular solution for publishing E3 Series CAD projects.
It replaces the monolithic publish_3.py script with a well-structured, maintainable architecture
that follows software engineering best practices.

Key Features:
    - Modular architecture with clear separation of concerns
    - Comprehensive error handling and logging
    - Dependency injection for testability
    - Support for single and series publishing
    - Integration with E3 Series COM API
    - PDF and DXF export capabilities
    - Manual creation and BOM report generation
    - Configurable publishing workflows

Architecture:
    The package follows a layered architecture pattern:
    
    - **GUI Layer** (gui/): User interface components and widgets
    - **Services Layer** (services/): Business logic and workflow orchestration
    - **Models Layer** (models/): Data structures and validation
    - **Integrations Layer** (integrations/): External system interfaces
    - **Utils Layer** (utils/): Utility functions and helpers
    - **Config Layer** (config/): Configuration management

Usage Example:
    ```python
    from apps.publish.container import ServiceContainer
    from apps.publish.models.project_data import ProjectData
    from apps.publish.services.publish_service import PublishConfig
    
    # Initialize services
    container = ServiceContainer()
    publish_service = container.get_service(PublishService)
    
    # Create project data
    project_data = ProjectData(
        gss_parent="GSS123",
        serial_number="001",
        customer="Example Corp"
    )
    
    # Configure publishing
    config = PublishConfig()
    config.output_base_path = "/path/to/output"
    
    # Publish project
    result = publish_service.publish_project(project_data, config)
    ```

Exception Hierarchy:
    - PublishError: Base exception for all publishing operations
    - ValidationError: Data validation failures
    - E3ConnectionError: E3 Series integration issues
    - ExportError: Export operation failures
    - ConfigurationError: Configuration management issues
    - FileOperationError: File system operation failures

Author: E3 Automation Team
Version: 3.0.0
License: Internal Use Only
"""

__version__ = "3.0.0"
__author__ = "E3 Automation Team"

# Import main classes for convenient access
try:
    from .exceptions import (
        PublishError,
        ValidationError,
        E3ConnectionError,
        ExportError,
        ConfigurationError,
        FileOperationError,
        ServiceError,
        IntegrationError,
        GUIError
    )

    from .models.project_data import ProjectData, TitleBlockData, ValidationResult
    from .services.publish_service import PublishService, PublishConfig, PublishResult
    from .container import ServiceContainer
except ImportError:
    # Handle import errors gracefully during development
    pass

# Package-level constants - loaded from configuration
def _load_constants():
    """Load constants from configuration with fallbacks."""
    try:
        from .config.configuration_manager import ConfigurationManager
        config_manager = ConfigurationManager()
        config = config_manager.load_app_config()

        export_config = config.get('publishing', {}).get('export', {})
        app_config = config.get('application', {})

        return {
            'DEFAULT_EXPORT_FORMATS': export_config.get('default_formats', ['pdf', 'dxf']),
            'DEFAULT_TIMEOUT_SECONDS': app_config.get('timeout_seconds', 300),
            'MAX_SERIES_COUNT': app_config.get('max_series_count', 1000)
        }
    except:
        # Fallback to hardcoded values if configuration fails
        return {
            'DEFAULT_EXPORT_FORMATS': ['pdf', 'dxf'],
            'DEFAULT_TIMEOUT_SECONDS': 300,
            'MAX_SERIES_COUNT': 1000
        }

# Load constants from configuration
_constants = _load_constants()
DEFAULT_EXPORT_FORMATS = _constants['DEFAULT_EXPORT_FORMATS']
DEFAULT_TIMEOUT_SECONDS = _constants['DEFAULT_TIMEOUT_SECONDS']
MAX_SERIES_COUNT = _constants['MAX_SERIES_COUNT']

# Public API
__all__ = [
    # Core classes
    'ServiceContainer',
    'PublishService',
    'ProjectData',
    'TitleBlockData',
    'PublishConfig',
    'PublishResult',
    'ValidationResult',
    
    # Exceptions
    'PublishError',
    'ValidationError',
    'E3ConnectionError',
    'ExportError',
    'ConfigurationError',
    'FileOperationError',
    'ServiceError',
    'IntegrationError',
    'GUIError',
    
    # Constants
    'DEFAULT_EXPORT_FORMATS',
    'DEFAULT_TIMEOUT_SECONDS',
    'MAX_SERIES_COUNT',
]