"""
Unit tests for ExportService.

This module contains tests for the ExportService class functionality
including PDF and DXF export operations.
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os
import tempfile
import shutil

# Add the parent directory to the path to import from apps
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.services.export_service import ExportService, ExportResult, ExportError
from apps.publish.integrations.e3_client import E3ConnectionError, E3OperationError


class MockProjectData:
    """Mock project data for testing."""
    
    def __init__(self, gss_parent="TEST123", serial_number="001"):
        self.gss_parent = gss_parent
        self.serial_number = serial_number
        self.customer = "Test Customer"
        self.location = "Test Location"
        self.title = "Test Title"
        self.sales_order = "SO123"
        self.model = "TEST-MODEL"


class TestExportService(unittest.TestCase):
    """Test cases for ExportService class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_e3_client = Mock()
        self.service = ExportService(self.mock_e3_client)
        self.project_data = MockProjectData()
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_init_with_client(self):
        """Test ExportService initialization with E3 client."""
        service = ExportService(self.mock_e3_client)
        self.assertEqual(service.e3_client, self.mock_e3_client)
    
    def test_init_without_client(self):
        """Test ExportService initialization without E3 client."""
        service = ExportService()
        self.assertIsNone(service.e3_client)
    
    def test_export_to_pdf_success(self):
        """Test successful PDF export."""
        output_path = os.path.join(self.temp_dir, "test.pdf")
        self.mock_e3_client.export_pdf.return_value = True
        
        result = self.service.export_to_pdf(self.project_data, output_path)
        
        self.assertTrue(result)
        self.mock_e3_client.export_pdf.assert_called_once_with(output_path)
    
    def test_export_to_pdf_no_client(self):
        """Test PDF export without E3 client."""
        service = ExportService()  # No client
        output_path = os.path.join(self.temp_dir, "test.pdf")
        
        with self.assertRaises(ExportError) as context:
            service.export_to_pdf(self.project_data, output_path)
        
        self.assertIn("E3 client not available", str(context.exception))
    
    def test_export_to_pdf_no_project_data(self):
        """Test PDF export without project data."""
        output_path = os.path.join(self.temp_dir, "test.pdf")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_pdf(None, output_path)
        
        self.assertIn("Project data is required", str(context.exception))
    
    def test_export_to_pdf_e3_connection_error(self):
        """Test PDF export with E3 connection error."""
        output_path = os.path.join(self.temp_dir, "test.pdf")
        self.mock_e3_client.export_pdf.side_effect = E3ConnectionError("Connection failed")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_pdf(self.project_data, output_path)
        
        self.assertIn("E3 connection error", str(context.exception))
    
    def test_export_to_pdf_e3_operation_error(self):
        """Test PDF export with E3 operation error."""
        output_path = os.path.join(self.temp_dir, "test.pdf")
        self.mock_e3_client.export_pdf.side_effect = E3OperationError("Operation failed")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_pdf(self.project_data, output_path)
        
        self.assertIn("E3 operation error", str(context.exception))
    
    def test_export_to_pdf_creates_directory(self):
        """Test that PDF export creates output directory if it doesn't exist."""
        nested_dir = os.path.join(self.temp_dir, "nested", "path")
        output_path = os.path.join(nested_dir, "test.pdf")
        self.mock_e3_client.export_pdf.return_value = True
        
        result = self.service.export_to_pdf(self.project_data, output_path)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(nested_dir))
    
    def test_export_to_dxf_success(self):
        """Test successful DXF export."""
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        expected_files = ["file1.dxf", "file2.dxf"]
        self.mock_e3_client.export_dxf_dataplates.return_value = expected_files
        
        result = self.service.export_to_dxf(self.project_data, output_dir)
        
        self.assertEqual(result, expected_files)
        self.mock_e3_client.export_dxf_dataplates.assert_called_once_with(
            output_dir, self.project_data.gss_parent, self.project_data.serial_number
        )
    
    def test_export_to_dxf_no_client(self):
        """Test DXF export without E3 client."""
        service = ExportService()  # No client
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        
        with self.assertRaises(ExportError) as context:
            service.export_to_dxf(self.project_data, output_dir)
        
        self.assertIn("E3 client not available", str(context.exception))
    
    def test_export_to_dxf_no_project_data(self):
        """Test DXF export without project data."""
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_dxf(None, output_dir)
        
        self.assertIn("Project data is required", str(context.exception))
    
    def test_export_to_dxf_missing_gss_parent(self):
        """Test DXF export with missing GSS parent."""
        project_data = MockProjectData()
        project_data.gss_parent = None
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_dxf(project_data, output_dir)
        
        self.assertIn("GSS parent number is required", str(context.exception))
    
    def test_export_to_dxf_missing_serial_number(self):
        """Test DXF export with missing serial number."""
        project_data = MockProjectData()
        project_data.serial_number = ""
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        
        with self.assertRaises(ExportError) as context:
            self.service.export_to_dxf(project_data, output_dir)
        
        self.assertIn("Serial number is required", str(context.exception))
    
    def test_export_to_dxf_no_files(self):
        """Test DXF export when no files are exported."""
        output_dir = os.path.join(self.temp_dir, "dxf_output")
        self.mock_e3_client.export_dxf_dataplates.return_value = []
        
        result = self.service.export_to_dxf(self.project_data, output_dir)
        
        self.assertEqual(result, [])
    
    def test_export_to_dxf_creates_directory(self):
        """Test that DXF export creates output directory if it doesn't exist."""
        nested_dir = os.path.join(self.temp_dir, "nested", "dxf_path")
        self.mock_e3_client.export_dxf_dataplates.return_value = ["test.dxf"]
        
        result = self.service.export_to_dxf(self.project_data, nested_dir)
        
        self.assertEqual(result, ["test.dxf"])
        self.assertTrue(os.path.exists(nested_dir))
    
    def test_export_project_both_formats(self):
        """Test exporting project in both PDF and DXF formats."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_pdf.return_value = True
        self.mock_e3_client.export_dxf_dataplates.return_value = ["test.dxf"]
        
        result = self.service.export_project(self.project_data, output_folder, ['pdf', 'dxf'])
        
        self.assertTrue(result.success)
        self.assertEqual(result.success_count, 2)  # 1 PDF + 1 DXF
        self.assertEqual(len(result.pdf_files), 1)
        self.assertEqual(len(result.dxf_files), 1)
        self.assertEqual(len(result.errors), 0)
    
    def test_export_project_pdf_only(self):
        """Test exporting project in PDF format only."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_pdf.return_value = True
        
        result = self.service.export_project(self.project_data, output_folder, ['pdf'])
        
        self.assertTrue(result.success)
        self.assertEqual(result.success_count, 1)
        self.assertEqual(len(result.pdf_files), 1)
        self.assertEqual(len(result.dxf_files), 0)
    
    def test_export_project_dxf_only(self):
        """Test exporting project in DXF format only."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_dxf_dataplates.return_value = ["test1.dxf", "test2.dxf"]
        
        result = self.service.export_project(self.project_data, output_folder, ['dxf'])
        
        self.assertTrue(result.success)
        self.assertEqual(result.success_count, 2)  # 2 DXF files
        self.assertEqual(len(result.pdf_files), 0)
        self.assertEqual(len(result.dxf_files), 2)
    
    def test_export_project_default_formats(self):
        """Test exporting project with default formats."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_pdf.return_value = True
        self.mock_e3_client.export_dxf_dataplates.return_value = ["test.dxf"]
        
        result = self.service.export_project(self.project_data, output_folder)
        
        self.assertTrue(result.success)
        self.assertEqual(result.success_count, 2)
    
    def test_export_project_with_errors(self):
        """Test exporting project with errors."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_pdf.side_effect = ExportError("PDF failed")
        self.mock_e3_client.export_dxf_dataplates.return_value = ["test.dxf"]
        
        result = self.service.export_project(self.project_data, output_folder, ['pdf', 'dxf'])
        
        self.assertFalse(result.success)
        self.assertEqual(result.success_count, 1)  # Only DXF succeeded
        self.assertEqual(len(result.errors), 1)
        self.assertIn("PDF export error", result.errors[0])
    
    def test_export_project_no_dxf_files(self):
        """Test exporting project when no DXF files are found."""
        output_folder = self.temp_dir
        self.mock_e3_client.export_pdf.return_value = True
        self.mock_e3_client.export_dxf_dataplates.return_value = []
        
        result = self.service.export_project(self.project_data, output_folder, ['pdf', 'dxf'])
        
        self.assertTrue(result.success)  # No errors, just warnings
        self.assertEqual(result.success_count, 1)  # Only PDF
        self.assertEqual(len(result.warnings), 1)
        self.assertIn("No DXF files exported", result.warnings[0])


class TestExportResult(unittest.TestCase):
    """Test cases for ExportResult class."""
    
    def test_init(self):
        """Test ExportResult initialization."""
        result = ExportResult()
        
        self.assertFalse(result.success)
        self.assertEqual(result.success_count, 0)
        self.assertEqual(result.pdf_files, [])
        self.assertEqual(result.dxf_files, [])
        self.assertEqual(result.errors, [])
        self.assertEqual(result.warnings, [])
    
    def test_str_success(self):
        """Test string representation for successful result."""
        result = ExportResult()
        result.success = True
        result.success_count = 3
        
        str_repr = str(result)
        self.assertIn("SUCCESS", str_repr)
        self.assertIn("3 files", str_repr)
        self.assertIn("0 errors", str_repr)
    
    def test_str_failure(self):
        """Test string representation for failed result."""
        result = ExportResult()
        result.success = False
        result.success_count = 1
        result.errors = ["Error 1", "Error 2"]
        
        str_repr = str(result)
        self.assertIn("FAILED", str_repr)
        self.assertIn("1 files", str_repr)
        self.assertIn("2 errors", str_repr)


class TestExportError(unittest.TestCase):
    """Test cases for ExportError class."""
    
    def test_init_basic(self):
        """Test ExportError initialization with basic message."""
        error = ExportError("Test error")
        
        self.assertEqual(error.message, "Test error")
        self.assertIsNone(error.operation)
        self.assertIsNone(error.original_error)
    
    def test_init_with_operation(self):
        """Test ExportError initialization with operation."""
        error = ExportError("Test error", "pdf_export")
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "pdf_export")
        self.assertIsNone(error.original_error)
    
    def test_init_with_original_error(self):
        """Test ExportError initialization with original error."""
        original = ValueError("Original error")
        error = ExportError("Test error", "pdf_export", original)
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "pdf_export")
        self.assertEqual(error.original_error, original)
    
    def test_str_basic(self):
        """Test string representation with basic message."""
        error = ExportError("Test error")
        self.assertEqual(str(error), "Test error")
    
    def test_str_with_operation(self):
        """Test string representation with operation."""
        error = ExportError("Test error", "pdf_export")
        expected = "Export operation 'pdf_export' failed: Test error"
        self.assertEqual(str(error), expected)
    
    def test_str_with_original_error(self):
        """Test string representation with original error."""
        original = ValueError("Original error")
        error = ExportError("Test error", "pdf_export", original)
        expected = "Export operation 'pdf_export' failed: Test error (Original error: Original error)"
        self.assertEqual(str(error), expected)


if __name__ == '__main__':
    unittest.main()