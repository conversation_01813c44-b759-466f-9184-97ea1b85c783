"""
Utils Module - Utility Functions and Helpers.

This module contains utility classes and functions that provide common functionality
used throughout the publishing system. Utilities are designed to be stateless,
reusable, and focused on specific tasks.

The utilities follow functional programming principles where possible:
- Pure functions with no side effects
- Immutable inputs and outputs
- Clear, single-purpose functionality
- Comprehensive error handling
- Extensive testing coverage

Available Utilities:
    - **FileOperations**: File and directory management utilities
    - **SeriesGenerator**: Serial number generation and manipulation
    - **PathUtils**: Path manipulation and validation utilities
    - **DataUtils**: Data transformation and formatting utilities

Design Principles:
    - Single Responsibility: Each utility has a focused purpose
    - Pure Functions: No side effects where possible
    - Error Handling: Comprehensive error checking and reporting
    - Performance: Optimized for common use cases
    - Testability: Easy to unit test in isolation
    - Documentation: Clear usage examples and parameter descriptions

Usage Example:
    ```python
    from apps.publish.utils import FileOperations, SeriesGenerator
    
    # File operations
    file_ops = FileOperations()
    output_path = file_ops.create_serial_folder("/base/path", "GSS123", "001")
    file_ops.save_job_data_json(project_data, output_path)
    
    # Series generation
    series_gen = SeriesGenerator()
    series = series_gen.generate_series("001", 5)  # ["001", "002", "003", "004", "005"]
    next_serial = series_gen.increment_serial("ABC123")  # "ABC124"
    ```

File Operations:
    - Safe directory creation with proper permissions
    - Atomic file operations to prevent corruption
    - Path validation and sanitization
    - Cross-platform path handling
    - Backup and recovery capabilities

Series Generation:
    - Support for numeric serial numbers (001, 002, 003...)
    - Support for alphanumeric serials (ABC001, ABC002...)
    - Support for alpha suffixes (001A, 001B, 001C...)
    - Intelligent format detection
    - Overflow handling and validation

Path Utilities:
    - Cross-platform path normalization
    - Path validation and security checks
    - Relative/absolute path conversion
    - Directory traversal prevention
    - File extension handling

Data Utilities:
    - JSON serialization/deserialization
    - Data format conversion
    - String manipulation and formatting
    - Date/time utilities
    - Validation helpers

Error Handling:
    All utilities use appropriate exception types:
    - FileOperationError: File system operation failures
    - ValidationError: Input validation failures
    - SeriesGeneratorError: Series generation failures

Performance Considerations:
    - Lazy evaluation where appropriate
    - Caching of expensive operations
    - Memory-efficient algorithms
    - Batch operations for multiple items
    - Progress reporting for long operations

Security Features:
    - Path traversal prevention
    - Input sanitization
    - Safe file operations
    - Permission validation
    - Secure temporary file handling

Testing:
    Utilities are extensively tested:
    - Unit tests for all public methods
    - Edge case and error condition testing
    - Performance benchmarking
    - Cross-platform compatibility testing
    - Security vulnerability testing

Author: E3 Automation Team
"""

from .file_operations import FileOperations
from .series_generator import SeriesGenerator, SerialFormat, SeriesGeneratorError

__all__ = [
    'FileOperations',
    'SeriesGenerator',
    'SerialFormat',
    'SeriesGeneratorError',
]