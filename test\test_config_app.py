"""
Tests for the config_app module.

This module contains unit tests for the configuration application in the applications.config_app module.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
import tkinter as tk
import customtkinter as ctk
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow importing from applications
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module under test
from applications.config_app import ConfigApp

class TestConfigApp(unittest.TestCase):
    """Test case for the config_app module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()

        # Create a mock config file
        self.config_data = {
            "folder_to_monitor": "/path/to/monitor",
            "target_email": "<EMAIL>"
        }
        self.config_file = os.path.join(self.temp_dir, "config.json")
        with open(self.config_file, "w") as f:
            json.dump(self.config_data, f)

        # Create a mock models file
        self.models_data = {
            "Category": {
                "Model": {
                    "template_path": "/path/to/template.dotx",
                    "drawings_path": "/path/to/drawings",
                    "asme_flag": False,
                    "controls_parent": ""
                }
            }
        }
        self.models_file = os.path.join(self.temp_dir, "models.json")
        with open(self.models_file, "w") as f:
            json.dump(self.models_data, f)

        # Create a root window for testing
        self.root = ctk.CTk()

        # Patch the load_config function
        self.load_config_patcher = patch("applications.config_app.load_config")
        self.mock_load_config = self.load_config_patcher.start()

        # Patch the save_config function
        self.save_config_patcher = patch("applications.config_app.save_config")
        self.mock_save_config = self.save_config_patcher.start()

        # Set up the mock load_config function
        def load_config_side_effect(filename):
            if filename == "config.json":
                return self.config_data
            elif filename == "models.json":
                return self.models_data
            else:
                raise FileNotFoundError(f"File not found: {filename}")

        self.mock_load_config.side_effect = load_config_side_effect

        # Set up the mock save_config function
        self.mock_save_config.return_value = True

    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

        # Stop the patchers
        self.load_config_patcher.stop()
        self.save_config_patcher.stop()

        # Destroy the root window
        self.root.destroy()

    def test_init(self):
        """Test initialization of ConfigApp."""
        # Create a ConfigApp instance
        app = ConfigApp(self.root)

        # Check that the app was initialized correctly
        self.assertEqual(app.root, self.root)
        self.assertIsInstance(app.template_path_var, tk.StringVar)
        self.assertIsInstance(app.drawings_path_var, tk.StringVar)
        self.assertIsInstance(app.monitor_path_var, tk.StringVar)
        self.assertIsInstance(app.email_var, tk.StringVar)

    def test_load_existing_config(self):
        """Test loading existing configuration."""
        # Create a ConfigApp instance
        app = ConfigApp(self.root)

        # Check that the configuration was loaded correctly
        self.assertEqual(app.monitor_path_var.get(), self.config_data["folder_to_monitor"])
        self.assertEqual(app.email_var.get(), self.config_data["target_email"])

        # Check that the template and drawings paths were loaded correctly
        template_path = os.path.dirname(self.models_data["Category"]["Model"]["template_path"])
        drawings_path = os.path.dirname(self.models_data["Category"]["Model"]["drawings_path"])
        self.assertEqual(app.template_path_var.get(), template_path)
        self.assertEqual(app.drawings_path_var.get(), drawings_path)

    @patch("applications.config_app.messagebox")
    def test_save_configuration(self, mock_messagebox):
        """Test saving configuration."""
        # Create a ConfigApp instance
        app = ConfigApp(self.root)

        # Set the configuration values
        app.template_path_var.set("/new/template/path")
        app.drawings_path_var.set("/new/drawings/path")
        app.monitor_path_var.set("/new/monitor/path")
        app.email_var.set("<EMAIL>")

        # Mock the update_models_json method
        app.update_models_json = MagicMock()

        # Save the configuration
        app.save_configuration()

        # Check that update_models_json was called with the correct arguments
        app.update_models_json.assert_called_with("/new/template/path", "/new/drawings/path")

        # Check that save_config was called with the correct arguments
        self.mock_save_config.assert_called_with(
            {
                "folder_to_monitor": "/new/monitor/path",
                "target_email": "<EMAIL>"
            },
            "config.json"
        )

        # Check that a success message was shown
        mock_messagebox.showinfo.assert_called_once()

    @patch("applications.config_app.messagebox")
    def test_save_configuration_missing_paths(self, mock_messagebox):
        """Test saving configuration with missing paths."""
        # Create a ConfigApp instance
        app = ConfigApp(self.root)

        # Set the configuration values with missing template path
        app.template_path_var.set("")
        app.drawings_path_var.set("/new/drawings/path")

        # Save the configuration
        app.save_configuration()

        # Check that an error message was shown
        mock_messagebox.showerror.assert_called_once()

        # Check that save_config was not called
        self.mock_save_config.assert_not_called()

    @patch("applications.config_app.os.path.exists")
    @patch("applications.config_app.json.load")
    @patch("builtins.open")
    def test_update_models_json(self, _, mock_json_load, mock_exists):
        """Test updating models.json."""
        # Set up mocks
        mock_exists.return_value = True
        mock_json_load.return_value = self.models_data

        # Mock the CTkFrame initialization to avoid theme manager issues
        with patch('applications.config_app.ctk.CTkFrame'):
            # Create a ConfigApp instance
            app = ConfigApp(self.root)

        # Update models.json
        app.update_models_json("/new/template/path", "/new/drawings/path")

        # Check that save_config was called with the correct arguments
        expected_models = {
            "Category": {
                "Model": {
                    "template_path": "/new/template/path/template.dotx",
                    "drawings_path": "/new/drawings/path/Category/drawings",
                    "asme_flag": False,
                    "controls_parent": ""
                }
            }
        }
        self.mock_save_config.assert_called_with(expected_models, "models.json")

if __name__ == "__main__":
    unittest.main()
