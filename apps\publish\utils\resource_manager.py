"""
Resource management utilities for optimizing COM object cleanup and memory usage.

This module provides context managers and utilities for proper resource management,
particularly for COM objects and external processes.
"""

import gc
import logging
import psutil
import threading
import time
import weakref
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Set, Callable
from dataclasses import dataclass, field


@dataclass
class ResourceStats:
    """Statistics for resource usage monitoring."""
    memory_usage_mb: float = 0.0
    com_objects_active: int = 0
    processes_active: int = 0
    threads_active: int = 0
    timestamp: float = field(default_factory=time.time)


class COMObjectTracker:
    """Tracks COM objects for proper cleanup and monitoring."""
    
    def __init__(self):
        """Initialize COM object tracker."""
        self._objects: Set[weakref.ref] = set()
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def register(self, com_object: Any, name: str = None) -> Any:
        """
        Register a COM object for tracking.
        
        Args:
            com_object: COM object to track
            name: Optional name for debugging
            
        Returns:
            The same COM object (for chaining)
        """
        if com_object is None:
            return com_object
            
        def cleanup_callback(ref):
            with self._lock:
                self._objects.discard(ref)
                
        with self._lock:
            ref = weakref.ref(com_object, cleanup_callback)
            self._objects.add(ref)
            
        self.logger.debug(f"Registered COM object: {name or type(com_object).__name__}")
        return com_object
    
    def cleanup_all(self):
        """Force cleanup of all tracked COM objects."""
        with self._lock:
            active_objects = []
            for ref in list(self._objects):
                obj = ref()
                if obj is not None:
                    active_objects.append(obj)
                    
            # Clear references
            for obj in active_objects:
                try:
                    # Try to release COM object
                    if hasattr(obj, 'Release'):
                        obj.Release()
                    elif hasattr(obj, 'Close'):
                        obj.Close()
                    del obj
                except Exception as e:
                    self.logger.warning(f"Error releasing COM object: {e}")
            
            self._objects.clear()
            
        # Force garbage collection
        gc.collect()
        self.logger.info(f"Cleaned up {len(active_objects)} COM objects")
    
    def get_active_count(self) -> int:
        """Get count of active COM objects."""
        with self._lock:
            active_count = 0
            for ref in list(self._objects):
                if ref() is not None:
                    active_count += 1
                else:
                    self._objects.discard(ref)
            return active_count


class ProcessTracker:
    """Tracks external processes for proper cleanup."""
    
    def __init__(self):
        """Initialize process tracker."""
        self._processes: Dict[int, psutil.Process] = {}
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def register(self, process: psutil.Process, name: str = None) -> psutil.Process:
        """
        Register a process for tracking.
        
        Args:
            process: Process to track
            name: Optional name for debugging
            
        Returns:
            The same process (for chaining)
        """
        if process is None:
            return process
            
        with self._lock:
            self._processes[process.pid] = process
            
        self.logger.debug(f"Registered process: {name or process.name()} (PID: {process.pid})")
        return process
    
    def unregister(self, pid: int):
        """Unregister a process."""
        with self._lock:
            self._processes.pop(pid, None)
    
    def cleanup_all(self, timeout: int = 30):
        """
        Cleanup all tracked processes.
        
        Args:
            timeout: Timeout in seconds for graceful termination
        """
        with self._lock:
            processes = list(self._processes.values())
            self._processes.clear()
            
        if not processes:
            return
            
        self.logger.info(f"Cleaning up {len(processes)} processes")
        
        # Try graceful termination first
        for process in processes:
            try:
                if process.is_running():
                    process.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.logger.debug(f"Process {process.pid} already terminated or access denied: {e}")
        
        # Wait for graceful termination
        start_time = time.time()
        remaining_processes = []
        
        while processes and (time.time() - start_time) < timeout:
            remaining_processes = []
            for process in processes:
                try:
                    if process.is_running():
                        remaining_processes.append(process)
                except psutil.NoSuchProcess:
                    pass  # Process already terminated
            
            processes = remaining_processes
            if processes:
                time.sleep(0.1)
        
        # Force kill remaining processes
        for process in remaining_processes:
            try:
                if process.is_running():
                    self.logger.warning(f"Force killing process {process.pid}")
                    process.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.logger.debug(f"Could not kill process {process.pid}: {e}")
    
    def get_active_count(self) -> int:
        """Get count of active processes."""
        with self._lock:
            active_count = 0
            dead_pids = []
            
            for pid, process in self._processes.items():
                try:
                    if process.is_running():
                        active_count += 1
                    else:
                        dead_pids.append(pid)
                except psutil.NoSuchProcess:
                    dead_pids.append(pid)
            
            # Clean up dead processes
            for pid in dead_pids:
                self._processes.pop(pid, None)
                
            return active_count


class MemoryMonitor:
    """Monitors memory usage and provides optimization suggestions."""
    
    def __init__(self, threshold_mb: float = 500.0):
        """
        Initialize memory monitor.
        
        Args:
            threshold_mb: Memory threshold in MB for warnings
        """
        self.threshold_mb = threshold_mb
        self.logger = logging.getLogger(__name__)
        self._baseline_memory = None
        
    def get_current_usage(self) -> float:
        """
        Get current memory usage in MB.
        
        Returns:
            Memory usage in megabytes
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            self.logger.error(f"Error getting memory usage: {e}")
            return 0.0
    
    def set_baseline(self):
        """Set baseline memory usage for comparison."""
        self._baseline_memory = self.get_current_usage()
        self.logger.debug(f"Set memory baseline: {self._baseline_memory:.1f} MB")
    
    def get_memory_increase(self) -> float:
        """
        Get memory increase since baseline.
        
        Returns:
            Memory increase in MB, or 0 if no baseline set
        """
        if self._baseline_memory is None:
            return 0.0
        return self.get_current_usage() - self._baseline_memory
    
    def check_threshold(self) -> bool:
        """
        Check if memory usage exceeds threshold.
        
        Returns:
            True if threshold exceeded
        """
        current_usage = self.get_current_usage()
        if current_usage > self.threshold_mb:
            self.logger.warning(f"Memory usage ({current_usage:.1f} MB) exceeds threshold ({self.threshold_mb:.1f} MB)")
            return True
        return False
    
    def suggest_cleanup(self) -> List[str]:
        """
        Suggest cleanup actions based on memory usage.
        
        Returns:
            List of suggested cleanup actions
        """
        suggestions = []
        current_usage = self.get_current_usage()
        
        if current_usage > self.threshold_mb:
            suggestions.append("Force garbage collection")
            suggestions.append("Release unused COM objects")
            
        if self._baseline_memory and (current_usage - self._baseline_memory) > 100:
            suggestions.append("Check for memory leaks")
            suggestions.append("Clear temporary data structures")
            
        return suggestions


class ResourceManager:
    """Central resource manager for coordinating cleanup and monitoring."""
    
    def __init__(self, memory_threshold_mb: float = 500.0):
        """
        Initialize resource manager.
        
        Args:
            memory_threshold_mb: Memory threshold for warnings
        """
        self.com_tracker = COMObjectTracker()
        self.process_tracker = ProcessTracker()
        self.memory_monitor = MemoryMonitor(memory_threshold_mb)
        self.logger = logging.getLogger(__name__)
        
        # Set memory baseline
        self.memory_monitor.set_baseline()
        
    def register_com_object(self, com_object: Any, name: str = None) -> Any:
        """Register a COM object for tracking."""
        return self.com_tracker.register(com_object, name)
    
    def register_process(self, process: psutil.Process, name: str = None) -> psutil.Process:
        """Register a process for tracking."""
        return self.process_tracker.register(process, name)
    
    def cleanup_all(self, process_timeout: int = 30):
        """
        Cleanup all tracked resources.
        
        Args:
            process_timeout: Timeout for process cleanup
        """
        self.logger.info("Starting comprehensive resource cleanup")
        
        # Cleanup COM objects first
        self.com_tracker.cleanup_all()
        
        # Cleanup processes
        self.process_tracker.cleanup_all(process_timeout)
        
        # Force garbage collection
        gc.collect()
        
        self.logger.info("Resource cleanup completed")
    
    def get_resource_stats(self) -> ResourceStats:
        """Get current resource usage statistics."""
        return ResourceStats(
            memory_usage_mb=self.memory_monitor.get_current_usage(),
            com_objects_active=self.com_tracker.get_active_count(),
            processes_active=self.process_tracker.get_active_count(),
            threads_active=threading.active_count()
        )
    
    def log_resource_stats(self, prefix: str = "Resource Stats"):
        """Log current resource statistics."""
        stats = self.get_resource_stats()
        self.logger.info(
            f"{prefix}: Memory={stats.memory_usage_mb:.1f}MB, "
            f"COM={stats.com_objects_active}, "
            f"Processes={stats.processes_active}, "
            f"Threads={stats.threads_active}"
        )
    
    def check_and_warn(self) -> bool:
        """
        Check resource usage and warn if thresholds exceeded.
        
        Returns:
            True if any thresholds exceeded
        """
        threshold_exceeded = False
        
        # Check memory threshold
        if self.memory_monitor.check_threshold():
            threshold_exceeded = True
            suggestions = self.memory_monitor.suggest_cleanup()
            self.logger.warning(f"Memory cleanup suggestions: {', '.join(suggestions)}")
        
        # Check for too many COM objects
        com_count = self.com_tracker.get_active_count()
        if com_count > 50:  # Arbitrary threshold
            self.logger.warning(f"High number of active COM objects: {com_count}")
            threshold_exceeded = True
        
        return threshold_exceeded


# Global resource manager instance
_global_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """Get the global resource manager instance."""
    global _global_resource_manager
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    return _global_resource_manager


@contextmanager
def managed_com_object(com_object: Any, name: str = None):
    """
    Context manager for COM objects with automatic cleanup.
    
    Args:
        com_object: COM object to manage
        name: Optional name for debugging
        
    Yields:
        The COM object
    """
    resource_manager = get_resource_manager()
    
    try:
        if com_object is not None:
            resource_manager.register_com_object(com_object, name)
        yield com_object
    finally:
        if com_object is not None:
            try:
                if hasattr(com_object, 'Release'):
                    com_object.Release()
                elif hasattr(com_object, 'Close'):
                    com_object.Close()
                del com_object
            except Exception as e:
                logging.getLogger(__name__).warning(f"Error releasing COM object {name}: {e}")


@contextmanager
def managed_process(process_func: Callable, name: str = None, timeout: int = 30):
    """
    Context manager for external processes with automatic cleanup.
    
    Args:
        process_func: Function that returns a process or subprocess
        name: Optional name for debugging
        timeout: Timeout for process cleanup
        
    Yields:
        The process object
    """
    resource_manager = get_resource_manager()
    process = None
    
    try:
        process = process_func()
        if hasattr(process, 'pid'):
            # Convert to psutil.Process for better management
            if not isinstance(process, psutil.Process):
                process = psutil.Process(process.pid)
            resource_manager.register_process(process, name)
        yield process
    finally:
        if process is not None:
            try:
                if hasattr(process, 'terminate'):
                    process.terminate()
                    try:
                        process.wait(timeout=timeout)
                    except:
                        if hasattr(process, 'kill'):
                            process.kill()
            except Exception as e:
                logging.getLogger(__name__).warning(f"Error terminating process {name}: {e}")


@contextmanager
def resource_monitoring(operation_name: str = "Operation"):
    """
    Context manager for monitoring resource usage during operations.
    
    Args:
        operation_name: Name of the operation being monitored
        
    Yields:
        ResourceManager instance
    """
    resource_manager = get_resource_manager()
    logger = logging.getLogger(__name__)
    
    # Log initial stats
    logger.info(f"Starting {operation_name}")
    resource_manager.log_resource_stats(f"{operation_name} - Initial")
    
    try:
        yield resource_manager
    finally:
        # Log final stats
        resource_manager.log_resource_stats(f"{operation_name} - Final")
        
        # Check for resource issues
        if resource_manager.check_and_warn():
            logger.warning(f"{operation_name} completed with resource warnings")
        
        # Suggest cleanup if memory increased significantly
        memory_increase = resource_manager.memory_monitor.get_memory_increase()
        if memory_increase > 50:  # 50MB increase
            logger.info(f"{operation_name} increased memory by {memory_increase:.1f}MB")


def force_cleanup():
    """Force cleanup of all tracked resources."""
    resource_manager = get_resource_manager()
    resource_manager.cleanup_all()


def optimize_memory():
    """Perform memory optimization operations."""
    logger = logging.getLogger(__name__)
    
    # Force garbage collection
    collected = gc.collect()
    logger.info(f"Garbage collection freed {collected} objects")
    
    # Get resource manager and check stats
    resource_manager = get_resource_manager()
    resource_manager.log_resource_stats("After optimization")