import os
import datetime
import win32com.client
import logging
import json
from pathlib import Path
import sys
import time
import pythoncom
import win32api
import win32con
import subprocess
import psutil
from threading import Timer

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions
try:
    from utils import get_resource_path, get_config_path
except ImportError:
    try:
        from lib.utils import get_resource_path, get_config_path
    except ImportError:
        # Fallback implementation if utils module is not available
        def get_app_dir():
            """Get the application directory, works for both development and PyInstaller"""
            try:
                # PyInstaller creates a temp folder and stores path in _MEIPASS
                base_path = sys._MEIPASS
            except Exception:
                # We're running in a normal Python environment
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            return base_path

        def get_resource_path(relative_path):
            """Get absolute path to resource, works for dev and for PyInstaller"""
            app_dir = get_app_dir()
            return os.path.join(app_dir, relative_path)

        def get_config_path(filename):
            """Get path to a configuration file in the resources/config directory"""
            return get_resource_path(os.path.join('resources', 'config', filename))

# Set up logging with more detailed information
logging.basicConfig(
    filename='nesting_monitor.log',
    level=logging.DEBUG,  # Changed to DEBUG for more detailed logging
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_config():
    """Load configuration from config file"""
    try:
        # First try to load from external config file in the same directory as the executable
        exe_dir = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
        external_config = os.path.join(exe_dir, 'config.json')

        # Try external config first
        if os.path.exists(external_config):
            logging.info(f"Loading external config from {external_config}")
            with open(external_config, 'r') as f:
                return json.load(f)

        # Fall back to bundled config
        logging.info("External config not found, using bundled config")
        config_path = get_config_path('config.json')
        with open(config_path, 'r') as f:
            # Load the bundled config
            config = json.load(f)

            # Save it as external config for future modifications
            try:
                with open(external_config, 'w') as ef:
                    json.dump(config, ef, indent=4)
                logging.info(f"Created external config at {external_config}")
            except Exception as e:
                logging.error(f"Failed to create external config: {e}")

            return config

    except Exception as e:
        logging.error(f"Error loading config file: {e}")
        raise

# For backward compatibility
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    # Check if it's a config file that should be in resources/config
    if relative_path.endswith('.json'):
        return get_config_path(os.path.basename(relative_path))
    return get_resource_path(relative_path)

class NestingMonitor:
    def __init__(self, folder_to_monitor, target_email, last_run_file="last_run_state.json"):
        self.folder_to_monitor = folder_to_monitor
        self.last_run_file = last_run_file
        self.target_email = target_email
        self.outlook = None

    def is_outlook_running(self):
        """Check if Outlook is running"""
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'].lower() == 'outlook.exe':
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        return False

    def start_outlook(self):
        """Start Outlook process"""
        if not self.is_outlook_running():
            logging.debug("Starting Outlook process")
            try:
                # Try to start Outlook using the default installation path
                outlook_path = r"C:\Program Files\Microsoft Office\root\Office16\OUTLOOK.EXE"
                if os.path.exists(outlook_path):
                    subprocess.Popen([outlook_path])
                    time.sleep(10)  # Give Outlook time to start
                    logging.debug("Outlook process started")
                else:
                    logging.error("Outlook executable not found")
                    raise FileNotFoundError("Outlook executable not found")
            except Exception as e:
                logging.error(f"Failed to start Outlook: {str(e)}")
                raise

    def initialize_outlook(self):
        """Initialize Outlook with timeout"""
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            try:
                logging.debug(f"Outlook initialization attempt {attempt + 1}")

                # Ensure Outlook is running
                self.start_outlook()

                # Try to get existing Outlook instance
                try:
                    logging.debug("Attempting to get active Outlook instance")
                    self.outlook = win32com.client.GetActiveObject("Outlook.Application")
                except:
                    logging.debug("Creating new Outlook instance")
                    self.outlook = win32com.client.dynamic.Dispatch("Outlook.Application")

                # Verify connection
                namespace = self.outlook.GetNamespace("MAPI")
                logging.debug("Got MAPI namespace")

                # Basic test of functionality
                inbox = namespace.GetDefaultFolder(6)
                logging.debug(f"Successfully accessed inbox")

                logging.info("Outlook initialized successfully")
                return

            except Exception as e:
                attempt += 1
                logging.error(f"Attempt {attempt} failed: {str(e)}")
                if attempt < max_attempts:
                    logging.debug("Waiting 5 seconds before retry")
                    time.sleep(5)
                else:
                    raise Exception(f"Failed to initialize Outlook after {max_attempts} attempts")

    def __del__(self):
        try:
            if hasattr(self, 'outlook') and self.outlook is not None:
                self.outlook = None
                pythoncom.CoUninitialize()
                logging.debug("COM uninitialized")
        except:
            pass

    def load_last_run_state(self):
        """Load the last run state from JSON file"""
        try:
            if os.path.exists(self.last_run_file):
                with open(self.last_run_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logging.error(f"Error loading last run state: {e}")
            return {}

    def save_current_state(self, state):
        """Save the current state to JSON file"""
        try:
            with open(self.last_run_file, 'w') as f:
                json.dump(state, f)
        except Exception as e:
            logging.error(f"Error saving current state: {e}")

    def get_modified_files(self):
        """Get all modified STEP and PDF files since last run"""
        last_state = self.load_last_run_state()
        current_state = {}
        modified_files = []

        # Define allowed file extensions
        allowed_extensions = ('.step', '.stp', '.pdf')

        try:
            for root, _, files in os.walk(self.folder_to_monitor):
                for file in files:
                    if file.lower().endswith(allowed_extensions):
                        full_path = os.path.join(root, file)
                        try:
                            mod_time = os.path.getmtime(full_path)
                            current_state[full_path] = mod_time

                            # Check if file is new or modified
                            if (full_path not in last_state or
                                mod_time > last_state[full_path]):
                                modified_files.append(full_path)
                        except Exception as e:
                            logging.error(f"Error processing file {full_path}: {e}")

        except Exception as e:
            logging.error(f"Error walking directory {self.folder_to_monitor}: {e}")
            raise

        return modified_files, current_state

    def check_file_size(self, file_path, max_size_mb=20):
        """Check if file size is within Outlook limits"""
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
        return file_size_mb <= max_size_mb

    def process_attachment(self, file_path):
        """Process a single attachment with size checking"""
        try:
            if not self.check_file_size(file_path):
                logging.warning(f"File {file_path} exceeds size limit - skipping")
                return False

            # Your existing attachment logic here
            return True
        except Exception as e:
            logging.error(f"Failed to process attachment {file_path}: {str(e)}")
            return False

    def process_files(self, modified_files):
        """Process modified files and send email"""
        try:
            if not modified_files:
                return

            self.initialize_outlook()
            mail = self.outlook.CreateItem(0)  # 0 = olMailItem
            mail.To = self.target_email
            mail.Subject = f"Modified Files Report - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}"

            unattached_files = []
            attached_count = 0

            for file_path in modified_files:
                try:
                    if self.check_file_size(file_path):
                        mail.Attachments.Add(file_path)
                        attached_count += 1
                        logging.info(f"Added attachment: {file_path}")
                    else:
                        unattached_files.append(file_path)
                        logging.warning(f"File too large to attach: {file_path}")
                except Exception as e:
                    unattached_files.append(file_path)
                    logging.error(f"Failed to attach file {file_path}: {e}")

            # Create email body with attachment status
            body = f"Hey Sammy, there were {len(modified_files)} files modified or created today. Can you nest or renest them?\n"
            body += f"Successfully attached {attached_count} files.\n\n"

            if unattached_files:
                body += f"\nWARNING: {len(unattached_files)} files could not be attached:\n"
                for file in unattached_files:
                    body += f"- {os.path.basename(file)}\n"
                body += f"\nThese files may be too large or inaccessible. Please check {self.folder_to_monitor}"

            mail.Body = body
            mail.Send()
            logging.info(f"Email sent successfully with {attached_count} files")

        except Exception as e:
            logging.error(f"Error processing files: {e}")
            raise

def main():
    try:
        # Load configuration
        config = load_config()
        folder_to_monitor = config['folder_to_monitor']
        target_email = config['target_email']

        logging.info("Starting nesting file monitor...")
        logging.info(f"Monitoring folder: {folder_to_monitor}")

        # First just check for modified files without initializing Outlook
        monitor = None
        try:
            # Create monitor with config values
            monitor = NestingMonitor(folder_to_monitor, target_email)
            modified_files, current_state = monitor.get_modified_files()

            if modified_files:
                logging.info(f"Found {len(modified_files)} modified files")
                # Only initialize Outlook and send email if we have modified files
                monitor.process_files(modified_files)
                monitor.save_current_state(current_state)
                logging.info("Successfully processed files")
            else:
                logging.info("No new or modified files found")

        except Exception as e:
            logging.error(f"Error during execution: {str(e)}")
            raise

    except Exception as e:
        logging.error(f"Fatal error: {str(e)}")
        sys.exit(1)
    finally:
        if monitor:
            del monitor

if __name__ == "__main__":
    main()


