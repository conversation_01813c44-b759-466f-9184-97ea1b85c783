/*<meta />*/

/*!
 * Copyright MadCap Software
 * http://www.madcapsoftware.com/
 * Unlicensed use is strictly prohibited
 *
 * v12.0.0.0
 */

.MCDropDown
{
	
}

.MCDropDownHead
{
	
}

.MCDropDownHotSpot
{
	
}

.MCDropDownBody
{
	
}

.MCDropDownIcon
{
	border: none;
}

.MCExpanding
{
	
}

.MCExpandingHead
{
	
}

.MCExpandingBody
{
	
}

.MCExpandingIcon
{
	border: none;
}

.MCKLink
{
	
}

.MCKLinkSpot
{
	
}

.MCKLinkBody
{
	background-color: #bbccdd;
	border: solid 1px #000000;
	margin: 0px;
	padding: 0px;
	position: absolute;
	cursor: pointer;
	cursor: hand;
	filter: alpha( opacity = 100 );
	-moz-opacity: 1.0;
	z-index: 2;
}

.MCKLinkBodyBG
{
	background-color: #000000;
	margin: 0px;
	padding: 0px;
	position: absolute;
	filter: alpha( opacity = 100 );
	-moz-opacity: 1.0;
	z-index: 1;
}

.MCKLinkBodyCell_Highlighted
{
	padding: 5px;
	background-color: Blue;
	color: White;
}

.MCKLinkBodyCell
{
	padding: 5px;
	background-color: Transparent;
	color: Black;
}

.MCKeywordLinkIcon
{
	border: none;
}

.MCConceptLinkIcon
{
	border: none;
}

.MCRelatedTopicsIcon
{
	border: none;
}

.MCPopup
{
	
}

a.MCPopupSpot
{
	
}

.MCPopupBody
{
	background-color: #ffffff;
	border: solid 1px #000000;
	margin: 0px;
	padding: 0px;
	position: absolute;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 2;
}

.MCPopupBodyBG
{
	background-color: #000000;
	margin: 0px;
	padding: 0px;
	position: absolute;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 1;
}

.MCTextPopup
{
	
}

.MCTextPopupSpot
{
	
}

.MCTextPopupBody
{
	background-color: #bbccdd;
	border: solid 1px #000000;
	margin: 0px;
	padding: 5px;
	position: absolute;
	overflow: auto;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 2;
}

.MCTextPopupBodyBG
{
	background-color: #000000;
	margin: 0px;
	padding: 0px;
	position: absolute;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 1;
}

.MCPopupThumbnail
{
	border: none;
}

.MCPopupThumbnail_Popup
{
	border: solid 1px Gray;
	position: absolute;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 102;
}

.MCToggler
{
	
}

.MCTogglerIcon
{
	border: none;
}

div.MCDialogWrapper
{
	width: 400px;
	position: fixed;
	z-index: 110;
}

div.MCDialog
{
	width: 400px;
	position: absolute;
	padding: 10px;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 2;
}

div.MCDialogShadow
{
	width: 400px;
	position: absolute;
	padding: 10px;
	filter: alpha( opacity = 0 );
	-moz-opacity: 0.0;
	z-index: 1;
}

div.MCDialog table.MCDialogOuterTable
{
	width: 100%;
	margin: 0px;
	padding: 0px;
	border: none;
	border-collapse: collapse;
	table-layout: fixed;
}

div.MCDialog table.MCDialogOuterTable td.Label
{
	vertical-align: top;
}

div.MCDialogWrapper.pulse-login
{
	width: 408px;
	height: 428px;
}

div.MCDialogWrapper.pulse-login div.MCDialog
{
	width: 100%;
	height: 100%;
	padding: 0;
}

div.MCDialogWrapper.pulse-login div.MCDialogShadow
{
	display: none;
}

div.MCDialogWrapper.pulse-login button.close-dialog
{
	position: absolute;
	top: 8px;
	right: 8px;
	background: none;
	background-image: url('../../Skin/Images/CloseDialog.png');
	height: 16px;
	width: 16px;
	margin: 0;
	padding: 0;
	border: 0;
	cursor: pointer;
}

#pulse-login-frame
{
	width: 100%;
	height: 100%;
	border: none;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	-webkit-box-shadow: 0 4px 10px #333333;
	-moz-box-shadow: 0 4px 10px #333333;
	box-shadow: 0 4px 10px #333333;
}

endnotesBlock
{
	display: block;
}

endnoteBlock
{
	display: block;
}

footnotesBlock
{
	display: block;
}

footnoteBlock
{
	display: block;
}

p.MCWebHelpFramesetLink
{
	display: none;
}

div.codeSnippetBody td > pre
{
	margin: 0;
}

div.codeSnippetBody_0
{
	font-family: 'Courier New';
	padding: 1em 0 1em 0;
	line-height: 1em;
	tab-size: 4;
	clear: both;
}

div.codeSnippetCaption_0
{
	font-family: Verdana;
	font-size: 10pt;
	text-decoration: underline;
	page-break-after: avoid;
	page-break-before: avoid;
}

div.codeSnippet_0
{
	padding: 1em;
	border: solid 1px #DDD;
	font-size: 9pt;
}

div.codeSnippetBody1
{
	font-family: Consolas, Monaco, 'Courier New', monospace;
	padding: 1em 0 1em 0;
	line-height: 1em;
	tab-size: 4;
	clear: both;
}

div.codeSnippet1
{
	padding: 1em;
	border: solid 1px #DDD;
	font-size: 9pt;
	caption-side: top;
}

a.MCXref_0
{
	color: blue;
	text-decoration: underline;
}

a.MCXref_0:link
{
	color: blue;
	text-decoration: underline;
}

a.MCXref_0:visited
{
	color: blue;
	text-decoration: underline;
}

a.MCXref_0:hover
{
	
}

a.MCXref_0:focus
{
	
}

