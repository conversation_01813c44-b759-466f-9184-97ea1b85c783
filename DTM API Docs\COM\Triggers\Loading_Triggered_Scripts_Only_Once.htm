<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Introduction|Triggered Scripts" MadCap:PathToHelpSystem="../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Loading Triggered Scripts Only Once</title>
        <link href="../../default.css" rel="stylesheet" type="text/css" />
        <script language="JavaScript" src="../../Resources/HelpDesign.js">
        </script>
        <script src="../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h4><a name="kanchor5128"></a><span class="mc-variable System.Title variable">Loading Triggered Scripts Only Once</span>
            </h4>
            <p>Triggered scripts can be load only once to save file accesses and to 
 start scripts faster.</p>
            <p>When starting the program all definitions regarding triggered scripts 
 are read. When first activating a defined and activated trigger, the script 
 is loaded and executed. When the trigger is then again activated, the 
 file does not need to be reloaded.</p>
            <p>Whether a trigger can be modified while running, is controlled using 
 the registry key:</p>
            <ul style="list-style: disc;" type="disc">
                <li class="p">
                    <p style="font-family: 'Courier New', monospace;">HKLM\SOFTWARE\Zuken\E3.series\&lt;version&gt;\EnableTriggerModifications</p>
                </li>
            </ul>
            <p>that is read as DWORD value. Only when this value is available and &lt;&gt; 
 0, definitions can be realized through <span style="font-family: 'Courier New', monospace;font-weight: bold;">e3.SetTrigger()</span>.</p>
            <p><a name="kanchor5129"></a>Execution of the triggers can be controlled using the following COM 
 calls:</p>
            <ul style="list-style: disc;" type="disc">
                <li class="p">
                    <p style="font-family: 'Courier New', monospace;font-weight: bold;">&lt;active&gt; 
 = e3.GetTrigger( &lt;name&gt;, &lt;file&gt; )</p>
                </li>
                <ul style="list-style: circle;" type="circle">
                    <li class="p">
                        <p>Returns the current definition for the <span style="font-family: 'Courier New', monospace;">&lt;name&gt;</span> 
 in <span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span> 
 and <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 (regardless of whether <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 is already read or not).</p>
                    </li>
                    <li class="p">
                        <p>When an invalid trigger <span style="font-family: 'Courier New', monospace;">&lt;name&gt;</span> 
 was defined, -2 is returned.</p>
                    </li>
                </ul>
            </ul>
            <ul style="list-style: circle;" type="circle">
                <li style="list-style: disc;" type="disc" class="p">
                    <p style="font-weight: bold;font-family: 'Courier New', monospace;">&lt;last&gt; 
 = e3.SetTrigger( &lt;name&gt;, &lt;file&gt;, &lt;active&gt; )</p>
                </li>
                <ul style="list-style: circle;" type="circle">
                    <li class="p">
                        <p>Defines <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 as trigger<span style="font-family: 'Courier New', monospace;"> &lt;name&gt;</span>. 
 <span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span> 
 defines, whether the trigger is to be activated.</p>
                    </li>
                    <li class="p">
                        <p><span style="font-family: 'Courier New', monospace;">&lt;name&gt;</span> 
 must always be defined. All trigger names are allowed that can be defined 
 in the Registry.</p>
                    </li>
                    <li class="p">
                        <p><span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 must not be defined, when a trigger shall be activated or deactivated 
 and the file name is already common from the registry or previous SetTrigger-calls.</p>
                    </li>
                    <li class="p">
                        <p><span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span>=0 
 deactivates a trigger (however, remembers the file name and file contents). 
 <span style="font-family: 'Courier New', monospace;"><br />		&lt;active&gt;</span>=1 activates an either known trigger or a 
 new file defined through <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span>.<span style="font-family: 'Courier New', monospace;"><br />		&lt;active&gt;</span>=2 activates an either known trigger or a 
 new file defined through <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span>, 
 however reloads the file even if the file name has not changed.</p>
                    </li>
                    <li class="p">
                        <p><span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span>=-1 
 permanently deletes a trigger (forgets the file name and file contents), 
 when no <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 is defined. When a <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> 
 is defined, its contents are re-read. In this case, the old state of the 
 <span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span> 
 designation of the trigger remains unchanged.</p>
                    </li>
                    <li class="p">
                        <p>The 
 return value <span style="font-family: 'Courier New', monospace;">&lt;last&gt;</span> 
 is the state of the <span style="font-family: 'Courier New', monospace;">&lt;active&gt;</span> 
 flag before it might be realized (usually 0 or 1). When a defined <span style="font-family: 'Courier New', monospace;">&lt;file&gt;</span> name 
 is not readable, -1 is returned. When an invalid trigger <span style="font-family: 'Courier New', monospace;">&lt;name&gt;</span> 
 is defined, -2 is returned. When the registry key <span style="font-family: 'Courier New', monospace;font-weight: bold;">EnableTriggerModifications</span> 
 does not exist or is 0, -3 is returned.</p>
                    </li>
                </ul>
            </ul>
            <p><span style="font-weight: bold;">Example</span>:</p>
            <table style="left: 0px;top: 1018px;width: 504px;height: 20px;border-spacing: 0px;border-spacing: 0px;" cellspacing="0" height="20" width="504">
                <col style="width: 100%;" />
                <tr valign="top">
                    <td style="width: 100%;border-left-width: 1px;border-left-color: #000000;border-left-style: Solid;border-top-style: Solid;border-top-color: #000000;border-top-width: 1px;border-right-width: 1px;border-right-color: #000000;border-right-style: Solid;border-bottom-style: Solid;border-bottom-color: #000000;border-bottom-width: 1px;padding-right: 10px;padding-left: 10px;" width="100%">
                        <p><code style="font-family: monospace;"><span style="color: #0000FF;">Set</span> e3 = CreateObject("CT.Application")<br /><br />e3.AvoidAutomaticClosing<br /><br />last = e3.SetTrigger("AfterModifySymbol", "C:\Trigger.vbs", 
 1)<span style="color: #006400;"><br /><br />' Do something<br />'<br />' ...<br /><br />' Get trigger information</span><br />active = e3.GetTrigger("AfterModifySymbol", Filename)<span style="color: #006400;"><br /><br />' Do something<br />'<br />' ...<br /><br />' Removing the trigger</span><br />last = e3.SetTrigger("AfterModifySymbol", "", -1)<span style="color: #0000FF;"><br /><br />Set</span> e3 = <span style="color: #0000FF;">Nothing</span></code>
                        </p>
                    </td>
                </tr>
            </table>
            <hr style="float: aligncenter;" align="center" width="100%" size="0" />
            <table wrapperparagraphselector="P" style="width: 100%;margin-top: 14pt;border-spacing: 0px;border-spacing: 0px;" cellspacing="0" width="100%">
                <tr>
                    <td style="width: 20%;padding-left: 2px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;" valign="top" width="20%">
                        <p style="margin-bottom: 0;font-weight: bold;">See also:</p>
                    </td>
                    <td style="width: 80%;padding-left: 2px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;" valign="top" width="80%">
                        <ul style="list-style: disc;" type="disc">
                            <li class="kadov-p"><a href="Overview.htm">Triggered Scripts</a>
                            </li>
                        </ul>
                    </td>
                </tr>
            </table>
&#160; 
</div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>