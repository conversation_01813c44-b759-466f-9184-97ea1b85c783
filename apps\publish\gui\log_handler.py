"""
GUI Log Handler for thread-safe log message queuing and display.

This module provides a custom logging handler that queues log messages
for display in the GUI console widget.
"""

import logging
import threading
from datetime import datetime
from typing import Optional, Callable, Set
from .communication import LogMessage, ThreadSafeQueue


class GUILogHandler(logging.Handler):
    """
    Custom logging handler that queues log messages for GUI display.
    
    This handler is thread-safe and allows the GUI to retrieve log messages
    from a background thread without blocking.
    """
    
    def __init__(self, max_queue_size: int = 1000):
        """
        Initialize the GUI log handler.
        
        Args:
            max_queue_size: Maximum number of log messages to queue
        """
        super().__init__()
        self._message_queue = ThreadSafeQueue(maxsize=max_queue_size)
        self._callbacks: Set[Callable[[LogMessage], None]] = set()
        self._lock = threading.Lock()
        self._enabled = True
        
        # Set default formatter
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.setFormatter(formatter)
    
    def emit(self, record: logging.LogRecord):
        """
        Emit a log record by adding it to the message queue.
        
        Args:
            record: Log record to emit
        """
        if not self._enabled:
            return
            
        try:
            # Create log message
            log_message = LogMessage(
                timestamp=datetime.fromtimestamp(record.created),
                level=record.levelname,
                message=record.getMessage(),
                module=record.name,
                thread_id=str(threading.get_ident()),
                formatted_message=self.format(record)
            )
            
            # Add to queue (non-blocking)
            if not self._message_queue.put(log_message, timeout=0.1):
                # Queue is full, remove oldest message and try again
                self._message_queue.get(timeout=0.1)
                self._message_queue.put(log_message, timeout=0.1)
            
            # Notify callbacks
            self._notify_callbacks(log_message)
            
        except Exception:
            # Don't let logging errors crash the application
            self.handleError(record)
    
    def get_messages(self, max_messages: Optional[int] = None) -> list[LogMessage]:
        """
        Get queued log messages.
        
        Args:
            max_messages: Maximum number of messages to retrieve (None for all)
            
        Returns:
            List of log messages
        """
        if max_messages is None:
            return self._message_queue.get_all()
        
        messages = []
        for _ in range(max_messages):
            message = self._message_queue.get(timeout=0.01)
            if message is None:
                break
            messages.append(message)
        
        return messages
    
    def add_callback(self, callback: Callable[[LogMessage], None]):
        """
        Add a callback to be notified of new log messages.
        
        Args:
            callback: Function to call with new log messages
        """
        with self._lock:
            self._callbacks.add(callback)
    
    def remove_callback(self, callback: Callable[[LogMessage], None]):
        """
        Remove a callback.
        
        Args:
            callback: Callback function to remove
        """
        with self._lock:
            self._callbacks.discard(callback)
    
    def _notify_callbacks(self, log_message: LogMessage):
        """
        Notify all registered callbacks of a new log message.
        
        Args:
            log_message: Log message to send to callbacks
        """
        with self._lock:
            callbacks = self._callbacks.copy()
        
        for callback in callbacks:
            try:
                callback(log_message)
            except Exception as e:
                # Don't let callback errors affect logging
                print(f"Error in log callback: {e}")
    
    def clear_queue(self):
        """Clear all queued log messages."""
        self._message_queue.clear()
    
    def set_enabled(self, enabled: bool):
        """
        Enable or disable the handler.
        
        Args:
            enabled: Whether to enable the handler
        """
        self._enabled = enabled
    
    def is_enabled(self) -> bool:
        """Check if the handler is enabled."""
        return self._enabled
    
    def get_queue_size(self) -> int:
        """Get the current queue size."""
        return self._message_queue.qsize()


class LogLevelFilter:
    """
    Utility class for filtering log messages by level.
    """
    
    # Log level hierarchy
    LEVELS = {
        'DEBUG': 10,
        'INFO': 20,
        'WARNING': 30,
        'ERROR': 40,
        'CRITICAL': 50
    }
    
    def __init__(self, min_level: str = 'INFO'):
        """
        Initialize the filter.
        
        Args:
            min_level: Minimum log level to accept
        """
        self.set_min_level(min_level)
    
    def set_min_level(self, min_level: str):
        """
        Set the minimum log level.
        
        Args:
            min_level: Minimum log level to accept
        """
        self._min_level = min_level.upper()
        self._min_level_value = self.LEVELS.get(self._min_level, 20)
    
    def should_show(self, log_message: LogMessage) -> bool:
        """
        Check if a log message should be shown based on level.
        
        Args:
            log_message: Log message to check
            
        Returns:
            True if message should be shown
        """
        message_level_value = self.LEVELS.get(log_message.level.upper(), 0)
        return message_level_value >= self._min_level_value
    
    def filter_messages(self, messages: list[LogMessage]) -> list[LogMessage]:
        """
        Filter a list of log messages by level.
        
        Args:
            messages: List of log messages to filter
            
        Returns:
            Filtered list of log messages
        """
        return [msg for msg in messages if self.should_show(msg)]


def setup_gui_logging(logger_name: Optional[str] = None, 
                     level: str = 'INFO',
                     max_queue_size: int = 1000) -> GUILogHandler:
    """
    Set up GUI logging for a logger.
    
    Args:
        logger_name: Name of logger to configure (None for root logger)
        level: Log level for the handler
        max_queue_size: Maximum queue size for log messages
        
    Returns:
        Configured GUILogHandler instance
    """
    # Get logger
    if logger_name is None:
        logger = logging.getLogger()
    else:
        logger = logging.getLogger(logger_name)
    
    # Create and configure handler
    gui_handler = GUILogHandler(max_queue_size=max_queue_size)
    gui_handler.setLevel(getattr(logging, level.upper()))
    
    # Add handler to logger
    logger.addHandler(gui_handler)
    
    return gui_handler


def remove_gui_logging(logger_name: Optional[str] = None):
    """
    Remove GUI logging handlers from a logger.
    
    Args:
        logger_name: Name of logger to configure (None for root logger)
    """
    # Get logger
    if logger_name is None:
        logger = logging.getLogger()
    else:
        logger = logging.getLogger(logger_name)
    
    # Remove GUI handlers
    handlers_to_remove = [h for h in logger.handlers if isinstance(h, GUILogHandler)]
    for handler in handlers_to_remove:
        logger.removeHandler(handler)
        handler.close()
