"""
File and folder operation utilities.

This module contains the FileOperations class for handling file system
operations including folder creation, JSON saving, and file management.
"""

import os
import json
import logging
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from .performance_monitor import timed_operation, FileIOOptimizer


class FileOperationError(Exception):
    """Custom exception for file operation errors."""
    pass


class FileOperations:
    """Utility class for file and folder operations."""
    
    def __init__(self):
        """Initialize file operations utility."""
        self.logger = logging.getLogger(__name__)
        
    @staticmethod
    def create_serial_folder(base_path: str, serial: str) -> str:
        """
        Create a folder for the specified serial number.
        
        Args:
            base_path: Base directory path
            serial: Serial number for folder name
            
        Returns:
            Path to the created serial folder
            
        Raises:
            FileOperationError: If folder creation fails
        """
        if not FileOperations.validate_path(base_path):
            raise FileOperationError(f"Invalid base path: {base_path}")
            
        if not serial or not serial.strip():
            raise FileOperationError("Serial number cannot be empty")
            
        # Sanitize serial number for filesystem
        sanitized_serial = FileOperations._sanitize_filename(serial)
        serial_folder_path = os.path.join(base_path, sanitized_serial)
        
        try:
            os.makedirs(serial_folder_path, exist_ok=True)
            logging.getLogger(__name__).info(f"Created serial folder: {serial_folder_path}")
            return serial_folder_path
        except OSError as e:
            error_msg = f"Failed to create serial folder {serial_folder_path}: {e}"
            logging.getLogger(__name__).error(error_msg)
            raise FileOperationError(error_msg) from e
            
    @staticmethod
    def save_job_data_json(project_data, output_path: str) -> bool:
        """
        Save project data to a JSON file.
        
        Args:
            project_data: ProjectData object to save
            output_path: Path for the JSON file
            
        Returns:
            True if save succeeded, False otherwise
            
        Raises:
            FileOperationError: If save operation fails
        """
        with timed_operation(f"Save JSON: {os.path.basename(output_path)}"):
            if not output_path or not output_path.strip():
                raise FileOperationError("Output path cannot be empty")
                
            # Validate output directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except OSError as e:
                    raise FileOperationError(f"Cannot create output directory {output_dir}: {e}")
            
            try:
                # Convert project_data to dictionary format
                job_data = {
                    "gss_parent": getattr(project_data, 'gss_parent', ''),
                    "serial_number": getattr(project_data, 'serial_number', ''),
                    "customer": getattr(project_data, 'customer', ''),
                    "location": getattr(project_data, 'location', ''),
                    "title": getattr(project_data, 'title', ''),
                    "sales_order": getattr(project_data, 'sales_order', ''),
                    "model": getattr(project_data, 'model', ''),
                    "folder_path": getattr(project_data, 'folder_path', ''),
                    "timestamp": datetime.now().isoformat()
                }
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(job_data, f, indent=4, ensure_ascii=False)
                    
                logging.getLogger(__name__).info(f"Job data saved to: {output_path}")
                return True
                
            except Exception as e:
                error_msg = f"Failed to save job data to {output_path}: {e}"
                logging.getLogger(__name__).error(error_msg)
                raise FileOperationError(error_msg) from e
            
    @staticmethod
    def create_dataplates_folder(base_path: str) -> str:
        """
        Create a DataPlates subfolder in the specified base path.
        
        Args:
            base_path: Base directory path
            
        Returns:
            Path to the created DataPlates folder
            
        Raises:
            FileOperationError: If folder creation fails
        """
        if not FileOperations.validate_path(base_path):
            raise FileOperationError(f"Invalid base path: {base_path}")
            
        dataplates_path = os.path.join(base_path, "DataPlates")
        
        try:
            os.makedirs(dataplates_path, exist_ok=True)
            logging.getLogger(__name__).info(f"Created DataPlates folder: {dataplates_path}")
            return dataplates_path
        except OSError as e:
            error_msg = f"Failed to create DataPlates folder {dataplates_path}: {e}"
            logging.getLogger(__name__).error(error_msg)
            raise FileOperationError(error_msg) from e
            
    @staticmethod
    def validate_path(path: str) -> bool:
        """
        Validate that a path is accessible and safe.
        
        Args:
            path: Path to validate
            
        Returns:
            True if path is valid and accessible, False otherwise
        """
        try:
            # Basic path validation
            if not path or not path.strip():
                return False
                
            # Convert to Path object for better validation
            path_obj = Path(path)
            
            # Check for path traversal attempts
            if '..' in path or path.startswith('/') and os.name == 'nt':
                return False
                
            # For existing paths, check if accessible
            if path_obj.exists():
                return os.access(path, os.R_OK)
                
            # For new paths, check if parent directory exists and is writable
            parent_dir = path_obj.parent
            if parent_dir.exists():
                return os.access(parent_dir, os.W_OK)
                
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def _sanitize_filename(filename: str) -> str:
        """
        Sanitize filename by removing or replacing invalid characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename safe for filesystem
        """
        # Characters not allowed in Windows filenames
        invalid_chars = '<>:"/\\|?*'
        sanitized = filename
        
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
            
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip(' .')
        
        # Ensure filename is not empty
        if not sanitized:
            sanitized = 'unnamed'
            
        return sanitized
    
    @staticmethod
    def ensure_directory_exists(directory_path: str) -> str:
        """
        Ensure a directory exists, creating it if necessary.
        
        Args:
            directory_path: Path to directory
            
        Returns:
            Path to the directory
            
        Raises:
            FileOperationError: If directory creation fails
        """
        if not directory_path or not directory_path.strip():
            raise FileOperationError("Directory path cannot be empty")
            
        try:
            os.makedirs(directory_path, exist_ok=True)
            logging.getLogger(__name__).debug(f"Ensured directory exists: {directory_path}")
            return directory_path
        except OSError as e:
            error_msg = f"Failed to create directory {directory_path}: {e}"
            logging.getLogger(__name__).error(error_msg)
            raise FileOperationError(error_msg) from e
    
    @staticmethod
    def copy_file(source_path: str, destination_path: str, overwrite: bool = False) -> bool:
        """
        Copy a file from source to destination.
        
        Args:
            source_path: Path to source file
            destination_path: Path to destination file
            overwrite: Whether to overwrite existing files
            
        Returns:
            True if copy succeeded, False otherwise
            
        Raises:
            FileOperationError: If copy operation fails
        """
        with timed_operation(f"Copy File: {os.path.basename(source_path)}"):
            if not os.path.exists(source_path):
                raise FileOperationError(f"Source file does not exist: {source_path}")
                
            if os.path.exists(destination_path) and not overwrite:
                raise FileOperationError(f"Destination file exists and overwrite is False: {destination_path}")
                
            # Ensure destination directory exists
            dest_dir = os.path.dirname(destination_path)
            if dest_dir:
                FileOperations.ensure_directory_exists(dest_dir)
            
            try:
                shutil.copy2(source_path, destination_path)
                logging.getLogger(__name__).info(f"Copied file from {source_path} to {destination_path}")
                return True
            except Exception as e:
                error_msg = f"Failed to copy file from {source_path} to {destination_path}: {e}"
                logging.getLogger(__name__).error(error_msg)
                raise FileOperationError(error_msg) from e
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        Delete a file if it exists.
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            True if file was deleted or didn't exist, False otherwise
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logging.getLogger(__name__).info(f"Deleted file: {file_path}")
            return True
        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to delete file {file_path}: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        Get the size of a file in bytes.
        
        Args:
            file_path: Path to file
            
        Returns:
            File size in bytes
            
        Raises:
            FileOperationError: If file doesn't exist or can't be accessed
        """
        try:
            return os.path.getsize(file_path)
        except OSError as e:
            raise FileOperationError(f"Cannot get size of file {file_path}: {e}")
    
    @staticmethod
    def list_files_in_directory(directory_path: str, extension: Optional[str] = None) -> List[str]:
        """
        List files in a directory, optionally filtered by extension.
        
        Args:
            directory_path: Path to directory
            extension: File extension to filter by (e.g., '.pdf', '.json')
            
        Returns:
            List of file paths
            
        Raises:
            FileOperationError: If directory doesn't exist or can't be accessed
        """
        if not os.path.exists(directory_path):
            raise FileOperationError(f"Directory does not exist: {directory_path}")
            
        if not os.path.isdir(directory_path):
            raise FileOperationError(f"Path is not a directory: {directory_path}")
        
        try:
            files = []
            for item in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item)
                if os.path.isfile(item_path):
                    if extension is None or item.lower().endswith(extension.lower()):
                        files.append(item_path)
            return files
        except OSError as e:
            raise FileOperationError(f"Cannot list files in directory {directory_path}: {e}")
    
    @staticmethod
    def load_json_file(file_path: str) -> Dict[str, Any]:
        """
        Load data from a JSON file.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Dictionary containing JSON data
            
        Raises:
            FileOperationError: If file cannot be loaded or parsed
        """
        if not os.path.exists(file_path):
            raise FileOperationError(f"JSON file does not exist: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logging.getLogger(__name__).debug(f"Loaded JSON data from: {file_path}")
            return data
        except json.JSONDecodeError as e:
            raise FileOperationError(f"Invalid JSON in file {file_path}: {e}")
        except Exception as e:
            raise FileOperationError(f"Failed to load JSON file {file_path}: {e}")