"""
Configuration Management Module

This module provides centralized configuration management for the Engineering Tools suite.
It handles loading, saving, and validating configuration files.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from .utils import get_user_data_dir, get_app_dir, ensure_dir_exists

# Default configuration paths
CONFIG_FILENAME = 'config.json'
MODELS_FILENAME = 'models.json'

def get_config_path(filename: str) -> str:
    """
    Get the path to a configuration file, checking user directory first, then app directory.
    
    Args:
        filename: Name of the configuration file
        
    Returns:
        str: Full path to the configuration file
    """
    # First check in user data directory
    user_config = os.path.join(get_user_data_dir(), filename)
    if os.path.exists(user_config):
        return user_config
    
    # Then check in app resources/config directory
    app_config = os.path.join(get_app_dir(), 'resources', 'config', filename)
    if os.path.exists(app_config):
        return app_config
    
    # Finally check in app directory (for backward compatibility)
    return os.path.join(get_app_dir(), filename)

def load_config(filename: str = CONFIG_FILENAME) -> Dict[str, Any]:
    """
    Load a configuration file.
    
    Args:
        filename: Name of the configuration file to load
        
    Returns:
        dict: Configuration data
        
    Raises:
        FileNotFoundError: If the configuration file cannot be found
        json.JSONDecodeError: If the configuration file is not valid JSON
    """
    config_path = get_config_path(filename)
    
    if not os.path.exists(config_path):
        logging.error(f"Configuration file not found: {config_path}")
        raise FileNotFoundError(f"Configuration file not found: {filename}")
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
            logging.debug(f"Loaded configuration from {config_path}")
            return config
    except json.JSONDecodeError as e:
        logging.error(f"Invalid JSON in configuration file {config_path}: {e}")
        raise
    except Exception as e:
        logging.error(f"Error loading configuration file {config_path}: {e}")
        raise

def save_config(data: Dict[str, Any], filename: str = CONFIG_FILENAME) -> bool:
    """
    Save configuration data to a file in the user data directory.
    
    Args:
        data: Configuration data to save
        filename: Name of the configuration file
        
    Returns:
        bool: True if successful, False otherwise
    """
    user_dir = get_user_data_dir()
    ensure_dir_exists(user_dir)
    config_path = os.path.join(user_dir, filename)
    
    try:
        with open(config_path, 'w') as f:
            json.dump(data, f, indent=4)
        logging.debug(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logging.error(f"Error saving configuration to {config_path}: {e}")
        return False

def get_config_value(key: str, default: Any = None, filename: str = CONFIG_FILENAME) -> Any:
    """
    Get a specific value from the configuration.
    
    Args:
        key: Configuration key to retrieve
        default: Default value to return if key is not found
        filename: Name of the configuration file
        
    Returns:
        The configuration value, or the default if not found
    """
    try:
        config = load_config(filename)
        return config.get(key, default)
    except Exception as e:
        logging.warning(f"Error getting configuration value {key}: {e}")
        return default

def update_config(key: str, value: Any, filename: str = CONFIG_FILENAME) -> bool:
    """
    Update a specific value in the configuration.
    
    Args:
        key: Configuration key to update
        value: New value
        filename: Name of the configuration file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        config = load_config(filename)
    except FileNotFoundError:
        config = {}
    
    config[key] = value
    return save_config(config, filename)

def load_models() -> Dict[str, Any]:
    """
    Load the models configuration.
    
    Returns:
        dict: Models configuration data
    """
    return load_config(MODELS_FILENAME)

def save_models(models_data: Dict[str, Any]) -> bool:
    """
    Save the models configuration.
    
    Args:
        models_data: Models configuration data
        
    Returns:
        bool: True if successful, False otherwise
    """
    return save_config(models_data, MODELS_FILENAME)
