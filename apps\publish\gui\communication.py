"""
Thread-safe communication infrastructure for GUI-worker communication.

This module provides data classes and utilities for safe communication between
the GUI thread and background worker threads.
"""

import queue
import threading
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Any, Callable, Dict
import logging


class WorkerStatus(Enum):
    """Enumeration of possible worker states."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"


@dataclass
class ProgressUpdate:
    """
    Data class for progress update communication.
    
    Attributes:
        current: Current progress value
        total: Total progress value
        message: Progress message
        step_name: Name of current step
        eta_seconds: Estimated time remaining in seconds
        can_cancel: Whether operation can be cancelled
        percentage: Calculated percentage (0-100)
    """
    current: int
    total: int
    message: str
    step_name: str
    eta_seconds: Optional[float] = None
    can_cancel: bool = True
    
    @property
    def percentage(self) -> float:
        """Calculate percentage completion."""
        if self.total <= 0:
            return 0.0
        return min(100.0, (self.current / self.total) * 100.0)
    
    @property
    def is_complete(self) -> bool:
        """Check if progress is complete."""
        return self.current >= self.total
    
    def __str__(self) -> str:
        """String representation of progress."""
        return f"{self.step_name}: {self.current}/{self.total} ({self.percentage:.1f}%) - {self.message}"


@dataclass
class LogMessage:
    """
    Data class for log message communication.
    
    Attributes:
        timestamp: When the log message was created
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        message: Log message content
        module: Module that generated the log
        thread_id: ID of the thread that generated the log
        formatted_message: Pre-formatted message for display
    """
    timestamp: datetime
    level: str
    message: str
    module: str
    thread_id: str
    formatted_message: Optional[str] = None
    
    def __post_init__(self):
        """Generate formatted message if not provided."""
        if self.formatted_message is None:
            time_str = self.timestamp.strftime("%H:%M:%S.%f")[:-3]  # Include milliseconds
            self.formatted_message = f"[{time_str}] {self.level:8} {self.module}: {self.message}"


@dataclass
class WorkerResult:
    """
    Data class for worker operation results.
    
    Attributes:
        success: Whether the operation succeeded
        result_data: Operation result data
        error_message: Error message if operation failed
        warnings: List of warning messages
        metadata: Additional metadata
    """
    success: bool
    result_data: Any = None
    error_message: Optional[str] = None
    warnings: Optional[list] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize empty collections."""
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


class ThreadSafeQueue:
    """
    Thread-safe queue wrapper with additional functionality.
    
    Provides a wrapper around queue.Queue with convenience methods
    and proper error handling.
    """
    
    def __init__(self, maxsize: int = 0):
        """
        Initialize the thread-safe queue.
        
        Args:
            maxsize: Maximum queue size (0 for unlimited)
        """
        self._queue = queue.Queue(maxsize=maxsize)
        self._logger = logging.getLogger(__name__)
    
    def put(self, item: Any, timeout: Optional[float] = None) -> bool:
        """
        Put an item in the queue.
        
        Args:
            item: Item to put in queue
            timeout: Timeout in seconds (None for blocking)
            
        Returns:
            True if item was added, False if timeout occurred
        """
        try:
            if timeout is None:
                self._queue.put(item)
                return True
            else:
                self._queue.put(item, timeout=timeout)
                return True
        except queue.Full:
            self._logger.warning(f"Queue full, could not add item: {type(item).__name__}")
            return False
    
    def get(self, timeout: Optional[float] = None) -> Optional[Any]:
        """
        Get an item from the queue.
        
        Args:
            timeout: Timeout in seconds (None for blocking)
            
        Returns:
            Item from queue or None if timeout occurred
        """
        try:
            if timeout is None:
                return self._queue.get()
            else:
                return self._queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def get_all(self, timeout: float = 0.1) -> list:
        """
        Get all available items from the queue.
        
        Args:
            timeout: Timeout for each get operation
            
        Returns:
            List of all available items
        """
        items = []
        while True:
            item = self.get(timeout=timeout)
            if item is None:
                break
            items.append(item)
        return items
    
    def empty(self) -> bool:
        """Check if queue is empty."""
        return self._queue.empty()
    
    def qsize(self) -> int:
        """Get approximate queue size."""
        return self._queue.qsize()
    
    def clear(self):
        """Clear all items from the queue."""
        while not self.empty():
            try:
                self._queue.get_nowait()
            except queue.Empty:
                break


class EventBus:
    """
    Simple event bus for decoupled communication between components.
    
    Allows components to subscribe to events and publish events
    without direct coupling.
    """
    
    def __init__(self):
        """Initialize the event bus."""
        self._subscribers: Dict[str, list] = {}
        self._lock = threading.Lock()
        self._logger = logging.getLogger(__name__)
    
    def subscribe(self, event_type: str, callback: Callable):
        """
        Subscribe to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            callback: Function to call when event occurs
        """
        with self._lock:
            if event_type not in self._subscribers:
                self._subscribers[event_type] = []
            self._subscribers[event_type].append(callback)
            self._logger.debug(f"Subscribed to event: {event_type}")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """
        Unsubscribe from an event type.
        
        Args:
            event_type: Type of event to unsubscribe from
            callback: Callback function to remove
        """
        with self._lock:
            if event_type in self._subscribers:
                try:
                    self._subscribers[event_type].remove(callback)
                    self._logger.debug(f"Unsubscribed from event: {event_type}")
                except ValueError:
                    self._logger.warning(f"Callback not found for event: {event_type}")
    
    def publish(self, event_type: str, data: Any = None):
        """
        Publish an event to all subscribers.
        
        Args:
            event_type: Type of event to publish
            data: Event data to send to subscribers
        """
        with self._lock:
            subscribers = self._subscribers.get(event_type, []).copy()
        
        for callback in subscribers:
            try:
                callback(data)
            except Exception as e:
                self._logger.error(f"Error in event callback for {event_type}: {e}")
    
    def clear_subscribers(self, event_type: Optional[str] = None):
        """
        Clear subscribers for an event type or all events.
        
        Args:
            event_type: Event type to clear (None for all)
        """
        with self._lock:
            if event_type is None:
                self._subscribers.clear()
                self._logger.debug("Cleared all event subscribers")
            else:
                self._subscribers.pop(event_type, None)
                self._logger.debug(f"Cleared subscribers for event: {event_type}")
