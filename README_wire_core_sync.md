# E3.series Wire Core Synchronization Script

## Overview

The Wire Core Synchronization script (`e3_wire_core_sync.py`) copies the "Wire number" attribute from net segments to the "Wire Number (Core)" core property for all connections in an E3.series project. This ensures that core properties are synchronized with the wire numbering system.

## Features

- **Automatic Connection Processing**: Processes all connections in the project
- **Wire Number Extraction**: Reads wire numbers from connected net segments
- **Core Property Setting**: Sets the "Wire Number (Core)" core property on connections
- **Multiple API Method Support**: Tries different E3 API methods for maximum compatibility
- **Comprehensive Logging**: Detailed logging of all operations and results
- **Error Handling**: Robust error handling with detailed error reporting
- **E3 Instance Selection**: Supports connecting to specific E3 instances

## Usage

### Standalone Execution

```bash
python lib/e3_wire_core_sync.py
```

### Integration with Other Scripts

```python
from lib.e3_wire_core_sync import WireCoreSynchronizer

# Create synchronizer instance
synchronizer = WireCoreSynchronizer()

# Run synchronization
success = synchronizer.run()

if success:
    print("Wire core synchronization completed successfully")
else:
    print("Wire core synchronization failed")
```

### With Specific E3 Instance

```python
from lib.e3_wire_core_sync import WireCoreSynchronizer

# Connect to specific E3 instance by PID
synchronizer = WireCoreSynchronizer(e3_pid=1234)
success = synchronizer.run()
```

## How It Works

### Process Flow

1. **E3 Connection**: Connects to E3.series using the connection manager
2. **Connection Discovery**: Gets all connection IDs from the project
3. **Net Segment Analysis**: For each connection, retrieves connected net segments
4. **Wire Number Extraction**: Reads the "Wire number" attribute from net segments
5. **Core Property Update**: Sets the "Wire Number (Core)" core property on the connection
6. **Result Reporting**: Logs detailed results including counts of updated, skipped, and failed connections

### API Methods Used

The script uses the correct E3 DTM API approach for setting core properties:

1. `e3Connection.GetCoreIds()` - Get all core IDs for the connection
2. `e3Pin.SetId(core_id)` - Set the active core (cores are handled as pin objects)
3. `e3Pin.SetAttributeValue("Wire Number (Core)", wire_number)` - Set the core attribute

### Connection Processing Logic

For each connection:
1. Get all connected net segment IDs using `e3Connection.GetNetSegmentIds()`
2. For each net segment, retrieve the wire number using `e3NetSegment.GetAttributeValue("Wire number")`
3. Use the first valid wire number found (all net segments in a connection should have the same wire number)
4. Get all core IDs for the connection using `e3Connection.GetCoreIds()`
5. For each core, set the "Wire Number (Core)" attribute using `e3Pin.SetAttributeValue()`

### Error Handling

The script handles various scenarios:
- Connections with no net segments (skipped)
- Net segments with no wire numbers (skipped)
- Connections with no cores (skipped, not an error)
- Cores where attribute setting fails (logged as errors)
- Invalid core IDs (filtered out)
- Connection and COM errors

## Output and Logging

### Console Output

The script provides real-time feedback:
- Connection count and processing progress
- Individual connection updates
- Summary statistics (updated, skipped, errors)

### Log File Output

Detailed logging includes:
- E3 connection status
- Individual connection processing details
- API method success/failure information
- Error details and troubleshooting information

### Example Output

```
INFO - Starting wire core synchronization process
INFO - Successfully connected to E3 application using e3series library
INFO - E3 reports 150 connections
INFO - Processing 150 connections
INFO - Updated 2 cores for connection 1001 with wire number '101A'
INFO - Updated 1 cores for connection 1002 with wire number '101B'
DEBUG - Connection 1003 has no wire number, skipping
INFO - Wire core synchronization completed: 145 updated, 3 skipped, 2 errors
INFO - Wire core synchronization completed successfully
```

## Requirements

### Software Requirements

- E3.series (any recent version)
- Python 3.7+
- Required Python packages:
  - `e3series` (PyPI package)
  - `pythoncom` (part of pywin32)

### E3 Project Requirements

- E3.series project must be open
- Connections must exist in the project
- Wire numbers should be assigned to net segments (from wire numbering script)

## Integration with Other Scripts

This script is designed to work as part of the E3 automation workflow:

1. **Wire Numbering**: Run `e3_wire_numbering.py` first to assign wire numbers
2. **Wire Core Sync**: Run `e3_wire_core_sync.py` to sync core properties
3. **Device Designation**: Run `e3_device_designation.py` for device naming
4. **Terminal Pin Names**: Run `e3_terminal_pin_names.py` for terminal updates

## Technical Details

### E3 API Methods Used

- `e3Job.GetAllConnectionIds()` - Get all connection IDs
- `e3Connection.SetId()` - Set active connection
- `e3Connection.GetNetSegmentIds()` - Get net segment IDs for connection
- `e3Connection.GetCoreIds()` - Get core IDs for connection
- `e3Pin.SetId()` - Set active core (cores are handled as pin objects)
- `e3Pin.SetAttributeValue()` - Set core attribute value
- `e3NetSegment.SetId()` - Set active net segment
- `e3NetSegment.GetAttributeValue()` - Get wire number attribute

### COM Object Management

The script properly manages COM objects:
- Initializes COM using `pythoncom.CoInitialize()`
- Creates E3 API objects through the connection manager
- Cleans up objects in the finally block
- Uninitializes COM using `pythoncom.CoUninitialize()`

## Troubleshooting

### Common Issues

1. **No connections found**
   - Verify connections exist in the project
   - Check that the project is properly loaded in E3

2. **Core property setting fails**
   - Verify that "Wire Number (Core)" is a valid core attribute name in your E3 project
   - Check that the attribute exists in the E3 attribute definitions
   - Ensure cores have the attribute defined (may need to be added in E3 configuration)
   - Review log output for specific API error messages

3. **Wire numbers not found**
   - Run the wire numbering script first to assign wire numbers
   - Verify that net segments have the "Wire number" attribute set

4. **Connection to E3 fails**
   - Ensure E3.series is running with a project open
   - Check that the e3series Python package is installed
   - Verify no other scripts are currently connected to E3

### Debug Mode

For additional debugging information, modify the logging level:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Configuration

The script uses the same configuration system as other E3 automation scripts:
- Connection timeout settings
- Retry attempts
- Logging configuration

Configuration is loaded from `resources/config/config.json`.

## Author

E3 Automation Team  
Date: 2025-01-27
