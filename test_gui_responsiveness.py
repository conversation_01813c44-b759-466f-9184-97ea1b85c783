#!/usr/bin/env python3
"""
Test script to verify GUI responsiveness improvements.
"""

import sys
import os
import time
import threading

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.abspath(__file__))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_sync_gui_update_config():
    """Test that the synchronous GUI update configuration loads correctly."""
    try:
        from apps.publish.config.configuration_manager import ConfigurationManager
        
        config_manager = ConfigurationManager()
        config = config_manager.load_app_config()
        
        threading_config = config.get('threading', {})
        
        print("=== Synchronous GUI Update Configuration Test ===")
        print(f"✓ sync_gui_update_interval_ms: {threading_config.get('sync_gui_update_interval_ms', 'NOT SET')}")
        print(f"✓ disable_for_e3_operations: {threading_config.get('disable_for_e3_operations', 'NOT SET')}")
        
        # Verify the interval is reasonable
        interval = threading_config.get('sync_gui_update_interval_ms', 50)
        if 10 <= interval <= 200:
            print(f"✓ GUI update interval ({interval}ms) is within reasonable range")
        else:
            print(f"⚠ GUI update interval ({interval}ms) may be too fast or slow")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_progress_callback_mechanism():
    """Test the progress callback mechanism."""
    try:
        from apps.publish.services.publish_service import PublishService
        from apps.publish.gui.communication import ProgressUpdate
        
        print("\n=== Progress Callback Mechanism Test ===")
        
        # Create a mock publish service
        service = PublishService()
        
        # Test progress callback setting
        progress_updates = []
        
        def mock_progress_callback(current, total, message, step_name):
            progress_updates.append((current, total, message, step_name))
        
        service.set_progress_callback(mock_progress_callback)
        
        # Test progress reporting
        service._report_progress(1, 5, "Test message", "Test step")
        service._report_progress(2, 5, "Another message", "Another step")
        
        if len(progress_updates) == 2:
            print("✓ Progress callback mechanism working correctly")
            print(f"  - Received {len(progress_updates)} progress updates")
            print(f"  - First update: {progress_updates[0]}")
            print(f"  - Second update: {progress_updates[1]}")
        else:
            print(f"✗ Expected 2 progress updates, got {len(progress_updates)}")
            return False
        
        # Test callback clearing
        service.set_progress_callback(None)
        service._report_progress(3, 5, "Should not be received", "Test")
        
        if len(progress_updates) == 2:
            print("✓ Progress callback clearing works correctly")
        else:
            print(f"✗ Progress callback not cleared properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Progress callback test failed: {e}")
        return False

def simulate_long_operation():
    """Simulate a long-running operation for testing."""
    print("\n=== Long Operation Simulation ===")
    print("Simulating a 3-second operation with progress updates...")
    
    progress_updates = []
    
    def progress_callback(current, total, message, step_name):
        progress_updates.append((current, total, message, step_name))
        print(f"Progress: {current}/{total} - {step_name}: {message}")
    
    # Simulate progress updates
    total_steps = 10
    for i in range(total_steps):
        progress_callback(i + 1, total_steps, f"Processing item {i + 1}", f"Step {i + 1}")
        time.sleep(0.3)  # Simulate work
    
    print(f"✓ Simulation completed with {len(progress_updates)} progress updates")
    return len(progress_updates) == total_steps

def test_gui_update_timing():
    """Test GUI update timing calculations."""
    print("\n=== GUI Update Timing Test ===")
    
    # Test different update intervals
    intervals = [25, 50, 100, 200]
    
    for interval in intervals:
        updates_per_second = 1000 / interval
        print(f"✓ {interval}ms interval = {updates_per_second:.1f} updates/second")
        
        if updates_per_second > 40:
            print(f"  ⚠ High frequency - may impact performance")
        elif updates_per_second < 5:
            print(f"  ⚠ Low frequency - may feel unresponsive")
        else:
            print(f"  ✓ Good balance of responsiveness and performance")
    
    return True

def main():
    """Run all tests."""
    print("Testing GUI Responsiveness Improvements")
    print("=" * 50)
    
    success = True
    
    success &= test_sync_gui_update_config()
    success &= test_progress_callback_mechanism()
    success &= simulate_long_operation()
    success &= test_gui_update_timing()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ All tests passed!")
        print("✓ GUI responsiveness improvements are properly implemented")
        print("✓ Synchronous operations should now maintain GUI responsiveness")
        print("\nKey improvements:")
        print("  • Periodic GUI updates during synchronous operations")
        print("  • Configurable update intervals")
        print("  • Proper cleanup on window close")
        print("  • Progress feedback for synchronous operations")
    else:
        print("✗ Some tests failed")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
