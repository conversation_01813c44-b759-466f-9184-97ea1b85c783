"""
PDF Printing Utilities Module

This module provides utility functions for the PDF Section Printing Application.
"""

import os
import sys
import logging
import subprocess
import tempfile
import urllib.request
import zipfile
import shutil
from typing import Optional

def get_sumatra_path() -> Optional[str]:
    """
    Get the path to SumatraPDF executable.
    
    Returns:
        Optional[str]: The path to SumatraPDF executable, or None if not found
    """
    # Check common installation paths
    paths = [
        r"C:\Program Files\SumatraPDF\SumatraPDF.exe",
        r"C:\Program Files (x86)\SumatraPDF\SumatraPDF.exe",
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin", "SumatraPDF.exe")
    ]
    
    for path in paths:
        if os.path.exists(path):
            return path
    
    return None

def download_sumatra_pdf() -> Optional[str]:
    """
    Download and install SumatraPDF to the bin directory.
    
    Returns:
        Optional[str]: The path to SumatraPDF executable, or None if download failed
    """
    try:
        # Create bin directory if it doesn't exist
        bin_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin")
        os.makedirs(bin_dir, exist_ok=True)
        
        # Download SumatraPDF
        logging.info("Downloading SumatraPDF...")
        download_url = "https://www.sumatrapdfreader.org/dl/rel/3.4.6/SumatraPDF-3.4.6-64.zip"
        zip_path = os.path.join(tempfile.gettempdir(), "SumatraPDF.zip")
        
        urllib.request.urlretrieve(download_url, zip_path)
        
        # Extract SumatraPDF
        logging.info("Extracting SumatraPDF...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(bin_dir)
        
        # Clean up
        os.remove(zip_path)
        
        # Return the path to SumatraPDF executable
        sumatra_path = os.path.join(bin_dir, "SumatraPDF.exe")
        if os.path.exists(sumatra_path):
            logging.info(f"SumatraPDF installed to {sumatra_path}")
            return sumatra_path
        else:
            logging.error("Failed to install SumatraPDF")
            return None
    except Exception as e:
        logging.error(f"Error downloading SumatraPDF: {e}")
        return None

def ensure_sumatra_pdf() -> Optional[str]:
    """
    Ensure SumatraPDF is installed and return the path to the executable.
    
    Returns:
        Optional[str]: The path to SumatraPDF executable, or None if not available
    """
    sumatra_path = get_sumatra_path()
    if sumatra_path:
        return sumatra_path
    
    # Try to download SumatraPDF
    return download_sumatra_pdf()
