"""
Data validation utilities and custom exception classes.

This module contains validation functions and exception classes
for data validation throughout the publishing system.
"""

from typing import List, Any, Optional, Tuple
import re
import os
import string


class ValidationError(Exception):
    """Exception raised when data validation fails."""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        """
        Initialize validation error.
        
        Args:
            message: Error message
            field: Field name that failed validation
            value: Value that failed validation
        """
        super().__init__(message)
        self.field = field
        self.value = value
    
    def __str__(self):
        """String representation of the error."""
        if self.field:
            return f"Validation error in field '{self.field}': {super().__str__()}"
        return super().__str__()


class ConfigurationError(Exception):
    """Exception raised when configuration is invalid."""
    
    def __init__(self, message: str, config_key: str = None):
        """
        Initialize configuration error.
        
        Args:
            message: Error message
            config_key: Configuration key that failed
        """
        super().__init__(message)
        self.config_key = config_key


class PublishError(Exception):
    """Base exception for publishing operations."""
    pass


class E3ConnectionError(PublishError):
    """Exception raised when E3 Series connection fails."""
    pass


class ExportError(PublishError):
    """Exception raised when export operations fail."""
    pass


def validate_gss_parent(gss_parent: str) -> List[str]:
    """
    Validate GSS parent number format.
    
    Args:
        gss_parent: GSS parent number to validate
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not gss_parent:
        errors.append("GSS Parent number is required")
        return errors
    
    gss_parent = gss_parent.strip()
    if not gss_parent:
        errors.append("GSS Parent number cannot be empty or whitespace")
        return errors
    
    # Check for valid characters (alphanumeric, hyphens, underscores)
    if not re.match(r'^[A-Za-z0-9\-_]+$', gss_parent):
        errors.append("GSS Parent number contains invalid characters (only letters, numbers, hyphens, and underscores allowed)")
    
    # Check length constraints
    if len(gss_parent) < 3:
        errors.append("GSS Parent number must be at least 3 characters long")
    elif len(gss_parent) > 50:
        errors.append("GSS Parent number must be 50 characters or less")
    
    # Check for common patterns that might indicate errors
    if gss_parent.startswith('-') or gss_parent.endswith('-'):
        errors.append("GSS Parent number cannot start or end with a hyphen")
    
    if '--' in gss_parent:
        errors.append("GSS Parent number cannot contain consecutive hyphens")
    
    return errors


def validate_serial_number(serial_number: str) -> List[str]:
    """
    Validate serial number format.
    
    Args:
        serial_number: Serial number to validate
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not serial_number:
        errors.append("Serial number is required")
        return errors
    
    serial_number = serial_number.strip()
    if not serial_number:
        errors.append("Serial number cannot be empty or whitespace")
        return errors
    
    # Check for valid characters (alphanumeric, hyphens, underscores)
    if not re.match(r'^[A-Za-z0-9\-_]+$', serial_number):
        errors.append("Serial number contains invalid characters (only letters, numbers, hyphens, and underscores allowed)")
    
    # Check length constraints
    if len(serial_number) < 1:
        errors.append("Serial number must be at least 1 character long")
    elif len(serial_number) > 50:
        errors.append("Serial number must be 50 characters or less")
    
    # Check for common patterns that might indicate errors
    if serial_number.startswith('-') or serial_number.endswith('-'):
        errors.append("Serial number cannot start or end with a hyphen")
    
    return errors


def validate_file_path(file_path: str) -> List[str]:
    """
    Validate file path format and accessibility.
    
    Args:
        file_path: File path to validate
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not file_path:
        errors.append("File path is required")
        return errors
    
    file_path = file_path.strip()
    if not file_path:
        errors.append("File path cannot be empty or whitespace")
        return errors
    
    # Check for invalid characters in Windows paths (excluding colon for drive letters)
    # Split path to handle drive letters properly
    path_parts = os.path.splitdrive(file_path)
    path_without_drive = path_parts[1] if len(path_parts) > 1 else file_path
    
    invalid_chars = '<>"|?*'
    if any(char in path_without_drive for char in invalid_chars):
        errors.append(f"File path contains invalid characters: {invalid_chars}")
    
    # Check path length (Windows MAX_PATH limitation)
    if len(file_path) > 260:
        errors.append("File path is too long (maximum 260 characters on Windows)")
    
    # Check if path exists and is accessible
    try:
        if not os.path.exists(file_path):
            errors.append(f"File path does not exist: {file_path}")
        elif not os.path.isdir(file_path):
            errors.append(f"Path is not a directory: {file_path}")
        elif not os.access(file_path, os.W_OK):
            errors.append(f"Directory is not writable: {file_path}")
    except (OSError, PermissionError) as e:
        errors.append(f"Cannot access file path: {str(e)}")
    
    return errors


def validate_model_name(model_name: str, available_models: List[str] = None) -> List[str]:
    """
    Validate model name.
    
    Args:
        model_name: Model name to validate
        available_models: List of available model names
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not model_name:
        errors.append("Model name is required")
        return errors
    
    model_name = model_name.strip()
    if not model_name:
        errors.append("Model name cannot be empty or whitespace")
        return errors
    
    # Check if model is in available models list
    if available_models is not None and model_name not in available_models:
        errors.append(f"Model '{model_name}' is not in the list of available models")
    
    return errors


def validate_series_count(series_count: int) -> List[str]:
    """
    Validate series count for batch publishing.
    
    Args:
        series_count: Number of items in series
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not isinstance(series_count, int):
        errors.append("Series count must be an integer")
        return errors
    
    if series_count < 1:
        errors.append("Series count must be at least 1")
    elif series_count > 1000:
        errors.append("Series count cannot exceed 1000")
    
    return errors


def validate_export_formats(export_formats: List[str]) -> List[str]:
    """
    Validate export format list.
    
    Args:
        export_formats: List of export formats
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not export_formats:
        errors.append("At least one export format must be specified")
        return errors
    
    valid_formats = ["PDF", "DXF"]
    invalid_formats = [fmt for fmt in export_formats if fmt.upper() not in valid_formats]
    
    if invalid_formats:
        errors.append(f"Invalid export formats: {', '.join(invalid_formats)}. Valid formats: {', '.join(valid_formats)}")
    
    return errors


def validate_template_path(template_path: str) -> List[str]:
    """
    Validate Word template file path.
    
    Args:
        template_path: Path to Word template file
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not template_path:
        errors.append("Template path is required")
        return errors
    
    template_path = template_path.strip()
    if not template_path:
        errors.append("Template path cannot be empty or whitespace")
        return errors
    
    # Check if file exists
    if not os.path.exists(template_path):
        errors.append(f"Template file does not exist: {template_path}")
        return errors
    
    # Check if it's a file (not directory)
    if not os.path.isfile(template_path):
        errors.append(f"Template path is not a file: {template_path}")
        return errors
    
    # Check file extension
    valid_extensions = ['.dotx', '.docx', '.dot', '.doc']
    file_ext = os.path.splitext(template_path)[1].lower()
    if file_ext not in valid_extensions:
        errors.append(f"Template file must be a Word document ({', '.join(valid_extensions)})")
    
    # Check file accessibility
    try:
        if not os.access(template_path, os.R_OK):
            errors.append(f"Template file is not readable: {template_path}")
    except (OSError, PermissionError) as e:
        errors.append(f"Cannot access template file: {str(e)}")
    
    return errors


def validate_email_address(email: str) -> List[str]:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    if not email:
        return errors  # Email is optional in most cases
    
    email = email.strip()
    if not email:
        return errors
    
    # Basic email regex pattern
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        errors.append("Invalid email address format")
    
    return errors


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing or replacing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename safe for use on Windows
    """
    if not filename:
        return "untitled"
    
    # Remove or replace invalid characters
    invalid_chars = '<>:"|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Ensure filename is not empty
    if not filename:
        filename = "untitled"
    
    # Truncate if too long (leave room for extension)
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename


def validate_and_sanitize_project_data(project_data: dict) -> Tuple[dict, List[str], List[str]]:
    """
    Validate and sanitize project data dictionary.
    
    Args:
        project_data: Dictionary containing project data
        
    Returns:
        Tuple of (sanitized_data, errors, warnings)
    """
    errors = []
    warnings = []
    sanitized_data = project_data.copy()
    
    # Validate and sanitize each field
    if 'gss_parent' in sanitized_data:
        gss_errors = validate_gss_parent(sanitized_data['gss_parent'])
        errors.extend(gss_errors)
    
    if 'serial_number' in sanitized_data:
        serial_errors = validate_serial_number(sanitized_data['serial_number'])
        errors.extend(serial_errors)
    
    if 'folder_path' in sanitized_data:
        path_errors = validate_file_path(sanitized_data['folder_path'])
        errors.extend(path_errors)
    
    # Sanitize string fields
    string_fields = ['customer', 'location', 'title', 'sales_order']
    for field in string_fields:
        if field in sanitized_data and sanitized_data[field]:
            original = sanitized_data[field]
            sanitized = sanitize_filename(original)
            if original != sanitized:
                warnings.append(f"Field '{field}' was sanitized for file system compatibility")
                sanitized_data[field] = sanitized
    
    return sanitized_data, errors, warnings