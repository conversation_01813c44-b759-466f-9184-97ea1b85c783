"""
Export service for PDF and DXF export operations.

This module contains the ExportService class that handles exporting
projects to various formats.
"""

import logging
import os
from typing import Optional, List
from ..integrations.e3_client import E3Client, E3ConnectionError, E3OperationError


class ExportService:
    """Service for handling PDF and DXF export operations."""
    
    def __init__(self, e3_client: Optional[E3Client] = None, config_manager=None):
        """
        Initialize the export service.

        Args:
            e3_client: Client for E3 Series integration
            config_manager: Configuration manager for settings
        """
        self.e3_client = e3_client
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # Load export configuration
        self.config = {}
        if config_manager:
            try:
                app_config = config_manager.load_app_config()
                self.config = app_config.get('services', {}).get('export_service', {})
                self.export_config = app_config.get('publishing', {}).get('export', {})
            except:
                self.export_config = {}
        else:
            self.export_config = {}

        # Set configuration values with defaults
        self.parallel_exports = self.config.get('parallel_exports', False)
        self.cleanup_temp_files = self.config.get('cleanup_temp_files', True)
        self.pdf_quality = self.export_config.get('pdf_quality', 'high')
        self.dxf_version = self.export_config.get('dxf_version', '2018')
        
    def export_to_pdf(self, project_data, output_path: str) -> bool:
        """
        Export project to PDF format.
        
        Args:
            project_data: Project data containing export information
            output_path: Path for output PDF file
            
        Returns:
            True if export succeeded, False otherwise
            
        Raises:
            ExportError: If export operation fails
        """
        if not self.e3_client:
            error_msg = "E3 client not available for PDF export"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "pdf_export")
            
        try:
            self.logger.info(f"Starting PDF export to: {output_path}")
            
            # Validate project data
            if not project_data:
                raise ExportError("Project data is required for PDF export", "pdf_export")
            
            # Ensure output directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    self.logger.debug(f"Created output directory: {output_dir}")
                except Exception as e:
                    raise ExportError(f"Failed to create output directory: {e}", "pdf_export")
            
            # Perform PDF export using E3 client
            success = self.e3_client.export_pdf(output_path)
            
            if success:
                self.logger.info(f"PDF export completed successfully: {output_path}")
                return True
            else:
                error_msg = f"PDF export failed for unknown reason"
                self.logger.error(error_msg)
                raise ExportError(error_msg, "pdf_export")
                
        except E3ConnectionError as e:
            error_msg = f"E3 connection error during PDF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "pdf_export", e)
        except E3OperationError as e:
            error_msg = f"E3 operation error during PDF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "pdf_export", e)
        except ExportError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error during PDF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "pdf_export", e)
        
    def export_to_dxf(self, project_data, output_dir: str) -> List[str]:
        """
        Export dataplates to DXF format.
        
        Args:
            project_data: Project data containing export information
            output_dir: Directory path for output DXF files
            
        Returns:
            List of exported DXF file paths
            
        Raises:
            ExportError: If export operation fails
        """
        if not self.e3_client:
            error_msg = "E3 client not available for DXF export"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "dxf_export")
            
        try:
            self.logger.info(f"Starting DXF export to directory: {output_dir}")
            
            # Validate project data
            if not project_data:
                raise ExportError("Project data is required for DXF export", "dxf_export")
                
            if not hasattr(project_data, 'gss_parent') or not project_data.gss_parent:
                raise ExportError("GSS parent number is required for DXF export", "dxf_export")
                
            if not hasattr(project_data, 'serial_number') or not project_data.serial_number:
                raise ExportError("Serial number is required for DXF export", "dxf_export")
            
            # Ensure output directory exists
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    self.logger.debug(f"Created output directory: {output_dir}")
                except Exception as e:
                    raise ExportError(f"Failed to create output directory: {e}", "dxf_export")
            
            # Perform DXF export using E3 client
            exported_files = self.e3_client.export_dxf_dataplates(
                output_dir, 
                project_data.gss_parent, 
                project_data.serial_number
            )
            
            if exported_files:
                self.logger.info(f"DXF export completed successfully. Exported {len(exported_files)} files")
                for file_path in exported_files:
                    self.logger.debug(f"Exported DXF file: {file_path}")
            else:
                self.logger.warning("No DXF files were exported (no dataplate sheets found)")
                
            return exported_files
                
        except E3ConnectionError as e:
            error_msg = f"E3 connection error during DXF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "dxf_export", e)
        except E3OperationError as e:
            error_msg = f"E3 operation error during DXF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "dxf_export", e)
        except ExportError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error during DXF export: {e}"
            self.logger.error(error_msg)
            raise ExportError(error_msg, "dxf_export", e)
    
    def export_project(self, project_data, output_folder: str, export_formats: List[str] = None) -> 'ExportResult':
        """
        Export project in multiple formats.
        
        Args:
            project_data: Project data containing export information
            output_folder: Base folder for all exports
            export_formats: List of formats to export ('pdf', 'dxf')
            
        Returns:
            ExportResult containing success status and file paths
        """
        if export_formats is None:
            export_formats = ['pdf', 'dxf']
            
        result = ExportResult()
        
        try:
            self.logger.info(f"Starting multi-format export to: {output_folder}")
            
            # Export to PDF if requested
            if 'pdf' in export_formats:
                try:
                    pdf_filename = f"{project_data.gss_parent} {project_data.serial_number}.pdf"
                    pdf_path = os.path.join(output_folder, pdf_filename)
                    
                    success = self.export_to_pdf(project_data, pdf_path)
                    if success:
                        result.pdf_files.append(pdf_path)
                        result.success_count += 1
                    else:
                        result.errors.append(f"PDF export failed: {pdf_path}")
                        
                except Exception as e:
                    error_msg = f"PDF export error: {e}"
                    result.errors.append(error_msg)
                    self.logger.error(error_msg)
            
            # Export to DXF if requested
            if 'dxf' in export_formats:
                try:
                    dxf_dir = os.path.join(os.path.dirname(output_folder), "DataPlates")
                    dxf_files = self.export_to_dxf(project_data, dxf_dir)
                    
                    if dxf_files:
                        result.dxf_files.extend(dxf_files)
                        result.success_count += len(dxf_files)
                    else:
                        result.warnings.append("No DXF files exported (no dataplate sheets found)")
                        
                except Exception as e:
                    error_msg = f"DXF export error: {e}"
                    result.errors.append(error_msg)
                    self.logger.error(error_msg)
            
            # Determine overall success
            result.success = len(result.errors) == 0
            
            if result.success:
                self.logger.info(f"Multi-format export completed successfully with {result.success_count} files")
            else:
                self.logger.warning(f"Multi-format export completed with {len(result.errors)} errors")
                
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error during multi-format export: {e}"
            self.logger.error(error_msg)
            result.errors.append(error_msg)
            result.success = False
            return result


class ExportResult:
    """Result of export operations."""
    
    def __init__(self):
        """Initialize export result."""
        self.success = False
        self.success_count = 0
        self.pdf_files = []
        self.dxf_files = []
        self.errors = []
        self.warnings = []
    
    def __str__(self):
        """String representation of export result."""
        status = "SUCCESS" if self.success else "FAILED"
        return f"ExportResult({status}, {self.success_count} files, {len(self.errors)} errors)"


class ExportError(Exception):
    """Exception raised when export operations fail."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None):
        """
        Initialize export error.
        
        Args:
            message: Error message
            operation: Name of the export operation that failed
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.operation = operation
        self.original_error = original_error
    
    def __str__(self):
        base_msg = self.message
        if self.operation:
            base_msg = f"Export operation '{self.operation}' failed: {base_msg}"
        if self.original_error:
            base_msg += f" (Original error: {self.original_error})"
        return base_msg