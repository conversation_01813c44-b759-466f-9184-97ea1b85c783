<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Parameters" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Arrow Style</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows2.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor825"></a><a name="kanchor826"></a><a name="kanchor827"></a><a name="kanchor828"></a><a name="kanchor829"></a>Arrow Style
		</h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">Integer</span> <i>arrowstyles</i></p>
            <h4>Description</h4>
            <p>Parameter represents a arrow style value as an integer.</p>
            <h4>Possible Values</h4>
            <table style="width: 100%;border-top-left-radius: 1px;border-top-right-radius: 1px;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;border-bottom-style: solid;border-bottom-width: 1px;mc-table-style: url('../../../Resources/TableStyles/Rows2.css');margin-left: auto;margin-right: 0;" class="TableStyle-Rows2" cellspacing="0">
                <col style="width: 169px;" class="TableStyle-Rows2-Column-Column1" />
                <col class="TableStyle-Rows2-Column-Column2" style="width: 333px;" />
                <col class="TableStyle-Rows2-Column-Column3" />
                <tbody>
                    <tr class="TableStyle-Rows2-Body-Body2">
                        <td style="font-weight: bold;" class="TableStyle-Rows2-BodyE-Column1-Body2">Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows2-BodyE-Column2-Body2">Arrow Style</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows2-BodyD-Column3-Body2">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body1">
                        <td class="TableStyle-Rows2-BodyE-Column1-Body1">0</td>
                        <td class="TableStyle-Rows2-BodyE-Column2-Body1">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowFilled.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyD-Column3-Body1">Filled</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body2">
                        <td class="TableStyle-Rows2-BodyE-Column1-Body2">1</td>
                        <td class="TableStyle-Rows2-BodyE-Column2-Body2">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowClosed.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyD-Column3-Body2">Closed</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body1">
                        <td class="TableStyle-Rows2-BodyE-Column1-Body1">2</td>
                        <td class="TableStyle-Rows2-BodyE-Column2-Body1">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowOpen.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyD-Column3-Body1">Open</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body2">
                        <td class="TableStyle-Rows2-BodyE-Column1-Body2">3</td>
                        <td class="TableStyle-Rows2-BodyE-Column2-Body2">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowDiagonal.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyD-Column3-Body2">Diagonal</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body1">
                        <td class="TableStyle-Rows2-BodyE-Column1-Body1">4</td>
                        <td class="TableStyle-Rows2-BodyE-Column2-Body1">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowLine.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyD-Column3-Body1">Line</td>
                    </tr>
                    <tr class="TableStyle-Rows2-Body-Body2">
                        <td class="TableStyle-Rows2-BodyB-Column1-Body2">5</td>
                        <td class="TableStyle-Rows2-BodyB-Column2-Body2">
                            <img src="../../../Resources/Images/ArrowStyles/ArrowNone.png" />
                        </td>
                        <td class="TableStyle-Rows2-BodyA-Column3-Body2">None</td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>Arrow styles are used by dimension items for displaying the scope of the dimension.</p>
            <h4>Version Information</h4>
            <p>Introduced in v2009-8.50.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="../../Classes/e3Dimension/GetArrowMode.htm">e3Dimension.GetArrowMode()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Dimension/SetArrowMode.htm">e3Dimension.SetArrowMode()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>