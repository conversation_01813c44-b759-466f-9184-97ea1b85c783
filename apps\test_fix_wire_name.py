#!/usr/bin/env python3
"""
Test script for FixWireName attribute functionality in wire numbering script.

This script demonstrates and tests the FixWireName attribute feature that allows
connections to be skipped during automatic wire number assignment.

Author: <PERSON>
Date: 2025-07-11
"""

import e3series
import logging
import sys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_fix_wire_name.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class FixWireNameTester:
    def __init__(self):
        self.app = None
        self.job = None
        self.connection = None
        self.net_segment = None
        self.net = None
        
    def connect_to_e3(self):
        """Connect to the open E3 application"""
        try:
            # Connect to the active E3.series application
            self.app = e3series.Application()
            self.job = self.app.CreateJobObject()
            self.connection = self.job.CreateConnectionObject()
            self.net_segment = self.job.CreateNetSegmentObject()
            self.net = self.job.CreateNetObject()
            logging.info("Successfully connected to E3 application using e3series library")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to E3: {e}")
            return False
    
    def get_net_segments_for_connection(self, connection_id):
        """Get all net segment IDs for a given connection"""
        try:
            self.connection.SetId(connection_id)
            net_segment_ids_result = self.connection.GetNetSegmentIds()

            if not net_segment_ids_result:
                return []

            actual_net_segments = []
            if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                _ = net_segment_ids_result[0]  # count (not used)
                net_segment_ids = net_segment_ids_result[1]

                if isinstance(net_segment_ids, tuple):
                    actual_net_segments = [nsid for nsid in net_segment_ids if nsid is not None and nsid != 0]
                else:
                    if net_segment_ids is not None and net_segment_ids != 0:
                        actual_net_segments = [net_segment_ids]

            return actual_net_segments

        except Exception as e:
            logging.error(f"Error getting net segments for connection {connection_id}: {e}")
            return []

    def has_fix_wire_name_attribute(self, connection_id):
        """Check if the net for this connection has the FixWireName attribute set"""
        try:
            self.connection.SetId(connection_id)
            net_id = self.connection.GetNetId()

            logging.info(f"Connection {connection_id} net ID: {net_id}")

            # Check if we got a valid net ID
            if net_id <= 0:
                logging.info(f"Connection {connection_id} has no valid net ID ({net_id}) - would process normally")
                return False, None

            # Set the net object to this net and check for FixWireName attribute
            self.net.SetId(net_id)
            fix_wire_name = self.net.GetAttributeValue("FixWireName")

            logging.info(f"Net {net_id}: FixWireName = '{fix_wire_name}'")

            # Check if the attribute exists and has a truthy value
            if fix_wire_name and str(fix_wire_name).strip().lower() not in ['', '0', 'false', 'no']:
                logging.info(f"Connection {connection_id} has FixWireName attribute set to '{fix_wire_name}' on net {net_id} - would be skipped")
                return True, fix_wire_name

            logging.info(f"Connection {connection_id} would be processed normally")
            return False, None

        except Exception as e:
            logging.error(f"Error checking FixWireName attribute for connection {connection_id}: {e}")
            return False, None

    def test_fix_wire_name_functionality(self):
        """Test the FixWireName attribute functionality"""
        try:
            # Get all connection IDs
            connection_ids_result = self.job.GetAllConnectionIds()
            if not connection_ids_result:
                logging.warning("No connections found in project")
                return

            # E3 API returns (count, tuple_of_ids)
            actual_connections = []
            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                count = connection_ids_result[0]
                connection_ids = connection_ids_result[1]

                logging.info(f"E3 reports {count} connections")

                if isinstance(connection_ids, tuple):
                    # Filter out None values
                    actual_connections = [cid for cid in connection_ids if cid is not None]
                else:
                    actual_connections = [connection_ids] if connection_ids is not None else []

            logging.info(f"Found {len(actual_connections)} valid connections to test")

            # Test each connection for FixWireName attribute
            fix_wire_name_count = 0
            regular_connections = 0

            for conn_id in actual_connections[:10]:  # Test first 10 connections
                if conn_id is None:
                    continue

                try:
                    self.connection.SetId(conn_id)
                    signal_name = self.connection.GetSignalName()
                    
                    has_fix_attr, attr_value = self.has_fix_wire_name_attribute(conn_id)
                    
                    if has_fix_attr:
                        fix_wire_name_count += 1
                        logging.info(f"Connection {conn_id} (Signal: '{signal_name}') - HAS FixWireName='{attr_value}' - WOULD BE SKIPPED")
                    else:
                        regular_connections += 1
                        logging.info(f"Connection {conn_id} (Signal: '{signal_name}') - No FixWireName attribute - WOULD BE PROCESSED")

                except Exception as e:
                    logging.error(f"Error testing connection {conn_id}: {e}")

            logging.info(f"\nTest Summary:")
            logging.info(f"- Connections with FixWireName attribute: {fix_wire_name_count}")
            logging.info(f"- Regular connections (would be processed): {regular_connections}")
            logging.info(f"- Total tested: {fix_wire_name_count + regular_connections}")

        except Exception as e:
            logging.error(f"Error during FixWireName testing: {e}")
    
    def run(self):
        """Main execution method"""
        logging.info("Starting FixWireName attribute test")
        
        if not self.connect_to_e3():
            logging.error("Failed to connect to E3. Make sure E3 is running with a project open.")
            return False
        
        try:
            self.test_fix_wire_name_functionality()
            logging.info("FixWireName attribute test completed successfully")
            return True
            
        except Exception as e:
            logging.error(f"Error during FixWireName test: {e}")
            return False
        
        finally:
            # Clean up E3.series objects
            self.app = None
            self.job = None
            self.connection = None
            self.net_segment = None

def main():
    """Main function"""
    tester = FixWireNameTester()
    success = tester.run()
    
    if success:
        print("FixWireName attribute test completed successfully!")
        print("Check the log file 'test_fix_wire_name.log' for details.")
    else:
        print("FixWireName attribute test failed. Check the log file for errors.")
        sys.exit(1)

if __name__ == "__main__":
    main()
