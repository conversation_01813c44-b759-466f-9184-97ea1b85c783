#!/usr/bin/env python3
"""
PDF Section Printing Application

This application automates the printing of technical documents with varying page sizes and formatting requirements.
It detects page types based on size and applies appropriate printer settings for each section.
"""

import os
import sys
import logging
import traceback

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import application modules
try:
    from lib.pdf_printing_logging import setup_logging, StatusLogger
    from lib.pdf_printing_ui import PDFSectionPrintingApp
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please make sure all required modules are installed.")
    print("Run 'pip install -r requirements.txt' to install dependencies.")
    sys.exit(1)

def main():
    """Main entry point for the application."""
    try:
        # Set up logging
        log_file = setup_logging()
        logging.info("Starting PDF Section Printing Application")

        # Create and run the application
        app = PDFSectionPrintingApp()
        app.protocol("WM_DELETE_WINDOW", app.on_closing)
        app.mainloop()

        logging.info("Application closed")
    except Exception as e:
        logging.error(f"Unhandled exception: {e}")
        logging.debug(traceback.format_exc())
        print(f"An error occurred: {e}")
        print(f"See log file for details: {log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
