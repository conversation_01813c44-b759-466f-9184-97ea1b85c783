"""
Unit tests for file operations utilities.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path

from apps.publish.utils.file_operations import FileOperations, FileOperationError


class TestFileOperations(unittest.TestCase):
    """Test cases for FileOperations class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.file_ops = FileOperations()
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Clean up temp directory
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_create_serial_folder_success(self):
        """Test successful serial folder creation."""
        serial = "12345"
        result_path = FileOperations.create_serial_folder(self.temp_dir, serial)
        
        expected_path = os.path.join(self.temp_dir, serial)
        self.assertEqual(result_path, expected_path)
        self.assertTrue(os.path.exists(result_path))
        self.assertTrue(os.path.isdir(result_path))
    
    def test_create_serial_folder_sanitizes_filename(self):
        """Test that serial folder creation sanitizes invalid characters."""
        serial = "12<>345"
        result_path = FileOperations.create_serial_folder(self.temp_dir, serial)
        
        expected_path = os.path.join(self.temp_dir, "12__345")
        self.assertEqual(result_path, expected_path)
        self.assertTrue(os.path.exists(result_path))
    
    def test_create_serial_folder_invalid_base_path(self):
        """Test serial folder creation with invalid base path."""
        with self.assertRaises(FileOperationError):
            FileOperations.create_serial_folder("", "12345")
    
    def test_create_serial_folder_empty_serial(self):
        """Test serial folder creation with empty serial."""
        with self.assertRaises(FileOperationError):
            FileOperations.create_serial_folder(self.temp_dir, "")
    
    def test_save_job_data_json_success(self):
        """Test successful JSON saving."""
        # Create mock project data
        project_data = MagicMock()
        project_data.gss_parent = "GSS123"
        project_data.serial_number = "12345"
        project_data.customer = "Test Customer"
        project_data.location = "Test Location"
        project_data.title = "Test Title"
        project_data.sales_order = "SO123"
        project_data.model = "TestModel"
        project_data.folder_path = "/test/path"
        
        output_path = os.path.join(self.temp_dir, "job_data.json")
        result = FileOperations.save_job_data_json(project_data, output_path)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))
        
        # Verify JSON content
        with open(output_path, 'r') as f:
            data = json.load(f)
        
        self.assertEqual(data['gss_parent'], "GSS123")
        self.assertEqual(data['serial_number'], "12345")
        self.assertIn('timestamp', data)
    
    def test_save_job_data_json_creates_directory(self):
        """Test that JSON saving creates output directory if needed."""
        project_data = MagicMock()
        project_data.gss_parent = "GSS123"
        project_data.serial_number = "12345"
        project_data.customer = "Test Customer"
        project_data.location = "Test Location"
        project_data.title = "Test Title"
        project_data.sales_order = "SO123"
        project_data.model = "TestModel"
        project_data.folder_path = "/test/path"
        
        nested_dir = os.path.join(self.temp_dir, "nested", "path")
        output_path = os.path.join(nested_dir, "job_data.json")
        
        result = FileOperations.save_job_data_json(project_data, output_path)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))
        self.assertTrue(os.path.exists(nested_dir))
    
    def test_validate_path_valid_existing_path(self):
        """Test path validation with valid existing path."""
        result = FileOperations.validate_path(self.temp_dir)
        self.assertTrue(result)
    
    def test_validate_path_empty_path(self):
        """Test path validation with empty path."""
        result = FileOperations.validate_path("")
        self.assertFalse(result)
    
    def test_validate_path_none_path(self):
        """Test path validation with None path."""
        result = FileOperations.validate_path(None)
        self.assertFalse(result)
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        test_cases = [
            ("normal_file.txt", "normal_file.txt"),
            ("file<>name.txt", "file__name.txt"),
            ("file:with|chars.txt", "file_with_chars.txt"),
            ("", "unnamed"),
            ("   .  ", "unnamed"),
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = FileOperations._sanitize_filename(input_name)
                self.assertEqual(result, expected)
    
    def test_ensure_directory_exists(self):
        """Test directory creation."""
        test_dir = os.path.join(self.temp_dir, "test_dir")
        result = FileOperations.ensure_directory_exists(test_dir)
        
        self.assertEqual(result, test_dir)
        self.assertTrue(os.path.exists(test_dir))
        self.assertTrue(os.path.isdir(test_dir))
    
    def test_copy_file_success(self):
        """Test successful file copying."""
        # Create source file
        source_path = os.path.join(self.temp_dir, "source.txt")
        with open(source_path, 'w') as f:
            f.write("test content")
        
        dest_path = os.path.join(self.temp_dir, "dest.txt")
        result = FileOperations.copy_file(source_path, dest_path)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(dest_path))
        
        with open(dest_path, 'r') as f:
            content = f.read()
        self.assertEqual(content, "test content")
    
    def test_copy_file_nonexistent_source(self):
        """Test file copying with nonexistent source."""
        source_path = os.path.join(self.temp_dir, "nonexistent.txt")
        dest_path = os.path.join(self.temp_dir, "dest.txt")
        
        with self.assertRaises(FileOperationError):
            FileOperations.copy_file(source_path, dest_path)
    
    def test_get_file_size(self):
        """Test getting file size."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        test_content = "Hello, World!"
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        size = FileOperations.get_file_size(test_file)
        self.assertEqual(size, len(test_content))
    
    def test_list_files_in_directory(self):
        """Test listing files in directory."""
        # Create test files
        test_files = ["file1.txt", "file2.pdf", "file3.txt"]
        for filename in test_files:
            filepath = os.path.join(self.temp_dir, filename)
            with open(filepath, 'w') as f:
                f.write("test")
        
        # Test listing all files
        all_files = FileOperations.list_files_in_directory(self.temp_dir)
        self.assertEqual(len(all_files), 3)
        
        # Test filtering by extension
        txt_files = FileOperations.list_files_in_directory(self.temp_dir, '.txt')
        self.assertEqual(len(txt_files), 2)
        
        pdf_files = FileOperations.list_files_in_directory(self.temp_dir, '.pdf')
        self.assertEqual(len(pdf_files), 1)


if __name__ == '__main__':
    unittest.main()