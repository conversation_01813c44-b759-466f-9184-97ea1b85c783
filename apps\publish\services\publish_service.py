"""
Core publishing service for orchestrating the publishing workflow.

This module contains the PublishService class that coordinates all aspects
of the project publishing process.
"""

import os
import json
import datetime
from typing import List, Optional, Dict, Any
import logging

from ..integrations.e3_client import E3Client, E3ConnectionError, E3OperationError
from ..integrations.report_generator import ReportGeneratorClient, ReportGeneratorError
from ..integrations.wire_core_sync import WireCoreSyncClient, WireCoreSyncError
from .export_service import ExportService, ExportError
from .manual_service import ManualService, ManualError
from ..utils.resource_manager import get_resource_manager, resource_monitoring
from ..utils.performance_monitor import timed_operation, progress_tracking, get_performance_monitor
from ..utils.series_generator import SeriesGenerator, SeriesGeneratorError
from typing import Callable


class PublishService:
    """Service for orchestrating the complete publishing workflow."""

    def __init__(self, e3_client: Optional[E3Client] = None,
                 export_service: Optional[ExportService] = None,
                 manual_service: Optional[ManualService] = None,
                 report_generator: Optional[ReportGeneratorClient] = None,
                 wire_core_sync_client = None,
                 config_manager = None):
        """
        Initialize the publish service.

        Args:
            e3_client: Client for E3 Series integration
            export_service: Service for handling exports
            manual_service: Service for manual creation
            report_generator: Client for report generation
            wire_core_sync_client: Client for wire core synchronization
            config_manager: Configuration manager for settings
        """
        self.e3_client = e3_client
        self.export_service = export_service
        self.manual_service = manual_service
        self.report_generator = report_generator
        self.wire_core_sync_client = wire_core_sync_client
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # Initialize series generator for proper serial number handling
        self.series_generator = SeriesGenerator()

        # Progress reporting
        self._progress_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Optional[Callable]):
        """
        Set a callback function for progress updates.

        Args:
            callback: Function to call with progress updates (current, total, message, step_name)
        """
        self._progress_callback = callback

    def _report_progress(self, current: int, total: int, message: str, step_name: str):
        """
        Report progress to the callback if set.

        Args:
            current: Current progress value
            total: Total progress value
            message: Progress message
            step_name: Name of current step
        """
        if self._progress_callback:
            try:
                self._progress_callback(current, total, message, step_name)
            except Exception as e:
                self.logger.warning(f"Error in progress callback: {e}")

    def publish_project(self, project_data, publish_config: 'PublishConfig') -> 'PublishResult':
        """
        Publish a single project.
        
        Args:
            project_data: Project data to publish
            publish_config: Publishing configuration options
            
        Returns:
            PublishResult containing operation results
            
        Raises:
            PublishError: If critical publishing operations fail
        """
        # Validate inputs first
        self._validate_publish_inputs(project_data, publish_config)
        
        result = PublishResult(serial_number=project_data.serial_number)
        
        with resource_monitoring(f"Publish Project: {project_data.gss_parent}-{project_data.serial_number}"):
            try:
                self.logger.info(f"Starting project publishing: {project_data.gss_parent} - {project_data.serial_number}")
                total_steps = 8  # Total number of main steps (added wire core sync)
                current_step = 0

                # Create output folder
                self._report_progress(current_step, total_steps, "Creating output folder", "Setup")
                output_folder = self._create_output_folder(project_data, publish_config.output_base_path)
                result.output_path = output_folder
                current_step += 1

                # Step 1: Update E3 project attributes
                self._report_progress(current_step, total_steps, "Updating E3 project attributes", "E3 Attributes")
                if self.e3_client:
                    try:
                        self.logger.info("Updating E3 project attributes...")
                        self.e3_client.update_attributes(project_data)
                        result.steps_completed.append("update_attributes")
                    except (E3ConnectionError, E3OperationError) as e:
                        error_msg = f"Failed to update E3 attributes: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_e3_errors:
                            raise PublishError(error_msg, "update_attributes", e)
                else:
                    result.warnings.append("E3 client not available - skipping attribute updates")
                current_step += 1

                # Step 2: Run wire core synchronization
                self._report_progress(current_step, total_steps, "Synchronizing wire core properties", "Wire Core Sync")
                run_wire_core_sync = getattr(publish_config, 'run_wire_core_sync', True)
                if self.wire_core_sync_client and run_wire_core_sync:
                    try:
                        self.logger.info("Running wire core synchronization...")
                        e3_pid = self.e3_client.get_process_id() if self.e3_client else None
                        success = self.wire_core_sync_client.run_wire_core_sync(e3_pid)
                        if success:
                            result.steps_completed.append("wire_core_sync")
                            self.logger.info("Wire core synchronization completed successfully")
                        else:
                            result.warnings.append("Wire core synchronization failed")
                    except Exception as e:
                        error_msg = f"Failed to run wire core synchronization: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_e3_errors:
                            raise PublishError(error_msg, "wire_core_sync", e)
                else:
                    if not self.wire_core_sync_client:
                        result.warnings.append("Wire core sync client not available - skipping wire core synchronization")
                    elif not run_wire_core_sync:
                        self.logger.info("Wire core synchronization disabled in configuration - skipping")
                current_step += 1

                # Step 3: Update BOM reports and additional reports
                self._report_progress(current_step, total_steps, "Updating reports", "Reports")
                if self.report_generator and publish_config.update_bom_reports:
                    try:
                        self.logger.info("Updating BOM reports and additional reports...")
                        e3_pid = self.e3_client.get_process_id() if self.e3_client else None
                        if e3_pid:
                            # Use configurable report lists with safe attribute access
                            bom_reports = getattr(publish_config, 'bom_reports', None) or ["PHOENIX BOM", "Simplified BOM"]
                            additional_reports = getattr(publish_config, 'additional_reports', None) or ["Field Cable List", "Field Cable List without Cores"]

                            bom_results = self.report_generator.update_bom_reports(
                                e3_pid,
                                report_names=bom_reports,
                                additional_reports=additional_reports
                            )
                            successful_reports = sum(1 for success in bom_results.values() if success)
                            result.bom_reports_updated = successful_reports
                            result.steps_completed.append("update_bom_reports")

                            total_reports = len(bom_reports) + len(additional_reports)
                            if successful_reports < total_reports:
                                result.warnings.append(f"Only {successful_reports}/{total_reports} reports updated successfully")
                        else:
                            result.warnings.append("E3 process ID not available - skipping report updates")
                    except ReportGeneratorError as e:
                        error_msg = f"Failed to update reports: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_report_errors:
                            raise PublishError(error_msg, "update_reports", e)
                current_step += 1

                # Step 4: Save E3 project
                self._report_progress(current_step, total_steps, "Saving E3 project", "Save Project")
                if self.e3_client and publish_config.save_project:
                    try:
                        self.logger.info("Saving E3 project...")
                        save_path = os.path.join(output_folder, f"{project_data.gss_parent} {project_data.serial_number}.e3s")
                        success = self.e3_client.save_project(save_path)
                        if success:
                            result.project_saved_path = save_path
                            result.steps_completed.append("save_project")
                            self.logger.info(f"E3 project saved successfully: {save_path}")
                        else:
                            error_msg = f"Failed to save E3 project to: {save_path}"
                            self.logger.error(error_msg)
                            result.errors.append(error_msg)
                    except Exception as e:
                        error_msg = f"Failed to save E3 project: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_e3_errors:
                            raise PublishError(error_msg, "save_project", e)
                current_step += 1

                # Step 5: Export to PDF/DXF
                self._report_progress(current_step, total_steps, "Exporting to PDF/DXF", "Export Files")
                if self.export_service and publish_config.export_formats:
                    try:
                        self.logger.info(f"Exporting project in formats: {publish_config.export_formats}")
                        export_result = self.export_service.export_project(
                            project_data, output_folder, publish_config.export_formats
                        )
                        
                        result.exported_files.extend(export_result.pdf_files)
                        result.exported_files.extend(export_result.dxf_files)
                        result.export_success = export_result.success
                        result.steps_completed.append("export_files")
                        
                        if not export_result.success:
                            result.errors.extend(export_result.errors)
                        if export_result.warnings:
                            result.warnings.extend(export_result.warnings)
                            
                    except ExportError as e:
                        error_msg = f"Failed to export project: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_export_errors:
                            raise PublishError(error_msg, "export_files", e)
                current_step += 1

                # Step 5: Create manual
                self._report_progress(current_step, total_steps, "Creating project manual", "Create Manual")
                if self.manual_service and publish_config.create_manual:
                    try:
                        if self.manual_service.can_create_manual_for_model(project_data.model):
                            self.logger.info("Creating project manual...")
                            manual_result = self.manual_service.create_manual(project_data, output_folder)
                            
                            result.manual_created = manual_result.success
                            result.manual_files.extend(manual_result.created_files)
                            result.steps_completed.append("create_manual")
                            
                            if not manual_result.success:
                                result.errors.extend(manual_result.errors)
                            if manual_result.warnings:
                                result.warnings.extend(manual_result.warnings)
                        else:
                            result.warnings.append(f"Manual creation not supported for model: {project_data.model}")
                            
                    except ManualError as e:
                        error_msg = f"Failed to create manual: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                        if publish_config.fail_on_manual_errors:
                            raise PublishError(error_msg, "create_manual", e)
                current_step += 1

                # Step 7: Export GSS BOM
                self._report_progress(current_step, total_steps, "Exporting GSS BOM", "Export BOM")
                if self.report_generator and publish_config.export_gss_bom:
                    try:
                        self.logger.info("Exporting GSS BOM...")
                        e3_pid = self.e3_client.get_process_id() if self.e3_client else None
                        if e3_pid:
                            gss_bom_path = self.report_generator.export_gss_bom(
                                e3_pid, project_data.gss_parent, project_data.serial_number
                            )
                            if gss_bom_path:
                                result.gss_bom_path = gss_bom_path
                                result.steps_completed.append("export_gss_bom")
                            else:
                                result.warnings.append("GSS BOM export completed but file not found")
                        else:
                            result.warnings.append("E3 process ID not available - skipping GSS BOM export")
                    except ReportGeneratorError as e:
                        error_msg = f"Failed to export GSS BOM: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                current_step += 1

                # Step 8: Save job data JSON
                self._report_progress(current_step, total_steps, "Saving job data", "Save Data")
                if publish_config.save_job_data:
                    try:
                        self.logger.info("Saving job data JSON...")
                        json_path = self._save_job_data_json(project_data, output_folder)
                        result.job_data_path = json_path
                        result.steps_completed.append("save_job_data")
                    except Exception as e:
                        error_msg = f"Failed to save job data JSON: {e}"
                        self.logger.error(error_msg)
                        result.errors.append(error_msg)
                
                # Determine overall success
                result.success = len(result.errors) == 0
                result.completed_at = datetime.datetime.now()

                # Final progress report
                if result.success:
                    self._report_progress(total_steps, total_steps, f"Project {project_data.serial_number} completed successfully", "Complete")
                    self.logger.info(f"Project publishing completed successfully: {project_data.serial_number}")
                else:
                    self._report_progress(total_steps, total_steps, f"Project {project_data.serial_number} completed with errors", "Complete with Errors")
                    self.logger.warning(f"Project publishing completed with {len(result.errors)} errors: {project_data.serial_number}")

                return result
                
            except PublishError:
                result.success = False
                result.completed_at = datetime.datetime.now()
                raise
            except Exception as e:
                error_msg = f"Unexpected error during project publishing: {e}"
                self.logger.error(error_msg)
                result.success = False
                result.errors.append(error_msg)
                result.completed_at = datetime.datetime.now()
                raise PublishError(error_msg, "publish_project", e)
        
    def publish_series(self, base_project_data, publish_config: 'PublishConfig', 
                      series_count: int) -> List['PublishResult']:
        """
        Publish a series of projects with incremented serial numbers.
        
        Args:
            base_project_data: Base project data template
            publish_config: Publishing configuration options
            series_count: Number of projects in series
            
        Returns:
            List of PublishResult objects
            
        Raises:
            PublishError: If critical publishing operations fail
        """
        if series_count <= 0:
            raise PublishError("Series count must be greater than 0", "validate_series_count")
        
        with timed_operation(f"Series Publishing: {series_count} projects"):
            self.logger.info(f"Starting series publishing: {series_count} projects based on {base_project_data.gss_parent}")
            
            results = []
            successful_count = 0
            
            with progress_tracking(series_count, f"Series Publishing: {base_project_data.gss_parent}") as progress:
                for i in range(series_count):
                    try:
                        # Create project data for this series item
                        series_project_data = self._create_series_project_data(base_project_data, i)
                        
                        self.logger.info(f"Publishing series item {i+1}/{series_count}: {series_project_data.serial_number}")
                        
                        # Publish individual project
                        result = self.publish_project(series_project_data, publish_config)
                        results.append(result)
                        
                        if result.success:
                            successful_count += 1
                            self.logger.info(f"Series item {i+1} completed successfully")
                        else:
                            self.logger.warning(f"Series item {i+1} completed with errors")
                        
                        # Update progress
                        progress.update(1, series_project_data.serial_number)
                        
                        # Stop on first failure if configured
                        if not result.success and publish_config.stop_series_on_error:
                            self.logger.error(f"Stopping series publishing due to error in item {i+1}")
                            break
                            
                    except Exception as e:
                        error_msg = f"Failed to publish series item {i+1}: {e}"
                        self.logger.error(error_msg)
                        
                        # Create error result
                        error_result = PublishResult(
                            serial_number=f"{base_project_data.serial_number}+{i}",
                            success=False,
                            errors=[error_msg]
                        )
                        error_result.completed_at = datetime.datetime.now()
                        results.append(error_result)
                        
                        # Update progress even for errors
                        progress.update(1, f"ERROR: {base_project_data.serial_number}+{i}")
                        
                        if publish_config.stop_series_on_error:
                            break
            
            self.logger.info(f"Series publishing completed: {successful_count}/{len(results)} projects successful")
            
            # Log performance summary
            get_performance_monitor().log_summary()
            
            return results
    
    def _validate_publish_inputs(self, project_data, publish_config):
        """Validate publishing inputs."""
        if not project_data:
            raise PublishError("Project data is required", "validate_inputs")
        
        if not hasattr(project_data, 'gss_parent') or not project_data.gss_parent:
            raise PublishError("GSS parent number is required", "validate_inputs")
        
        if not hasattr(project_data, 'serial_number') or not project_data.serial_number:
            raise PublishError("Serial number is required", "validate_inputs")
        
        if not publish_config:
            raise PublishError("Publish configuration is required", "validate_inputs")
    
    def _create_output_folder(self, project_data, base_path: str) -> str:
        """Create and return output folder path."""
        if not base_path:
            raise PublishError("Output base path is required", "create_output_folder")
        
        folder_name = f"{project_data.gss_parent} {project_data.serial_number}"
        output_folder = os.path.join(base_path, folder_name)
        
        try:
            os.makedirs(output_folder, exist_ok=True)
            self.logger.debug(f"Created output folder: {output_folder}")
            return output_folder
        except Exception as e:
            raise PublishError(f"Failed to create output folder: {e}", "create_output_folder", e)
    
    def _save_job_data_json(self, project_data, output_folder: str) -> str:
        """Save job data to JSON file."""
        job_data = {
            "gss_parent": project_data.gss_parent,
            "serial_number": project_data.serial_number,
            "customer": getattr(project_data, 'customer', ''),
            "location": getattr(project_data, 'location', ''),
            "title": getattr(project_data, 'title', ''),
            "sales_order": getattr(project_data, 'sales_order', ''),
            "model": getattr(project_data, 'model', ''),
            "job_folder": output_folder,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        json_filename = f"{project_data.gss_parent} {project_data.serial_number}.json"
        json_path = os.path.join(output_folder, json_filename)
        
        with open(json_path, 'w') as f:
            json.dump(job_data, f, indent=4)
        
        self.logger.debug(f"Saved job data JSON: {json_path}")
        return json_path
    
    def _create_series_project_data(self, base_project_data, series_index: int):
        """Create project data for a series item."""
        # Create a copy of the base project data
        series_data = type(base_project_data)()
        
        # Copy all attributes
        for attr in dir(base_project_data):
            if not attr.startswith('_'):
                try:
                    setattr(series_data, attr, getattr(base_project_data, attr))
                except (AttributeError, TypeError):
                    pass  # Skip read-only or special attributes
        
        # Increment serial number
        series_data.serial_number = self._increment_serial_number(base_project_data.serial_number, series_index)
        
        return series_data
    
    def _increment_serial_number(self, base_serial: str, increment: int) -> str:
        """Increment serial number for series publishing using SeriesGenerator."""
        try:
            # Use the series generator to properly increment the serial number
            current_serial = base_serial
            for _ in range(increment):
                next_serial = self.series_generator.increment_serial(current_serial)
                if next_serial is None:
                    # If increment fails, fall back to the old behavior as last resort
                    self.logger.warning(f"SeriesGenerator failed to increment {current_serial}, using fallback")
                    return f"{base_serial}+{increment}"
                current_serial = next_serial
            return current_serial
        except SeriesGeneratorError as e:
            # If series generator fails, fall back to the old behavior
            self.logger.warning(f"SeriesGenerator error for {base_serial}: {e}, using fallback")
            return f"{base_serial}+{increment}"


class PublishConfig:
    """Configuration for publishing operations."""

    def __init__(self, config_manager=None):
        """Initialize publish configuration with defaults from configuration."""
        # Load configuration
        config = {}
        if config_manager:
            try:
                config = config_manager.load_app_config()
            except:
                pass

        publish_config = config.get('publishing', {}).get('config', {})
        export_config = config.get('publishing', {}).get('export', {})
        error_config = config.get('publishing', {}).get('error_handling', {})
        paths_config = config.get('paths', {})

        # Initialize with configuration values or defaults
        self.output_base_path = paths_config.get('output_base_path', "")
        self.export_formats = export_config.get('default_formats', ['pdf', 'dxf'])
        self.create_manual = publish_config.get('create_manual', False)
        self.update_bom_reports = publish_config.get('update_boms', True)
        self.export_gss_bom = publish_config.get('export_dxf', True)
        self.save_project = publish_config.get('save_project', True)
        self.save_job_data = publish_config.get('save_job_data', True)

        # Error handling options from configuration
        self.fail_on_e3_errors = error_config.get('fail_on_e3_errors', False)
        self.fail_on_export_errors = error_config.get('fail_on_export_errors', False)
        self.fail_on_manual_errors = error_config.get('fail_on_manual_errors', False)
        self.fail_on_report_errors = error_config.get('fail_on_report_errors', False)
        self.stop_series_on_error = error_config.get('stop_series_on_error', False)


class PublishResult:
    """Result of a publishing operation."""
    
    def __init__(self, success: bool = False, serial_number: str = "", 
                 output_path: str = "", errors: List[str] = None, 
                 warnings: List[str] = None):
        """
        Initialize publish result.
        
        Args:
            success: Whether the operation succeeded
            serial_number: Serial number of published project
            output_path: Path to output files
            errors: List of error messages
            warnings: List of warning messages
        """
        self.success = success
        self.serial_number = serial_number
        self.output_path = output_path
        self.errors = errors or []
        self.warnings = warnings or []
        
        # Detailed results
        self.steps_completed = []
        self.exported_files = []
        self.manual_files = []
        self.manual_created = False
        self.export_success = False
        self.bom_reports_updated = 0
        self.project_saved_path = ""
        self.gss_bom_path = ""
        self.job_data_path = ""
        self.completed_at = None
    
    def __str__(self):
        """String representation of publish result."""
        status = "SUCCESS" if self.success else "FAILED"
        return f"PublishResult({status}, {self.serial_number}, {len(self.steps_completed)} steps)"


class PublishError(Exception):
    """Exception raised when publishing operations fail."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None):
        """
        Initialize publish error.
        
        Args:
            message: Error message
            operation: Name of the operation that failed
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.operation = operation
        self.original_error = original_error
    
    def __str__(self):
        base_msg = self.message
        if self.operation:
            base_msg = f"Publish operation '{self.operation}' failed: {base_msg}"
        if self.original_error:
            base_msg += f" (Original error: {self.original_error})"
        return base_msg