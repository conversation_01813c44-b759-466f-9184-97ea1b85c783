"""
PDF Printing Logging Module

This module provides functions for logging and error handling in the PDF Section Printing Application.
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from typing import Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from lib.utils import get_user_data_dir, ensure_dir_exists
except ImportError:
    from utils import get_user_data_dir, ensure_dir_exists

def get_log_dir() -> str:
    """
    Get the directory for log files.
    
    Returns:
        str: The path to the log directory
    """
    log_dir = os.path.join(get_user_data_dir(), "pdf_printing", "logs")
    ensure_dir_exists(log_dir)
    return log_dir

def setup_logging(log_level: int = logging.INFO) -> str:
    """
    Set up logging to both file and console.
    
    Args:
        log_level: The logging level
        
    Returns:
        str: The path to the log file
    """
    # Create a unique log file name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(get_log_dir(), f"pdf_printing_{timestamp}.log")
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logging.info(f"Logging initialized. Log file: {log_file}")
    return log_file

def log_exception(e: Exception, message: str = "An error occurred") -> None:
    """
    Log an exception with a custom message.
    
    Args:
        e: The exception to log
        message: A custom message to include in the log
    """
    logging.error(f"{message}: {str(e)}")
    logging.debug(f"Exception details: {traceback.format_exc()}")

class StatusLogger:
    """Class for logging status messages and updating the UI."""
    
    def __init__(self, status_callback=None):
        """
        Initialize the status logger.
        
        Args:
            status_callback: A callback function to update the UI with status messages
        """
        self.status_callback = status_callback
    
    def log(self, message: str, level: int = logging.INFO) -> None:
        """
        Log a message and update the UI.
        
        Args:
            message: The message to log
            level: The logging level
        """
        # Log the message
        logging.log(level, message)
        
        # Update the UI if a callback is provided
        if self.status_callback:
            self.status_callback(message)
    
    def info(self, message: str) -> None:
        """
        Log an info message and update the UI.
        
        Args:
            message: The message to log
        """
        self.log(message, logging.INFO)
    
    def warning(self, message: str) -> None:
        """
        Log a warning message and update the UI.
        
        Args:
            message: The message to log
        """
        self.log(message, logging.WARNING)
    
    def error(self, message: str) -> None:
        """
        Log an error message and update the UI.
        
        Args:
            message: The message to log
        """
        self.log(message, logging.ERROR)
    
    def exception(self, e: Exception, message: str = "An error occurred") -> None:
        """
        Log an exception with a custom message and update the UI.
        
        Args:
            e: The exception to log
            message: A custom message to include in the log
        """
        error_message = f"{message}: {str(e)}"
        self.error(error_message)
        logging.debug(f"Exception details: {traceback.format_exc()}")
