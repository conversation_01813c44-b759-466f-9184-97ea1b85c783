"""Utility Functions Module

This module provides utility functions for file operations, path handling,
and configuration management used throughout the Engineering Tools suite.
"""

import os
import sys
import json
import logging
from typing import Any, Dict, Optional, Union
import appdirs

# Application constants
APP_NAME = "EngineeringTools"
APP_AUTHOR = "<PERSON>"

def get_app_dir() -> str:
    """Get the application directory, works for both development and PyInstaller.

    Returns:
        str: The absolute path to the application directory
    """
    try:
        # <PERSON>yInstalle<PERSON> creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except AttributeError:
        # We're running in a normal Python environment
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return base_path

def get_user_data_dir() -> str:
    """Get the user data directory for storing configuration files.

    Returns:
        str: The absolute path to the user data directory
    """
    return appdirs.user_data_dir(APP_NAME, APP_AUTHOR)

def get_resource_path(relative_path: str) -> str:
    """Get absolute path to resource, works for dev and for PyInstaller.

    Args:
        relative_path: The relative path to the resource

    Returns:
        str: The absolute path to the resource
    """
    app_dir = get_app_dir()
    return os.path.join(app_dir, relative_path)

def get_config_path(filename: str) -> str:
    """Get path to a configuration file, checking user directory first, then app directory.

    Args:
        filename: The name of the configuration file

    Returns:
        str: The absolute path to the configuration file
    """
    # First check in user data directory
    user_config = os.path.join(get_user_data_dir(), filename)
    if os.path.exists(user_config):
        return user_config

    # Then check in app resources/config directory
    app_config = os.path.join(get_app_dir(), 'resources', 'config', filename)
    if os.path.exists(app_config):
        return app_config

    # Finally check in app directory (for backward compatibility)
    return os.path.join(get_app_dir(), filename)

def ensure_dir_exists(directory: str) -> str:
    """Ensure a directory exists, creating it if necessary.

    Args:
        directory: The directory path to ensure exists

    Returns:
        str: The directory path

    Raises:
        OSError: If the directory cannot be created
    """
    if not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
        except OSError as e:
            logging.error(f"Failed to create directory {directory}: {e}")
            raise
    return directory

def load_json_config(filename: str) -> Dict[str, Any]:
    """Load a JSON configuration file.

    Args:
        filename: The name of the configuration file

    Returns:
        dict: The configuration data, or an empty dict if the file cannot be loaded

    Raises:
        FileNotFoundError: If the configuration file cannot be found and raise_error is True
    """
    config_path = get_config_path(filename)

    if not os.path.exists(config_path):
        logging.warning(f"Configuration file not found: {config_path}")
        return {}

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
            logging.debug(f"Loaded configuration from {config_path}")
            return config
    except json.JSONDecodeError as e:
        logging.error(f"Invalid JSON in configuration file {config_path}: {e}")
        return {}
    except Exception as e:
        logging.error(f"Error loading configuration file {config_path}: {e}")
        return {}

def save_json_config(filename: str, data: Dict[str, Any]) -> bool:
    """Save a JSON configuration file to the user data directory.

    Args:
        filename: The name of the configuration file
        data: The configuration data to save

    Returns:
        bool: True if successful, False otherwise
    """
    user_dir = get_user_data_dir()
    ensure_dir_exists(user_dir)
    config_path = os.path.join(user_dir, filename)

    try:
        with open(config_path, 'w') as f:
            json.dump(data, f, indent=4)
        logging.debug(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logging.error(f"Failed to save {filename}: {e}")
        return False

def convert_to_relative_path(base_dir: str, full_path: str) -> str:
    """Convert an absolute path to a relative path based on base_dir.

    Args:
        base_dir: The base directory
        full_path: The absolute path to convert

    Returns:
        str: The relative path, or the original path if conversion is not possible
    """
    try:
        return os.path.relpath(full_path, base_dir)
    except ValueError:
        # If paths are on different drives, return the original path
        logging.debug(f"Cannot convert {full_path} to relative path from {base_dir} (different drives)")
        return full_path

def convert_to_absolute_path(base_dir: str, rel_path: str) -> str:
    """Convert a relative path to an absolute path based on base_dir.

    Args:
        base_dir: The base directory
        rel_path: The relative path to convert

    Returns:
        str: The absolute path
    """
    if os.path.isabs(rel_path):
        return rel_path
    return os.path.normpath(os.path.join(base_dir, rel_path))

def setup_logging(log_name: str, log_level: int = logging.INFO) -> str:
    """Set up logging to both file and console.

    This function is deprecated. Use the logging_config module instead.

    Args:
        log_name: The name of the log file
        log_level: The logging level

    Returns:
        str: The path to the log file
    """
    log_dir = ensure_dir_exists(os.path.join(get_user_data_dir(), 'logs'))
    log_file = os.path.join(log_dir, f"{log_name}.log")

    # Remove any existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:  # Make a copy of the list
        root_logger.removeHandler(handler)

    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logging.warning(
        "The utils.setup_logging function is deprecated. "
        "Use the logging_config module instead."
    )

    return log_file
