"""
PDF Printer Queue Module

This module provides functions for creating and managing printer queues with specific settings.
"""

import os
import sys
import logging
import subprocess
import time
from typing import Optional, Dict

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER
from lib.pdf_printing_engine import PrinterProfile

# Printer queue names
LETTER_QUEUE_SUFFIX = "_Letter_Portrait_Duplex"
TABLOID_QUEUE_SUFFIX = "_Tabloid_Landscape_Simplex"
OTHER_QUEUE_SUFFIX = "_Other"

def create_printer_queue(base_printer: str, queue_name: str, settings: Dict[str, str]) -> bool:
    """
    Create a printer queue with specific settings.

    Args:
        base_printer: The name of the base printer
        queue_name: The name of the new printer queue
        settings: A dictionary of printer settings

    Returns:
        bool: True if successful, False otherwise
    """
    # For now, just use the base printer directly
    # This is a simplified approach that avoids the complexity of creating printer queues
    logging.info(f"Using base printer {base_printer} directly instead of creating a queue")
    return True

def ensure_printer_queues(base_printer: str) -> Dict[str, str]:
    """
    Ensure that printer queues exist for each page type.

    Args:
        base_printer: The name of the base printer

    Returns:
        Dict[str, str]: A dictionary mapping page types to printer queue names
    """
    # For now, just use the base printer directly for all page types
    # This is a simplified approach that avoids the complexity of creating printer queues
    return {
        PAGE_TYPE_LETTER: base_printer,
        PAGE_TYPE_TABLOID: base_printer,
        PAGE_TYPE_OTHER: base_printer
    }

def print_to_queue(pdf_path: str, printer_queue: str) -> bool:
    """
    Print a PDF file to a specific printer queue.

    Args:
        pdf_path: The path to the PDF file
        printer_queue: The name of the printer queue

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logging.info(f"Printing {pdf_path} to printer queue {printer_queue}")

        # Use ShellExecute to print the PDF
        import win32api
        win32api.ShellExecute(
            0,
            "print",
            pdf_path,
            f'/d:"{printer_queue}"',
            ".",
            0
        )

        # Wait for the print job to be processed
        time.sleep(3)

        return True
    except Exception as e:
        logging.error(f"Error printing to queue: {e}")
        return False

def print_section_to_queue(section: PDFSection, queues: Dict[str, str]) -> bool:
    """
    Print a PDF section to the appropriate printer queue.

    Args:
        section: The PDF section to print
        queues: A dictionary mapping page types to printer queue names

    Returns:
        bool: True if successful, False otherwise
    """
    if not section.temp_file_path or not os.path.exists(section.temp_file_path):
        logging.error(f"Temporary file for section {section} not found")
        return False

    # Get the appropriate printer queue for this section
    printer_queue = queues.get(section.page_type)
    if not printer_queue:
        logging.error(f"No printer queue found for page type {section.page_type}")
        return False

    # Print to the queue
    logging.info(f"Printing section {section} to printer {printer_queue}")
    logging.info(f"Section page type: {section.page_type}")
    return print_to_queue(section.temp_file_path, printer_queue)
