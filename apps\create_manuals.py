import os
import logging
import json
import sys
from tkinter import filedialog, messagebox
import customtkinter as ctk

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions
try:
    from utils import get_resource_path, get_config_path
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import get_resource_path, get_config_path
        from lib.theme_utils import apply_theme
    except ImportError:
        from lib.manual_creator import get_resource_path, get_config_path
        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

from lib.manual_creator import ManualCreator

# Set up logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')

# Apply the red theme
apply_theme("red", "dark")

# Load the MODELS dictionary from the JSON file
json_file_path = get_config_path("models.json")
logging.info(f"Attempting to load models from: {json_file_path}")

try:
    with open(json_file_path, 'r') as json_file:
        MODELS = json.load(json_file)
    logging.info(f"Successfully loaded models. Categories: {list(MODELS.keys())}")
except Exception as e:
    logging.error(f"Failed to load models.json: {str(e)}")
    MODELS = {}

# List of field labels for additional information
FIELDS = ('Company:', 'Location:', 'Serial:')

class ManualCreatorApp(ctk.CTk):
    """
    GUI application for creating manuals from templates,
    replacing placeholders, converting to PDF, copying drawings,
    and merging PDFs in the specified order.
    """
    def __init__(self):
        super().__init__()
        self.title("Manual Creator")
        self.geometry("375x375")  # Adjusted window size to fit the frame tightly
        self.job_folder = None  # Path to the selected job folder

        self.create_widgets()

    def create_widgets(self):
        """Create and place the GUI widgets."""
        # Dropdown for selecting a category; auto-populated with model categories.
        self.category_var = ctk.StringVar(value=list(MODELS.keys())[0])
        self.category_dropdown = ctk.CTkOptionMenu(self, values=list(MODELS.keys()), variable=self.category_var, command=self.update_model_dropdown)
        self.category_dropdown.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        # Dropdown for selecting a model; initially populated with the first category's models.
        self.model_var = ctk.StringVar(value=list(MODELS[self.category_var.get()].keys())[0])
        self.model_dropdown = ctk.CTkOptionMenu(self, values=list(MODELS[self.category_var.get()].keys()), variable=self.model_var)
        self.model_dropdown.grid(row=1, column=0, padx=10, pady=10, sticky="ew")

        # Job folder selection: label and browse button.
        browse_frame = ctk.CTkFrame(self)
        browse_frame.grid(row=2, column=0, columnspan=2, padx=10, pady=10, sticky="ew")
        self.browse_button = ctk.CTkButton(browse_frame, text="Browse Job Folder", command=self.browse_job_folder)
        self.browse_button.pack(side="right", padx=5)

        folder_frame = ctk.CTkFrame(self, height=30)  # Set a specific height for the frame
        folder_frame.grid(row=3, column=0, columnspan=2, padx=10, pady=10, sticky="ew")
        self.job_folder_label = ctk.CTkLabel(folder_frame, text="No job folder selected")
        self.job_folder_label.pack(side="left", padx=5)
        folder_frame.pack_propagate(False)  # Prevent the frame from expanding
        self.job_folder_label.pack_propagate(False)  # Prevent the label from expanding

        # Create label-entry pairs for each field in FIELDS.
        self.entries = {}
        for i, field in enumerate(FIELDS, start=4):
            label = ctk.CTkLabel(self, text=field)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = ctk.CTkEntry(self)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            # Store entries using lowercase keys (without the colon)
            self.entries[field[:-1].lower()] = entry

        # Create button to initiate the manual creation process.
        self.create_button = ctk.CTkButton(self, text="Create", command=self.create_manual)
        self.create_button.grid(row=i+1, column=0, columnspan=2, padx=10, pady=20)

    def update_model_dropdown(self, *args):
        """Update the model dropdown based on the selected category."""
        category = self.category_var.get()
        models = list(MODELS[category].keys())
        self.model_var.set(models[0])
        self.model_dropdown.configure(values=models)

    def browse_job_folder(self):
        """Open a dialog to select the job folder and update the label."""
        folder = filedialog.askdirectory(title="Select Job Folder")
        if folder:
            self.job_folder = folder
            self.job_folder_label.configure(text=folder)

    def create_manual(self):
        """
        Handle the creation of the manual.
        This method opens the Word template, replaces placeholders,
        converts the document to PDF, copies drawing PDFs, and merges all PDFs.
        """
        # Ensure a job folder is selected.
        if not self.job_folder:
            messagebox.showerror("Error", "Please select a job folder.")
            logging.error("No job folder selected.")
            return

        # Retrieve entry values.
        company = self.entries["company"].get()
        location = self.entries["location"].get()
        serial = self.entries["serial"].get()

        if not all([company, location, serial]):
            messagebox.showerror("Error", "Please fill in all fields.")
            logging.error("Not all fields are filled.")
            return

        # Get selected model information.
        selected_category = self.category_var.get()
        selected_model = self.model_var.get()

        try:
            # Get model_data from MODELS dictionary
            model_data = MODELS[selected_category][selected_model]

            # Log the model data for debugging
            logging.info(f"Model data for {selected_model}: {model_data}")

            # Verify drawings folder exists
            drawings_folder = model_data[1]  # Second element is drawings folder path
            if not os.path.exists(drawings_folder):
                messagebox.showerror("Error", f"Drawings folder not found: {drawings_folder}")
                logging.error(f"Drawings folder not found: {drawings_folder}")
                return

            # Log the contents of the drawings folder
            logging.info(f"Contents of drawings folder {drawings_folder}:")
            for file in os.listdir(drawings_folder):
                logging.info(f"Found file: {file}")

            # Create the manual using the ManualCreator class
            manual_creator = ManualCreator(
                job_folder=self.job_folder,
                category=selected_category,
                model=selected_model,
                company=company,
                location=location,
                serial=serial,
                model_data=model_data,
                document_number=self.entries.get("document number", ctk.CTkEntry(self)).get()  # Fallback if field doesn't exist
            )

            # Create the manual
            result = manual_creator.create_manual()

            # Verify files were copied
            pdf_count = len([f for f in os.listdir(self.job_folder) if f.lower().endswith('.pdf')])
            logging.info(f"Number of PDF files in job folder after creation: {pdf_count}")

            if result:
                messagebox.showinfo("Success", "Manual created successfully!")

        except KeyError:
            messagebox.showerror("Error", f"Model data not found for {selected_model}")
            logging.error(f"Model data not found for {selected_model} in category {selected_category}")
            return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create manual: {str(e)}")
            logging.error(f"Failed to create manual: {str(e)}")
            return

if __name__ == "__main__":
    app = ManualCreatorApp()
    app.mainloop()
