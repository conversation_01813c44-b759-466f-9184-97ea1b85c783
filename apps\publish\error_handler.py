"""
Centralized error handling for the publish application.

This module provides centralized error handling functionality including
error logging, user-friendly error message display, and error recovery.
"""

import logging
import traceback
from typing import Optional, Callable, Any, Dict
from functools import wraps

from .exceptions import (
    PublishError, ValidationError, E3ConnectionError, ExportError,
    ConfigurationError, FileOperationError, ServiceError, 
    IntegrationError, GUIError
)


class ErrorHandler:
    """
    Centralized error handler for the publish application.
    
    This class provides methods for handling different types of errors,
    logging them appropriately, and presenting user-friendly messages.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the error handler.
        
        Args:
            logger: Logger instance to use for error logging
        """
        self.logger = logger or logging.getLogger(__name__)
        self._error_message_map = self._build_error_message_map()
    
    def _build_error_message_map(self) -> Dict[type, str]:
        """
        Build a mapping of exception types to user-friendly messages.
        
        Returns:
            Dictionary mapping exception types to user messages
        """
        return {
            ValidationError: "The provided data is invalid. Please check your input and try again.",
            E3ConnectionError: "Unable to connect to E3 Series. Please ensure E3 is running and try again.",
            ExportError: "Failed to export the project. Please check the output location and try again.",
            ConfigurationError: "Configuration error detected. Please check your settings.",
            FileOperationError: "File operation failed. Please check file permissions and disk space.",
            ServiceError: "A service operation failed. Please try again or contact support.",
            IntegrationError: "External system integration failed. Please check system availability.",
            GUIError: "User interface error occurred. Please restart the application.",
            PublishError: "An error occurred during the publishing process. Please try again."
        }
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Handle an error by logging it and returning a user-friendly message.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            User-friendly error message
        """
        # Log the error with full details
        self._log_error(error, context)
        
        # Return user-friendly message
        return self._get_user_message(error)
    
    def _log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """
        Log an error with appropriate detail level.
        
        Args:
            error: The exception that occurred
            context: Additional context information
        """
        # Build log message
        log_message = f"Error occurred: {str(error)}"
        
        # Add context if available
        if context:
            context_str = ", ".join(f"{k}={v}" for k, v in context.items())
            log_message += f" | Context: {context_str}"
        
        # Add exception context if it's a PublishError
        if isinstance(error, PublishError) and error.context:
            error_context_str = ", ".join(f"{k}={v}" for k, v in error.context.items())
            log_message += f" | Error Context: {error_context_str}"
        
        # Log with appropriate level based on error type
        if isinstance(error, (ValidationError, ConfigurationError)):
            self.logger.warning(log_message)
        elif isinstance(error, (E3ConnectionError, ExportError, IntegrationError)):
            self.logger.error(log_message)
        else:
            self.logger.error(log_message)
        
        # Log stack trace for debugging
        self.logger.debug("Stack trace:", exc_info=True)
    
    def _get_user_message(self, error: Exception) -> str:
        """
        Get a user-friendly error message for an exception.
        
        Args:
            error: The exception that occurred
            
        Returns:
            User-friendly error message
        """
        # Check for specific error type messages
        for error_type, message in self._error_message_map.items():
            if isinstance(error, error_type):
                return message
        
        # Default message for unknown errors
        return "An unexpected error occurred. Please try again or contact support."
    
    def wrap_with_error_handling(self, func: Callable, 
                                error_callback: Optional[Callable[[str], None]] = None) -> Callable:
        """
        Wrap a function with error handling.
        
        Args:
            func: Function to wrap
            error_callback: Optional callback to handle error messages
            
        Returns:
            Wrapped function with error handling
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_message = self.handle_error(e)
                if error_callback:
                    error_callback(error_message)
                else:
                    # Default behavior - just log the error
                    self.logger.error(f"Unhandled error in {func.__name__}: {error_message}")
                return None
        
        return wrapper


def error_handler_decorator(error_callback: Optional[Callable[[str], None]] = None):
    """
    Decorator for adding error handling to functions.
    
    Args:
        error_callback: Optional callback to handle error messages
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        handler = ErrorHandler()
        return handler.wrap_with_error_handling(func, error_callback)
    
    return decorator


class GUIErrorHandler(ErrorHandler):
    """
    Specialized error handler for GUI operations.
    
    This class extends the base ErrorHandler with GUI-specific functionality
    for displaying error messages to users.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None, 
                 show_error_dialog: Optional[Callable[[str, str], None]] = None):
        """
        Initialize the GUI error handler.
        
        Args:
            logger: Logger instance to use for error logging
            show_error_dialog: Function to show error dialog to user
        """
        super().__init__(logger)
        self.show_error_dialog = show_error_dialog
    
    def handle_gui_error(self, error: Exception, title: str = "Error", 
                        context: Optional[Dict[str, Any]] = None) -> None:
        """
        Handle a GUI error by logging it and showing a dialog to the user.
        
        Args:
            error: The exception that occurred
            title: Title for the error dialog
            context: Additional context information
        """
        # Handle the error normally (logging, etc.)
        user_message = self.handle_error(error, context)
        
        # Show error dialog if callback is available
        if self.show_error_dialog:
            self.show_error_dialog(title, user_message)
        else:
            # Fallback - just log that we couldn't show dialog
            self.logger.warning(f"No error dialog callback available. Error: {user_message}")
    
    def wrap_gui_operation(self, func: Callable, title: str = "Operation Error") -> Callable:
        """
        Wrap a GUI operation with error handling.
        
        Args:
            func: GUI function to wrap
            title: Title for error dialogs
            
        Returns:
            Wrapped function with GUI error handling
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self.handle_gui_error(e, title)
                return None
        
        return wrapper


def gui_error_handler(title: str = "Error"):
    """
    Decorator for adding GUI error handling to methods.
    
    Args:
        title: Title for error dialogs
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        # This will be set by the GUI class when it initializes
        handler = getattr(func, '_gui_error_handler', None)
        if handler is None:
            # Create a basic handler if none is set
            handler = GUIErrorHandler()
        
        return handler.wrap_gui_operation(func, title)
    
    return decorator


class ErrorMiddleware:
    """
    Error handling middleware for GUI operations.
    
    This class provides centralized error handling that can be integrated
    into GUI components to ensure consistent error handling and logging.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the error middleware.
        
        Args:
            logger: Logger instance to use for error logging
        """
        self.logger = logger or logging.getLogger(__name__)
        self.error_handler = ErrorHandler(logger)
        self.gui_error_handler: Optional['GUIErrorHandler'] = None
        
    def set_gui_error_handler(self, gui_error_handler: 'GUIErrorHandler'):
        """
        Set the GUI error handler for displaying error dialogs.
        
        Args:
            gui_error_handler: GUI error handler instance
        """
        self.gui_error_handler = gui_error_handler
    
    def handle_operation_error(self, error: Exception, operation_name: str = "Operation",
                              show_dialog: bool = True, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Handle an error from a GUI operation.
        
        Args:
            error: The exception that occurred
            operation_name: Name of the operation that failed
            show_dialog: Whether to show error dialog to user
            context: Additional context information
        """
        # Add operation name to context
        if context is None:
            context = {}
        context['operation'] = operation_name
        
        # Handle the error (logging, etc.)
        user_message = self.error_handler.handle_error(error, context)
        
        # Show dialog if requested and GUI handler is available
        if show_dialog and self.gui_error_handler:
            # Use general GUI error handling for all errors
            # The GUI handler will determine the appropriate dialog type
            self.gui_error_handler.handle_gui_error(error, f"{operation_name} Error")
    
    def wrap_gui_method(self, method: Callable, operation_name: str = None) -> Callable:
        """
        Wrap a GUI method with error handling middleware.
        
        Args:
            method: Method to wrap
            operation_name: Name of the operation for error reporting
            
        Returns:
            Wrapped method with error handling
        """
        if operation_name is None:
            operation_name = getattr(method, '__name__', 'GUI Operation')
        
        @wraps(method)
        def wrapper(*args, **kwargs):
            try:
                return method(*args, **kwargs)
            except Exception as e:
                self.handle_operation_error(e, operation_name)
                return None
        
        return wrapper


class ErrorRecovery:
    """
    Utility class for error recovery operations.
    
    This class provides methods for attempting to recover from common errors
    and retry operations with backoff strategies.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the error recovery utility.
        
        Args:
            logger: Logger instance to use
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def retry_operation(self, operation: Callable, max_retries: int = 3, 
                       delay: float = 1.0, backoff_factor: float = 2.0) -> Any:
        """
        Retry an operation with exponential backoff.
        
        Args:
            operation: Operation to retry
            max_retries: Maximum number of retry attempts
            delay: Initial delay between retries in seconds
            backoff_factor: Factor to multiply delay by for each retry
            
        Returns:
            Result of the operation
            
        Raises:
            The last exception if all retries fail
        """
        import time
        
        last_exception = None
        current_delay = delay
        
        for attempt in range(max_retries + 1):
            try:
                return operation()
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries:
                    self.logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}")
                    self.logger.info(f"Retrying in {current_delay} seconds...")
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
                else:
                    self.logger.error(f"Operation failed after {max_retries + 1} attempts")
        
        # Re-raise the last exception if all retries failed
        raise last_exception
    
    def safe_cleanup(self, cleanup_operations: list[Callable]) -> None:
        """
        Safely execute cleanup operations, continuing even if some fail.
        
        Args:
            cleanup_operations: List of cleanup functions to execute
        """
        for cleanup_op in cleanup_operations:
            try:
                cleanup_op()
            except Exception as e:
                self.logger.warning(f"Cleanup operation failed: {str(e)}")
                # Continue with other cleanup operations