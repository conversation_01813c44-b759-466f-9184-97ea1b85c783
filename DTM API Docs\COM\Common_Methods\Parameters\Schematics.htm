<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Parameters" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Schematic</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor6097"></a><a name="kanchor6098"></a><a name="kanchor6099"></a><a name="kanchor6100"></a>Schematic
		</h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">Integer</span> <i>Schematic</i></p>
            <h4>Description</h4>
            <p>Parameter represents a schematic value as an integer.</p>
            <h4>Possible Values</h4>
            <table style="width: 100%;border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;border-left-style: solid;border-left-width: 1px;border-left-color: ;border-right-style: solid;border-right-width: 1px;border-right-color: ;border-top-style: solid;border-top-width: 1px;border-top-color: ;border-bottom-style: solid;border-bottom-width: 1px;border-bottom-color: ;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 169px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Electric</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">1</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Hydraulic</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">2</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Pneumatic</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">3</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Process, measurement and control</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">4</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Tubes + instruments</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">5</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Single Line Diagram</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">6</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Panel Symbol</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">&gt;= 100</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">User-defined types</td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>The schematic types are defined in the database whether further types may be user-defined.</p>
            <h4>Version Information</h4>
            <p>Introduced in v2009-8.50.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="../../Classes/e3Device/GetSchematicTypes.htm">e3Device.GetSchematicTypes()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Sheet/GetSchematicTypes.htm">e3Sheet.GetSchematicTypes()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Sheet/SetSchematicTypes.htm">e3Sheet.SetSchematicTypes()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Symbol/GetSchematicTypes.htm">e3Symbol.GetSchematicTypes()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Tree/GetVisibleInfoTypesEx.htm">e3Tree.GetVisibleInfoTypesEx()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3Tree/SetVisibleInfoTypesEx.htm">e3Tree.SetVisibleInfoTypesEx()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>