"""
Test dialog components against specific requirements.

This module verifies that the dialog implementation meets the specific
requirements mentioned in the task: 2.1 and 3.4.
"""

import unittest
import sys
import os
import inspect

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestDialogRequirements(unittest.TestCase):
    """Test dialog components against specific requirements."""
    
    def test_requirement_2_1_gui_separation(self):
        """
        Test Requirement 2.1: GUI module SHALL only contain user interface logic and event handling.
        
        Verify that dialog components:
        - Only contain UI logic
        - Don't contain business logic
        - Don't directly import business services
        """
        try:
            from apps.publish.gui.dialogs import (
                BaseDialog, ErrorDialog, ConfirmationDialog, 
                ProgressDialog, ValidationErrorDialog, ResultsDialog
            )
            
            # Get source code for all dialog classes
            dialog_classes = [
                BaseDialog, ErrorDialog, ConfirmationDialog,
                ProgressDialog, ValidationErrorDialog, ResultsDialog
            ]
            
            for dialog_class in dialog_classes:
                source_code = inspect.getsource(dialog_class)
                
                # Verify no business service imports
                business_services = [
                    'PublishService', 'ModelService', 'ExportService', 
                    'ManualService', 'E3Client', 'ReportGeneratorClient'
                ]
                
                for service in business_services:
                    self.assertNotIn(
                        service, source_code,
                        f"{dialog_class.__name__} should not import {service} (Requirement 2.1)"
                    )
                
                # Verify no business logic operations
                business_operations = [
                    'publish_project', 'export_to_pdf', 'create_manual',
                    'connect_to_e3', 'run_report'
                ]
                
                for operation in business_operations:
                    self.assertNotIn(
                        operation, source_code,
                        f"{dialog_class.__name__} should not contain {operation} (Requirement 2.1)"
                    )
                    
            print("✓ Requirement 2.1: Dialogs contain only UI logic")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_requirement_3_4_meaningful_feedback(self):
        """
        Test Requirement 3.4: Application SHALL provide meaningful feedback to users when errors occur.
        
        Verify that dialog components:
        - Provide clear error messages
        - Support different types of user feedback
        - Have consistent interfaces for user interaction
        """
        try:
            from apps.publish.gui.dialogs import (
                ErrorDialog, ConfirmationDialog, ValidationErrorDialog, 
                ProgressDialog, ResultsDialog
            )
            
            # Test ErrorDialog provides meaningful feedback
            error_dialog = ErrorDialog(None, "Test Error", "This is a detailed error message")
            self.assertEqual(error_dialog.title, "Test Error")
            self.assertEqual(error_dialog.message, "This is a detailed error message")
            self.assertTrue(hasattr(error_dialog, 'show'))
            
            # Test ValidationErrorDialog provides structured feedback
            validation_dialog = ValidationErrorDialog(
                None,
                ["Field 1 is required", "Field 2 has invalid format"],
                ["Field 3 might cause issues"]
            )
            
            # Should build meaningful message from errors and warnings
            self.assertIn("Field 1 is required", validation_dialog.message)
            self.assertIn("Field 2 has invalid format", validation_dialog.message)
            self.assertIn("Field 3 might cause issues", validation_dialog.message)
            self.assertIn("errors must be fixed", validation_dialog.message.lower())
            
            # Test ConfirmationDialog provides clear choices
            confirm_dialog = ConfirmationDialog(None, "Confirm Action", "Do you want to continue?")
            self.assertTrue(hasattr(confirm_dialog, 'show'))
            # show() method should return boolean for clear yes/no choice
            
            # Test ProgressDialog provides progress feedback
            progress_dialog = ProgressDialog(None, "Processing", "Please wait...")
            self.assertTrue(hasattr(progress_dialog, 'update_progress'))
            self.assertTrue(hasattr(progress_dialog, 'is_cancelled'))
            
            # Test ResultsDialog provides detailed operation results
            results = [
                {'success': True, 'serial_number': '001'},
                {'success': False, 'serial_number': '002', 'errors': ['Export failed']}
            ]
            results_dialog = ResultsDialog(None, "Results", results)
            self.assertEqual(results_dialog.results, results)
            
            print("✓ Requirement 3.4: Dialogs provide meaningful user feedback")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_consistent_user_experience(self):
        """
        Test that dialogs ensure consistent user experience.
        
        Verify that:
        - All dialogs inherit from BaseDialog for consistency
        - All dialogs have consistent show() interface
        - All dialogs handle errors gracefully
        """
        try:
            from apps.publish.gui.dialogs import (
                BaseDialog, ErrorDialog, ConfirmationDialog, 
                ProgressDialog, ValidationErrorDialog, ResultsDialog
            )
            
            dialog_classes = [
                ErrorDialog, ConfirmationDialog, ProgressDialog, 
                ValidationErrorDialog, ResultsDialog
            ]
            
            for dialog_class in dialog_classes:
                # All dialogs should inherit from BaseDialog
                self.assertTrue(
                    issubclass(dialog_class, BaseDialog),
                    f"{dialog_class.__name__} should inherit from BaseDialog"
                )
                
                # All dialogs should have show() method
                instance = dialog_class(None, "Test", "Test message" if dialog_class != ResultsDialog else [])
                self.assertTrue(
                    hasattr(instance, 'show'),
                    f"{dialog_class.__name__} should have show() method"
                )
                
                # All dialogs should have consistent initialization parameters
                init_signature = inspect.signature(dialog_class.__init__)
                params = list(init_signature.parameters.keys())
                self.assertIn('parent', params, f"{dialog_class.__name__} should accept parent parameter")
                
            print("✓ Dialogs provide consistent user experience")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_error_display_capabilities(self):
        """
        Test that dialogs can display various types of errors effectively.
        """
        try:
            from apps.publish.gui.dialogs import ErrorDialog, ValidationErrorDialog
            
            # Test simple error display
            simple_error = ErrorDialog(None, "Simple Error", "File not found")
            self.assertIsNotNone(simple_error.message)
            
            # Test complex error display
            complex_error = ErrorDialog(
                None, 
                "Complex Error", 
                "Multiple issues occurred:\n1. Connection failed\n2. File access denied\n3. Invalid data format"
            )
            self.assertIn("Multiple issues", complex_error.message)
            
            # Test validation error display
            validation_errors = [
                "GSS Parent # is required",
                "Serial number must be numeric",
                "Customer field cannot be empty"
            ]
            validation_warnings = [
                "Model not found in database",
                "Output folder may not exist"
            ]
            
            validation_dialog = ValidationErrorDialog(None, validation_errors, validation_warnings)
            
            # Should contain all errors and warnings
            for error in validation_errors:
                self.assertIn(error, validation_dialog.message)
            for warning in validation_warnings:
                self.assertIn(warning, validation_dialog.message)
                
            print("✓ Dialogs can display various error types effectively")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_confirmation_dialog_capabilities(self):
        """
        Test that confirmation dialogs provide clear user choices.
        """
        try:
            from apps.publish.gui.dialogs import ConfirmationDialog
            
            # Test simple confirmation
            simple_confirm = ConfirmationDialog(None, "Delete File", "Are you sure you want to delete this file?")
            self.assertEqual(simple_confirm.message, "Are you sure you want to delete this file?")
            
            # Test complex confirmation with warnings
            complex_message = (
                "The following warnings were found:\n\n"
                "1. Model not found in database\n"
                "2. Output folder may not exist\n\n"
                "Do you want to continue anyway?"
            )
            complex_confirm = ConfirmationDialog(None, "Warnings Found", complex_message)
            self.assertIn("warnings were found", complex_confirm.message)
            self.assertIn("Do you want to continue", complex_confirm.message)
            
            print("✓ Confirmation dialogs provide clear user choices")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")


if __name__ == '__main__':
    unittest.main()