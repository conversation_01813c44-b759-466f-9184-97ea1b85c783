{"application": {"name": "Publish Project", "version": "3.0.0", "timeout_seconds": 300, "max_series_count": 1000}, "gui": {"window": {"title": "Publish Project", "geometry": "650x750", "min_width": 500, "min_height": 500}, "theme": {"appearance_mode": "dark", "color_theme": "red", "custom_theme_path": "resources/themes/red.json", "fallback_theme": "blue"}, "console": {"enabled": true, "height": 200, "auto_scroll": true, "max_lines": 1000, "log_level": "INFO", "show_timestamps": true, "font_family": "Consolas", "font_size": 10, "show_by_default": true, "collapsible": true}, "progress": {"enabled": true, "show_eta": true, "show_percentage": true, "allow_cancel": true, "update_interval_ms": 100, "height": 30}, "form_fields": ["GSS Parent #", "Serial number", "Customer", "Location", "Title", "Sales order #"]}, "paths": {"output_base_path": "", "templates_path": "resources/templates", "drawings_path": "resources/drawings", "logs_path": "logs", "config_dir": "resources/config", "themes_path": "resources/themes", "bom_export_path": "T:/ENG/Common/GSS ELEC CAD BOMS"}, "publishing": {"config": {"create_manual": false, "fill_series": false, "series_count": 1, "export_formats": ["PDF"], "export_dxf": true, "update_boms": false, "run_report_generator": false, "save_project": true, "save_job_data": true}, "export": {"default_formats": ["pdf", "dxf"], "valid_formats": ["PDF", "DXF"], "pdf_quality": "high", "dxf_version": "2018"}, "error_handling": {"fail_on_e3_errors": false, "fail_on_export_errors": false, "fail_on_manual_errors": false, "fail_on_report_errors": false, "stop_series_on_error": false}}, "e3_integration": {"connection_timeout": 30, "retry_attempts": 3, "auto_connect": true, "auto_select_instance": true, "reconnect_delay": 0.5}, "logging": {"log_level": "INFO", "console_level": "WARNING", "app_name": "publish_3", "max_file_size": 10485760, "backup_count": 5, "log_file_name": "publish_3.log", "fallback_log_file": "publish_debug.log"}, "services": {"configuration_manager": {"auto_reload": false, "validate_paths": true}, "model_service": {"cache_models": true, "validate_on_load": true}, "export_service": {"parallel_exports": false, "cleanup_temp_files": true}, "manual_service": {"auto_merge": true, "backup_originals": true}, "publish_service": {"batch_processing": true, "progress_reporting": true}}, "frozen_environment": {"log_to_user_temp": true, "change_working_directory": true, "update_path_environment": true, "user_log_file": "publish_3.log"}, "validation": {"required_model_fields": ["template_path", "drawings_path", "asme_flag", "controls_parent"], "validate_file_paths": true, "strict_validation": false}, "user_preferences": {"remember_last_folder": true, "remember_last_model": true, "remember_export_settings": true, "auto_populate_fields": true}, "performance": {"ui_update_delay": 1000, "connection_check_interval": 5000, "auto_refresh_title_block": true}, "threading": {"worker_timeout": 300, "progress_update_interval": 0.5, "log_queue_size": 1000, "progress_queue_size": 100, "enable_background_processing": true, "fallback_to_sync": true, "disable_for_e3_operations": true, "sync_gui_update_interval_ms": 50}, "development": {"debug_mode": true, "verbose_logging": true, "show_stack_traces": false, "enable_profiling": false}}