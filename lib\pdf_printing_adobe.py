"""
PDF Printing with Adobe Acrobat Module

This module provides functions for printing PDF files using Adobe Acrobat with specific settings.
"""

import os
import sys
import logging
import subprocess
import time
from typing import Dict, Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER

def get_adobe_path() -> Optional[str]:
    """
    Get the path to Adobe Acrobat or Reader executable.

    Returns:
        Optional[str]: The path to Adobe executable, or None if not found
    """
    # Check common installation paths
    paths = [
        r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
        r"C:\Program Files (x86)\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
        r"C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
        r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe"
    ]

    for path in paths:
        if os.path.exists(path):
            return path

    return None

def create_adobe_print_settings_file(section_type: str) -> Optional[str]:
    """
    Create a temporary file with Adobe print settings.

    Args:
        section_type: The type of section (Letter, Tabloid, Other)

    Returns:
        Optional[str]: The path to the settings file, or None if creation failed
    """
    # We're not using settings files anymore
    return None

def print_with_adobe_settings(pdf_path: str, printer_name: str, section_type: str) -> bool:
    """
    Print a PDF file using Adobe Acrobat with specific settings.

    Args:
        pdf_path: The path to the PDF file
        printer_name: The name of the printer
        section_type: The type of section (Letter, Tabloid, Other)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the Adobe executable path
        adobe_path = get_adobe_path()
        if not adobe_path:
            logging.error("Adobe Acrobat or Reader not found")
            return False

        logging.info(f"Printing {pdf_path} with Adobe for {section_type}")
        logging.info(f"Using printer: {printer_name}")

        # Create a batch file to set printer settings and print
        import tempfile
        fd, batch_path = tempfile.mkstemp(suffix='.bat')
        os.close(fd)

        try:
            # Determine the settings based on the section type
            if section_type == PAGE_TYPE_LETTER:
                # Letter, portrait, duplex, color
                paper_size = "LETTER"
                orientation = "PORTRAIT"
                duplex = "VERTICAL"
            elif section_type == PAGE_TYPE_TABLOID:
                # Tabloid, landscape, simplex, color
                paper_size = "TABLOID"
                orientation = "LANDSCAPE"
                duplex = "NONE"
            else:
                # Default to Letter, portrait, simplex, color
                paper_size = "LETTER"
                orientation = "PORTRAIT"
                duplex = "NONE"

            # Write the batch file content
            with open(batch_path, 'w') as f:
                f.write(f"@echo off\n")
                f.write(f"echo Printing {os.path.basename(pdf_path)} with specific settings\n")
                f.write(f"echo Paper size: {paper_size}\n")
                f.write(f"echo Orientation: {orientation}\n")
                f.write(f"echo Duplex: {duplex}\n")

                # Set printer settings using RUNDLL32
                f.write(f"echo Setting printer settings...\n")
                f.write(f"RUNDLL32 PRINTUI.DLL,PrintUIEntry /Xs /n\"{printer_name}\" /a \"paper={paper_size}\" \"orientation={orientation}\" \"duplex={duplex}\"\n")

                # Print the PDF using Adobe
                f.write(f"echo Printing PDF with Adobe...\n")
                f.write(f"\"{adobe_path}\" /t \"{pdf_path}\" \"{printer_name}\"\n")

            # Execute the batch file
            logging.info(f"Executing batch file: {batch_path}")
            result = subprocess.run([batch_path], check=True, capture_output=True, text=True)

            # Log the output
            if result.stdout:
                logging.info(f"Batch file output: {result.stdout}")
            if result.stderr:
                logging.warning(f"Batch file error: {result.stderr}")

            # Wait for the print job to be processed
            time.sleep(5)

            return True
        finally:
            # Clean up the batch file
            try:
                os.remove(batch_path)
            except:
                pass
    except Exception as e:
        logging.error(f"Error printing with Adobe: {e}")
        return False

def print_section_with_adobe(section: PDFSection, printer_name: str) -> bool:
    """
    Print a PDF section using Adobe Acrobat with appropriate settings.

    Args:
        section: The PDF section to print
        printer_name: The name of the printer

    Returns:
        bool: True if successful, False otherwise
    """
    if not section.temp_file_path or not os.path.exists(section.temp_file_path):
        logging.error(f"Temporary file for section {section} not found")
        return False

    # Print with Adobe settings
    return print_with_adobe_settings(section.temp_file_path, printer_name, section.page_type)
