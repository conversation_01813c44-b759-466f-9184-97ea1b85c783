"""
Configuration Management Module

This module provides centralized configuration loading and validation for the publish application.
It handles loading application settings and model configurations from JSON files.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

from .exceptions import ConfigurationError, ConfigurationValidationError, ConfigurationLoadError


class ConfigurationManager:
    """
    Manages application configuration loading and validation.
    
    This class handles loading configuration from JSON files and provides
    validation to ensure configuration integrity.
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir: Optional path to configuration directory. 
                       If None, uses default resources/config directory.
        """
        self.logger = logging.getLogger(__name__)
        
        # Set default config directory if not provided
        if config_dir is None:
            # Get the project root directory (assuming we're in apps/publish/config/)
            project_root = Path(__file__).parent.parent.parent.parent
            self.config_dir = project_root / "resources" / "config"
        else:
            self.config_dir = Path(config_dir)
            
        self.logger.debug(f"Configuration directory: {self.config_dir}")
        
        # Configuration storage
        self._app_config: Optional[Dict[str, Any]] = None
        self._models_config: Optional[Dict[str, Any]] = None
        
    def load_app_config(self) -> Dict[str, Any]:
        """
        Load application configuration from config.json.
        
        Returns:
            Dict containing application configuration
            
        Raises:
            ConfigurationLoadError: If configuration file cannot be loaded
            ConfigurationValidationError: If configuration is invalid
        """
        if self._app_config is not None:
            return self._app_config
            
        config_file = self.config_dir / "config.json"
        
        try:
            self.logger.debug(f"Loading application config from: {config_file}")
            
            if not config_file.exists():
                raise ConfigurationLoadError(f"Application config file not found: {config_file}")
                
            with open(config_file, 'r', encoding='utf-8') as f:
                self._app_config = json.load(f)
                
            self._validate_app_config(self._app_config)
            self.logger.info("Application configuration loaded successfully")
            
            return self._app_config
            
        except json.JSONDecodeError as e:
            raise ConfigurationLoadError(f"Invalid JSON in application config: {e}")
        except Exception as e:
            raise ConfigurationLoadError(f"Failed to load application config: {e}")
            
    def load_models_config(self) -> Dict[str, Any]:
        """
        Load models configuration from models.json.
        
        Returns:
            Dict containing models configuration
            
        Raises:
            ConfigurationLoadError: If configuration file cannot be loaded
            ConfigurationValidationError: If configuration is invalid
        """
        if self._models_config is not None:
            return self._models_config
            
        config_file = self.config_dir / "models.json"
        
        try:
            self.logger.debug(f"Loading models config from: {config_file}")
            
            if not config_file.exists():
                raise ConfigurationLoadError(f"Models config file not found: {config_file}")
                
            with open(config_file, 'r', encoding='utf-8') as f:
                self._models_config = json.load(f)
                
            self._validate_models_config(self._models_config)
            self.logger.info("Models configuration loaded successfully")
            
            return self._models_config
            
        except json.JSONDecodeError as e:
            raise ConfigurationLoadError(f"Invalid JSON in models config: {e}")
        except ConfigurationValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            raise ConfigurationLoadError(f"Failed to load models config: {e}")
            
    def get_app_setting(self, key: str, default: Any = None) -> Any:
        """
        Get a specific application setting.
        
        Args:
            key: Configuration key to retrieve
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        config = self.load_app_config()
        return config.get(key, default)
        
    def get_model_config(self, category: str, model: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific model.
        
        Args:
            category: Model category (e.g., "Chiller", "Pump")
            model: Model name (e.g., "P408", "PSP-4")
            
        Returns:
            Model configuration dict or None if not found
        """
        models = self.load_models_config()
        return models.get(category, {}).get(model)
        
    def get_models_for_category(self, category: str) -> Dict[str, Any]:
        """
        Get all models for a specific category.
        
        Args:
            category: Model category name
            
        Returns:
            Dict of models in the category
        """
        models = self.load_models_config()
        return models.get(category, {})
        
    def get_all_categories(self) -> list[str]:
        """
        Get list of all model categories.
        
        Returns:
            List of category names
        """
        models = self.load_models_config()
        return list(models.keys())
        
    def reload_config(self) -> None:
        """
        Reload all configuration from files.
        
        This clears the cached configuration and forces a reload from disk.
        """
        self.logger.info("Reloading configuration")
        self._app_config = None
        self._models_config = None
        
    def _validate_app_config(self, config: Dict[str, Any]) -> None:
        """
        Validate application configuration structure.
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ConfigurationValidationError: If configuration is invalid
        """
        # Basic validation - ensure it's a dictionary
        if not isinstance(config, dict):
            raise ConfigurationValidationError("Application configuration must be a dictionary")
            
        # Log configuration keys for debugging
        self.logger.debug(f"Application config keys: {list(config.keys())}")
        
        # Additional validation can be added here as needed
        # For now, we accept any valid JSON dictionary structure
        
    def _validate_models_config(self, config: Dict[str, Any]) -> None:
        """
        Validate models configuration structure.
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ConfigurationValidationError: If configuration is invalid
        """
        if not isinstance(config, dict):
            raise ConfigurationValidationError("Models configuration must be a dictionary")
            
        # Validate each category and model
        for category_name, category_data in config.items():
            if not isinstance(category_data, dict):
                raise ConfigurationValidationError(
                    f"Category '{category_name}' must contain a dictionary of models"
                )
                
            for model_name, model_data in category_data.items():
                self._validate_model_data(category_name, model_name, model_data)
                
        self.logger.debug(f"Validated {len(config)} model categories")
        
    def _validate_model_data(self, category: str, model: str, data: Dict[str, Any]) -> None:
        """
        Validate individual model configuration data.
        
        Args:
            category: Model category name
            model: Model name
            data: Model configuration data
            
        Raises:
            ConfigurationValidationError: If model data is invalid
        """
        if not isinstance(data, dict):
            raise ConfigurationValidationError(
                f"Model '{category}/{model}' configuration must be a dictionary"
            )
            
        # Required fields for model configuration
        required_fields = ['template_path', 'drawings_path', 'asme_flag', 'controls_parent']
        
        for field in required_fields:
            if field not in data:
                raise ConfigurationValidationError(
                    f"Model '{category}/{model}' missing required field: {field}"
                )
                
        # Validate field types
        if not isinstance(data['template_path'], str):
            raise ConfigurationValidationError(
                f"Model '{category}/{model}' template_path must be a string"
            )
            
        if not isinstance(data['drawings_path'], str):
            raise ConfigurationValidationError(
                f"Model '{category}/{model}' drawings_path must be a string"
            )
            
        if not isinstance(data['asme_flag'], bool):
            raise ConfigurationValidationError(
                f"Model '{category}/{model}' asme_flag must be a boolean"
            )
            
        if not isinstance(data['controls_parent'], str):
            raise ConfigurationValidationError(
                f"Model '{category}/{model}' controls_parent must be a string"
            )
            
    def validate_paths(self) -> Dict[str, list[str]]:
        """
        Validate that all configured paths exist.
        
        Returns:
            Dict with 'missing_templates' and 'missing_drawings' lists
        """
        models = self.load_models_config()
        missing_templates = []
        missing_drawings = []
        
        for category_name, category_data in models.items():
            for model_name, model_data in category_data.items():
                template_path = Path(model_data['template_path'])
                drawings_path = Path(model_data['drawings_path'])
                
                if not template_path.exists():
                    missing_templates.append(f"{category_name}/{model_name}: {template_path}")
                    
                if not drawings_path.exists():
                    missing_drawings.append(f"{category_name}/{model_name}: {drawings_path}")
                    
        return {
            'missing_templates': missing_templates,
            'missing_drawings': missing_drawings
        }