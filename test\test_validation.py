"""
Unit tests for validation utilities and exception classes.
"""

import unittest
import tempfile
import os
from unittest.mock import patch

# Add the parent directory to the path to import from apps
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.models.validation import (
    ValidationError, ConfigurationError, PublishError, E3ConnectionError, ExportError,
    validate_gss_parent, validate_serial_number, validate_file_path, validate_model_name,
    validate_series_count, validate_export_formats, validate_template_path,
    validate_email_address, sanitize_filename, validate_and_sanitize_project_data
)


class TestValidationExceptions(unittest.TestCase):
    """Test cases for validation exception classes."""
    
    def test_validation_error(self):
        """Test ValidationError exception."""
        error = ValidationError("Test message", field="test_field", value="test_value")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.value, "test_value")
        self.assertIn("test_field", str(error))
    
    def test_configuration_error(self):
        """Test ConfigurationError exception."""
        error = ConfigurationError("Config error", config_key="test_key")
        self.assertEqual(error.config_key, "test_key")
    
    def test_publish_error_hierarchy(self):
        """Test publish error exception hierarchy."""
        self.assertTrue(issubclass(E3ConnectionError, PublishError))
        self.assertTrue(issubclass(ExportError, PublishError))


class TestGSSParentValidation(unittest.TestCase):
    """Test cases for GSS parent number validation."""
    
    def test_valid_gss_parent(self):
        """Test valid GSS parent numbers."""
        valid_numbers = ["GSS-123", "ABC123", "TEST_001", "Model-A1"]
        for number in valid_numbers:
            errors = validate_gss_parent(number)
            self.assertEqual(len(errors), 0, f"Valid GSS parent '{number}' should not have errors")
    
    def test_empty_gss_parent(self):
        """Test empty GSS parent number."""
        errors = validate_gss_parent("")
        self.assertGreater(len(errors), 0)
        self.assertIn("required", errors[0])
        
        errors = validate_gss_parent("   ")
        self.assertGreater(len(errors), 0)
        self.assertIn("whitespace", errors[0])
    
    def test_invalid_characters(self):
        """Test GSS parent with invalid characters."""
        invalid_numbers = ["GSS@123", "ABC#123", "TEST 001", "Model/A1"]
        for number in invalid_numbers:
            errors = validate_gss_parent(number)
            self.assertGreater(len(errors), 0, f"Invalid GSS parent '{number}' should have errors")
            self.assertIn("invalid characters", errors[0])
    
    def test_length_constraints(self):
        """Test GSS parent length constraints."""
        # Too short
        errors = validate_gss_parent("AB")
        self.assertGreater(len(errors), 0)
        self.assertIn("at least 3", errors[0])
        
        # Too long
        errors = validate_gss_parent("A" * 51)
        self.assertGreater(len(errors), 0)
        self.assertIn("50 characters", errors[0])
    
    def test_hyphen_patterns(self):
        """Test GSS parent hyphen patterns."""
        # Starting with hyphen
        errors = validate_gss_parent("-ABC123")
        self.assertGreater(len(errors), 0)
        self.assertIn("start or end", errors[0])
        
        # Ending with hyphen
        errors = validate_gss_parent("ABC123-")
        self.assertGreater(len(errors), 0)
        self.assertIn("start or end", errors[0])
        
        # Consecutive hyphens
        errors = validate_gss_parent("ABC--123")
        self.assertGreater(len(errors), 0)
        self.assertIn("consecutive", errors[0])


class TestSerialNumberValidation(unittest.TestCase):
    """Test cases for serial number validation."""
    
    def test_valid_serial_numbers(self):
        """Test valid serial numbers."""
        valid_numbers = ["001", "SN-123", "ABC_001", "Model1"]
        for number in valid_numbers:
            errors = validate_serial_number(number)
            self.assertEqual(len(errors), 0, f"Valid serial number '{number}' should not have errors")
    
    def test_empty_serial_number(self):
        """Test empty serial number."""
        errors = validate_serial_number("")
        self.assertGreater(len(errors), 0)
        self.assertIn("required", errors[0])
    
    def test_invalid_characters(self):
        """Test serial number with invalid characters."""
        invalid_numbers = ["SN@123", "ABC#123", "TEST 001"]
        for number in invalid_numbers:
            errors = validate_serial_number(number)
            self.assertGreater(len(errors), 0)
            self.assertIn("invalid characters", errors[0])
    
    def test_length_constraints(self):
        """Test serial number length constraints."""
        # Too long
        errors = validate_serial_number("A" * 51)
        self.assertGreater(len(errors), 0)
        self.assertIn("50 characters", errors[0])


class TestFilePathValidation(unittest.TestCase):
    """Test cases for file path validation."""
    
    def test_valid_file_path(self):
        """Test valid file path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            errors = validate_file_path(temp_dir)
            self.assertEqual(len(errors), 0)
    
    def test_empty_file_path(self):
        """Test empty file path."""
        errors = validate_file_path("")
        self.assertGreater(len(errors), 0)
        self.assertIn("required", errors[0])
    
    def test_invalid_characters(self):
        """Test file path with invalid characters."""
        invalid_paths = ["C:\\test<path", "C:\\test>path", 'C:\\test"path']
        for path in invalid_paths:
            errors = validate_file_path(path)
            self.assertGreater(len(errors), 0)
            self.assertIn("invalid characters", errors[0])
    
    def test_nonexistent_path(self):
        """Test nonexistent file path."""
        errors = validate_file_path("C:\\nonexistent\\path\\that\\does\\not\\exist")
        self.assertGreater(len(errors), 0)
        self.assertIn("does not exist", errors[0])
    
    def test_path_too_long(self):
        """Test path that's too long."""
        long_path = "C:\\" + "A" * 300
        errors = validate_file_path(long_path)
        self.assertGreater(len(errors), 0)
        self.assertIn("too long", errors[0])


class TestModelNameValidation(unittest.TestCase):
    """Test cases for model name validation."""
    
    def test_valid_model_name(self):
        """Test valid model name."""
        errors = validate_model_name("TestModel")
        self.assertEqual(len(errors), 0)
    
    def test_empty_model_name(self):
        """Test empty model name."""
        errors = validate_model_name("")
        self.assertGreater(len(errors), 0)
        self.assertIn("required", errors[0])
    
    def test_model_not_in_available_list(self):
        """Test model name not in available models list."""
        available_models = ["Model1", "Model2", "Model3"]
        errors = validate_model_name("Model4", available_models)
        self.assertGreater(len(errors), 0)
        self.assertIn("not in the list", errors[0])
    
    def test_model_in_available_list(self):
        """Test model name in available models list."""
        available_models = ["Model1", "Model2", "Model3"]
        errors = validate_model_name("Model2", available_models)
        self.assertEqual(len(errors), 0)


class TestSeriesCountValidation(unittest.TestCase):
    """Test cases for series count validation."""
    
    def test_valid_series_count(self):
        """Test valid series counts."""
        valid_counts = [1, 5, 10, 100]
        for count in valid_counts:
            errors = validate_series_count(count)
            self.assertEqual(len(errors), 0)
    
    def test_invalid_series_count_type(self):
        """Test invalid series count type."""
        errors = validate_series_count("5")
        self.assertGreater(len(errors), 0)
        self.assertIn("integer", errors[0])
    
    def test_series_count_too_small(self):
        """Test series count too small."""
        errors = validate_series_count(0)
        self.assertGreater(len(errors), 0)
        self.assertIn("at least 1", errors[0])
    
    def test_series_count_too_large(self):
        """Test series count too large."""
        errors = validate_series_count(1001)
        self.assertGreater(len(errors), 0)
        self.assertIn("cannot exceed", errors[0])


class TestExportFormatsValidation(unittest.TestCase):
    """Test cases for export formats validation."""
    
    def test_valid_export_formats(self):
        """Test valid export formats."""
        valid_formats = [["PDF"], ["DXF"], ["PDF", "DXF"]]
        for formats in valid_formats:
            errors = validate_export_formats(formats)
            self.assertEqual(len(errors), 0)
    
    def test_empty_export_formats(self):
        """Test empty export formats list."""
        errors = validate_export_formats([])
        self.assertGreater(len(errors), 0)
        self.assertIn("At least one", errors[0])
    
    def test_invalid_export_formats(self):
        """Test invalid export formats."""
        errors = validate_export_formats(["PDF", "INVALID", "DXF"])
        self.assertGreater(len(errors), 0)
        self.assertIn("Invalid export formats", errors[0])


class TestTemplatePathValidation(unittest.TestCase):
    """Test cases for template path validation."""
    
    def test_valid_template_path(self):
        """Test valid template path."""
        with tempfile.NamedTemporaryFile(suffix='.dotx', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            errors = validate_template_path(temp_path)
            self.assertEqual(len(errors), 0)
        finally:
            os.unlink(temp_path)
    
    def test_empty_template_path(self):
        """Test empty template path."""
        errors = validate_template_path("")
        self.assertGreater(len(errors), 0)
        self.assertIn("required", errors[0])
    
    def test_nonexistent_template_path(self):
        """Test nonexistent template path."""
        errors = validate_template_path("C:\\nonexistent\\template.dotx")
        self.assertGreater(len(errors), 0)
        self.assertIn("does not exist", errors[0])
    
    def test_invalid_template_extension(self):
        """Test invalid template file extension."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            errors = validate_template_path(temp_path)
            self.assertGreater(len(errors), 0)
            self.assertIn("Word document", errors[0])
        finally:
            os.unlink(temp_path)


class TestEmailValidation(unittest.TestCase):
    """Test cases for email address validation."""
    
    def test_valid_email_addresses(self):
        """Test valid email addresses."""
        valid_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        for email in valid_emails:
            errors = validate_email_address(email)
            self.assertEqual(len(errors), 0)
    
    def test_empty_email(self):
        """Test empty email (should be valid as it's optional)."""
        errors = validate_email_address("")
        self.assertEqual(len(errors), 0)
    
    def test_invalid_email_addresses(self):
        """Test invalid email addresses."""
        invalid_emails = ["invalid", "@example.com", "test@", "test@.com"]
        for email in invalid_emails:
            errors = validate_email_address(email)
            self.assertGreater(len(errors), 0)
            self.assertIn("Invalid email", errors[0])


class TestFilenameSanitization(unittest.TestCase):
    """Test cases for filename sanitization."""
    
    def test_sanitize_valid_filename(self):
        """Test sanitizing valid filename."""
        result = sanitize_filename("valid_filename.txt")
        self.assertEqual(result, "valid_filename.txt")
    
    def test_sanitize_invalid_characters(self):
        """Test sanitizing filename with invalid characters."""
        result = sanitize_filename("file<name>with:invalid|chars")
        self.assertEqual(result, "file_name_with_invalid_chars")
    
    def test_sanitize_empty_filename(self):
        """Test sanitizing empty filename."""
        result = sanitize_filename("")
        self.assertEqual(result, "untitled")
    
    def test_sanitize_long_filename(self):
        """Test sanitizing very long filename."""
        long_name = "A" * 250
        result = sanitize_filename(long_name)
        self.assertLessEqual(len(result), 200)


class TestProjectDataValidation(unittest.TestCase):
    """Test cases for project data validation and sanitization."""
    
    def test_validate_and_sanitize_valid_data(self):
        """Test validation and sanitization of valid project data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_data = {
                "gss_parent": "GSS-123",
                "serial_number": "SN-001",
                "customer": "Test Customer",
                "folder_path": temp_dir
            }
            
            sanitized, errors, warnings = validate_and_sanitize_project_data(project_data)
            self.assertEqual(len(errors), 0)
            self.assertEqual(sanitized["gss_parent"], "GSS-123")
    
    def test_validate_and_sanitize_invalid_data(self):
        """Test validation and sanitization of invalid project data."""
        project_data = {
            "gss_parent": "",  # Invalid - empty
            "serial_number": "SN@001",  # Invalid - special character
            "customer": "Customer<Name>",  # Will be sanitized
            "folder_path": "C:\\nonexistent"  # Invalid - doesn't exist
        }
        
        sanitized, errors, warnings = validate_and_sanitize_project_data(project_data)
        self.assertGreater(len(errors), 0)
        self.assertGreater(len(warnings), 0)
        self.assertEqual(sanitized["customer"], "Customer_Name_")


if __name__ == '__main__':
    unittest.main()