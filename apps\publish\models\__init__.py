"""
Models Module - Data Structures and Validation.

This module contains all data model classes, validation logic, and data transfer
objects used throughout the publishing system. Models are designed to be immutable
where possible and include comprehensive validation capabilities.

The models follow the Data Transfer Object (DTO) pattern and include:
- Type safety through dataclasses and type hints
- Built-in validation with detailed error reporting
- Serialization/deserialization capabilities
- Clear field documentation and constraints

Available Models:
    - **ProjectData**: Core project information with validation
    - **TitleBlockData**: E3 title block data structure
    - **PublishConfig**: Publishing configuration options
    - **ValidationResult**: Data validation results and error reporting

Design Principles:
    - Immutability: Models are immutable where possible
    - Validation: Built-in validation with clear error messages
    - Type Safety: Comprehensive type hints and runtime checking
    - Serialization: Support for JSON serialization/deserialization
    - Documentation: Clear field documentation and constraints

Usage Example:
    ```python
    from apps.publish.models import ProjectData, ValidationResult
    
    # Create project data
    project = ProjectData(
        gss_parent="GSS123",
        serial_number="001",
        customer="Example Corp"
    )
    
    # Validate data
    validation_result = project.validate()
    if not validation_result.is_valid:
        print("Validation errors:", validation_result.errors)
    
    # Convert to dictionary for serialization
    project_dict = project.to_dict()
    
    # Create from dictionary
    restored_project = ProjectData.from_dict(project_dict)
    ```

Validation Features:
    - Required field validation
    - Format validation (e.g., GSS parent number format)
    - Length constraints
    - Custom business rule validation
    - Warning vs. error distinction
    - Detailed error context

Data Flow:
    1. Data enters system through GUI or API
    2. Raw data is converted to model objects
    3. Models perform validation automatically
    4. Validated models are passed to services
    5. Services use models for business operations
    6. Results are returned as model objects

Error Handling:
    Models use ValidationError for validation failures:
    - Field-level error reporting
    - Multiple error accumulation
    - Warning vs. error distinction
    - Context information for debugging

Author: E3 Automation Team
"""

from .project_data import ProjectData, TitleBlockData, ValidationResult
from .publish_config import PublishConfig
from .validation import (
    validate_gss_parent,
    validate_serial_number,
    validate_file_path,
    ValidationError
)

__all__ = [
    'ProjectData',
    'TitleBlockData', 
    'ValidationResult',
    'PublishConfig',
    'validate_gss_parent',
    'validate_serial_number',
    'validate_file_path',
    'ValidationError',
]