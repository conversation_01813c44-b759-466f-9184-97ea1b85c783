"""
Unit tests for ModelService.

This module contains tests for the ModelService class functionality
including model lookup and data retrieval operations.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add the parent directory to the path to import from apps
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.services.model_service import ModelService, ModelData


class TestModelService(unittest.TestCase):
    """Test cases for ModelService class."""
    
    def setUp(self):
        """Set up test fixtures with mock model data."""
        self.mock_models_config = {
            "Chiller": {
                "P408": {
                    "template_path": "/path/to/template.dotx",
                    "drawings_path": "/path/to/drawings",
                    "asme_flag": False,
                    "controls_parent": "503390"
                },
                "P410": {
                    "template_path": "/path/to/template2.dotx",
                    "drawings_path": "/path/to/drawings2",
                    "asme_flag": True,
                    "controls_parent": "503390"
                }
            },
            "Pump": {
                "PSP-4": {
                    "template_path": "/path/to/pump_template.dotx",
                    "drawings_path": "/path/to/pump_drawings",
                    "asme_flag": False,
                    "controls_parent": "500432"
                }
            }
        }
        self.service = ModelService(self.mock_models_config)
    
    def test_init_with_config(self):
        """Test ModelService initialization with configuration."""
        service = ModelService(self.mock_models_config)
        self.assertEqual(service.models, self.mock_models_config)
    
    def test_init_without_config(self):
        """Test ModelService initialization without configuration."""
        service = ModelService()
        self.assertEqual(service.models, {})
    
    def test_get_models_for_gss_found(self):
        """Test getting models for existing GSS parent number."""
        models = self.service.get_models_for_gss("503390")
        expected = ["P408", "P410"]  # Should be sorted
        self.assertEqual(models, expected)
    
    def test_get_models_for_gss_single_match(self):
        """Test getting models for GSS parent with single match."""
        models = self.service.get_models_for_gss("500432")
        expected = ["PSP-4"]
        self.assertEqual(models, expected)
    
    def test_get_models_for_gss_not_found(self):
        """Test getting models for non-existent GSS parent number."""
        models = self.service.get_models_for_gss("999999")
        self.assertEqual(models, [])
    
    def test_get_models_for_gss_empty_string(self):
        """Test getting models for empty GSS parent number."""
        models = self.service.get_models_for_gss("")
        self.assertEqual(models, [])
    
    def test_get_model_data_found(self):
        """Test getting model data for existing model."""
        model_data = self.service.get_model_data("P408")
        self.assertIsNotNone(model_data)
        self.assertEqual(model_data.template_path, "/path/to/template.dotx")
        self.assertEqual(model_data.drawings_path, "/path/to/drawings")
        self.assertEqual(model_data.asme_flag, False)
        self.assertEqual(model_data.controls_parent, "503390")
    
    def test_get_model_data_not_found(self):
        """Test getting model data for non-existent model."""
        model_data = self.service.get_model_data("NONEXISTENT")
        self.assertIsNone(model_data)
    
    def test_get_model_data_empty_string(self):
        """Test getting model data for empty model name."""
        model_data = self.service.get_model_data("")
        self.assertIsNone(model_data)
    
    def test_legacy_format_support(self):
        """Test support for legacy list/tuple format in model configuration."""
        legacy_config = {
            "Legacy": {
                "OLD-MODEL": [
                    "/legacy/template.dotx",
                    "/legacy/drawings",
                    True,
                    "LEGACY123"
                ]
            }
        }
        service = ModelService(legacy_config)
        
        # Test GSS lookup with legacy format
        models = service.get_models_for_gss("LEGACY123")
        self.assertEqual(models, ["OLD-MODEL"])
        
        # Test model data retrieval with legacy format
        model_data = service.get_model_data("OLD-MODEL")
        self.assertIsNotNone(model_data)
        self.assertEqual(model_data.template_path, "/legacy/template.dotx")
        self.assertEqual(model_data.drawings_path, "/legacy/drawings")
        self.assertEqual(model_data.asme_flag, True)
        self.assertEqual(model_data.controls_parent, "LEGACY123")
    
    @patch('apps.publish.services.model_service.logging.getLogger')
    def test_logging_debug_messages(self, mock_logger):
        """Test that debug messages are logged appropriately."""
        mock_logger_instance = MagicMock()
        mock_logger.return_value = mock_logger_instance
        
        service = ModelService(self.mock_models_config)
        service.get_models_for_gss("503390")
        
        # Verify debug logging was called
        mock_logger_instance.debug.assert_called()
    
    @patch('apps.publish.services.model_service.logging.getLogger')
    def test_logging_warning_for_missing_model(self, mock_logger):
        """Test that warning is logged for missing model."""
        mock_logger_instance = MagicMock()
        mock_logger.return_value = mock_logger_instance
        
        service = ModelService(self.mock_models_config)
        service.get_model_data("NONEXISTENT")
        
        # Verify warning logging was called
        mock_logger_instance.warning.assert_called_with("Model NONEXISTENT not found in configuration")


class TestModelData(unittest.TestCase):
    """Test cases for ModelData class."""
    
    def test_init_with_all_parameters(self):
        """Test ModelData initialization with all parameters."""
        model_data = ModelData(
            template_path="/test/template.dotx",
            drawings_path="/test/drawings",
            asme_flag=True,
            controls_parent="TEST123"
        )
        
        self.assertEqual(model_data.template_path, "/test/template.dotx")
        self.assertEqual(model_data.drawings_path, "/test/drawings")
        self.assertEqual(model_data.asme_flag, True)
        self.assertEqual(model_data.controls_parent, "TEST123")
    
    def test_init_with_defaults(self):
        """Test ModelData initialization with default parameters."""
        model_data = ModelData()
        
        self.assertEqual(model_data.template_path, "")
        self.assertEqual(model_data.drawings_path, "")
        self.assertEqual(model_data.asme_flag, False)
        self.assertEqual(model_data.controls_parent, "")


if __name__ == '__main__':
    unittest.main()