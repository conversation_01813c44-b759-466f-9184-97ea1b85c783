"""
Integration tests for configuration and logging modules.
"""

import json
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from apps.publish.config import (
    ConfigurationManager,
    setup_publish_logging,
    get_publish_logger
)


class TestConfigIntegration(unittest.TestCase):
    """Integration tests for configuration and logging working together."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
        
        # Create sample configuration files
        self.app_config = {
            "log_level": "DEBUG",
            "console_level": "INFO",
            "app_name": "test_publish"
        }
        
        self.models_config = {
            "TestCategory": {
                "TEST-001": {
                    "template_path": "/test/template.dotx",
                    "drawings_path": "/test/drawings",
                    "asme_flag": True,
                    "controls_parent": "TEST123"
                }
            }
        }
        
        # Write config files
        with open(self.config_dir / "config.json", 'w') as f:
            json.dump(self.app_config, f)
            
        with open(self.config_dir / "models.json", 'w') as f:
            json.dump(self.models_config, f)
            
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_configuration_and_logging_integration(self, mock_lib_setup):
        """Test that configuration manager and logging work together."""
        mock_lib_setup.return_value = "/test/path/test_publish.log"
        
        # Initialize configuration manager
        config_manager = ConfigurationManager(str(self.config_dir))
        
        # Load app config
        app_config = config_manager.load_app_config()
        
        # Set up logging using configuration
        log_path = setup_publish_logging(app_config)
        
        # Get a logger
        logger = get_publish_logger(__name__)
        
        # Verify everything works
        self.assertEqual(log_path, "/test/path/test_publish.log")
        self.assertIsNotNone(logger)
        
        # Verify lib_setup_logging was called with config values
        mock_lib_setup.assert_called_once()
        call_args = mock_lib_setup.call_args[1]  # Get keyword arguments
        self.assertEqual(call_args['app_name'], 'test_publish')
        
    def test_configuration_manager_with_logging(self):
        """Test that configuration manager properly logs its operations."""
        config_manager = ConfigurationManager(str(self.config_dir))
        
        # This should work without errors and log appropriately
        app_config = config_manager.load_app_config()
        models_config = config_manager.load_models_config()
        
        self.assertEqual(app_config, self.app_config)
        self.assertEqual(models_config, self.models_config)
        
        # Test getting specific configurations
        model_config = config_manager.get_model_config("TestCategory", "TEST-001")
        expected_model = self.models_config["TestCategory"]["TEST-001"]
        self.assertEqual(model_config, expected_model)


if __name__ == '__main__':
    unittest.main()