"""
PDF Direct Printing Module

This module provides functions for directly printing PDF files using various methods.
"""

import os
import sys
import logging
import subprocess
import tempfile
import time
import ctypes
from ctypes import wintypes
import win32print
import win32api
import win32con
import shutil

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_printing_engine import PrinterProfile, PAPER_LETTER, PAPER_TABLOID
from lib.pdf_printing_engine import ORIENTATION_PORTRAIT, ORIENTATION_LANDSCAPE
from lib.pdf_printing_engine import DUPLEX_SIMPLEX, DUPLEX_VERTICAL, DUPLEX_HORIZONTAL
from lib.pdf_printing_engine import COLOR_MONOCHROME, COLOR_COLOR

def print_pdf_with_ghostscript(pdf_path: str, printer_name: str) -> bool:
    """
    Print a PDF file using Ghostscript.

    Args:
        pdf_path: The path to the PDF file
        printer_name: The name of the printer

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if Ghostscript is installed or download portable version
        gs_path = ensure_ghostscript()
        if not gs_path:
            logging.warning("Ghostscript not found and could not be installed")
            return False

        logging.info(f"Printing {pdf_path} with Ghostscript")
        logging.info(f"Using printer: {printer_name}")

        # Build the command
        cmd = [
            gs_path,
            "-dNOPAUSE",
            "-dBATCH",
            "-dPrinted",
            "-sDEVICE=mswinpr2",
            f"-sOutputFile=%printer%{printer_name}",
            pdf_path
        ]

        # Log the command
        logging.info(f"Ghostscript command: {' '.join(cmd)}")

        # Execute the command
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        # Log the output
        if result.stdout:
            logging.info(f"Ghostscript output: {result.stdout}")
        if result.stderr:
            logging.warning(f"Ghostscript error: {result.stderr}")

        # Wait for the print job to be processed
        time.sleep(3)

        return True
    except Exception as e:
        logging.error(f"Error printing with Ghostscript: {e}")
        return False

def ensure_ghostscript() -> str:
    """
    Ensure Ghostscript is available and return the path to the executable.

    Returns:
        str: The path to the Ghostscript executable
    """
    # Check common installation paths
    paths = [
        r"C:\Program Files\gs\gs9.56.1\bin\gswin64c.exe",
        r"C:\Program Files (x86)\gs\gs9.56.1\bin\gswin32c.exe"
    ]

    for path in paths:
        if os.path.exists(path):
            return path

    # If not found, try to use a portable version in the bin directory
    bin_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "bin")
    portable_gs = os.path.join(bin_dir, "gswin64c.exe")

    if os.path.exists(portable_gs):
        return portable_gs

    # Could add code to download a portable version here

    return None

def print_pdf_with_powershell(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using PowerShell.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        printer_name = profile.printer_name
        logging.info(f"Printing {pdf_path} with PowerShell")
        logging.info(f"Using printer: {printer_name}")
        logging.info(f"Paper size: {profile.paper_size} (1=Letter, 3=Tabloid)")
        logging.info(f"Orientation: {profile.orientation} (1=Portrait, 2=Landscape)")
        logging.info(f"Duplex: {profile.duplex} (1=Simplex, 2=Duplex)")
        logging.info(f"Color: {profile.color} (1=Monochrome, 2=Color)")

        # Map profile settings to PowerShell values
        paper_size = "Letter" if profile.paper_size == PAPER_LETTER else "Tabloid" if profile.paper_size == PAPER_TABLOID else "Letter"
        orientation = "Portrait" if profile.orientation == ORIENTATION_PORTRAIT else "Landscape"
        duplex = "Simplex" if profile.duplex == DUPLEX_SIMPLEX else "Vertical" if profile.duplex == DUPLEX_VERTICAL else "Horizontal"
        color = "Monochrome" if profile.color == COLOR_MONOCHROME else "Color"

        # Create a temporary PowerShell script
        fd, ps_path = tempfile.mkstemp(suffix='.ps1')
        os.close(fd)

        try:
            # Write the PowerShell script content
            with open(ps_path, 'w') as f:
                f.write(f"""
# PowerShell script to print PDF
$printer = "{printer_name}"
$file = "{pdf_path}"
$paperSize = "{paper_size}"
$orientation = "{orientation}"
$duplex = "{duplex}"
$color = "{color}"

# Log settings
Write-Output "Printing with settings:"
Write-Output "  Printer: $printer"
Write-Output "  Paper Size: $paperSize"
Write-Output "  Orientation: $orientation"
Write-Output "  Duplex: $duplex"
Write-Output "  Color: $color"

# Set default printer
(New-Object -ComObject WScript.Network).SetDefaultPrinter($printer)

# Configure printer settings using RUNDLL32
try {{
    Write-Output "Configuring printer settings..."
    $paperSizeArg = if ($paperSize -eq "Letter") {{ "LETTER" }} else {{ "TABLOID" }}
    $orientationArg = if ($orientation -eq "Portrait") {{ "PORTRAIT" }} else {{ "LANDSCAPE" }}
    $duplexArg = if ($duplex -eq "Simplex") {{ "NONE" }} else {{ $duplex.ToUpper() }}

    # Set printer as default and configure settings
    & RUNDLL32 PRINTUI.DLL,PrintUIEntry /y /n "$printer"
    & RUNDLL32 PRINTUI.DLL,PrintUIEntry /Xs /n "$printer" /a "paper=$paperSizeArg" "orientation=$orientationArg" "duplex=$duplexArg"

    # Wait for settings to apply
    Start-Sleep -Seconds 2
}}
catch {{
    Write-Output "Error configuring printer settings: $_"
}}

# Try multiple printing methods
try {{
    # Method 1: Use Start-Process with print verb
    Write-Output "Method 1: Using Start-Process with print verb"
    Start-Process -FilePath $file -Verb Print -Wait
}}
catch {{
    Write-Output "Method 1 failed: $_"

    try {{
        # Method 2: Use .NET printing
        Write-Output "Method 2: Using .NET printing"
        Add-Type -AssemblyName System.Drawing
        $printDoc = New-Object System.Drawing.Printing.PrintDocument
        $printDoc.PrinterSettings.PrinterName = $printer
        $printDoc.DocumentName = $file
        $printDoc.Print()
    }}
    catch {{
        Write-Output "Method 2 failed: $_"

        try {{
            # Method 3: Use rundll32
            Write-Output "Method 3: Using rundll32"
            & rundll32.exe shell32.dll,ShellExec_RunDLL $file /p
        }}
        catch {{
            Write-Output "Method 3 failed: $_"

            # Method 4: Use shell COM object
            Write-Output "Method 4: Using shell COM object"
            $shell = New-Object -ComObject Shell.Application
            $shell.ShellExecute($file, "/p", "", "print", 0)
        }}
    }}
}}
""")

            # Execute the PowerShell script
            logging.info(f"Executing PowerShell script: {ps_path}")
            result = subprocess.run(["powershell.exe", "-ExecutionPolicy", "Bypass", "-File", ps_path],
                                   check=True, capture_output=True, text=True)

            # Log the output
            if result.stdout:
                logging.info(f"PowerShell output: {result.stdout}")
            if result.stderr:
                logging.warning(f"PowerShell error: {result.stderr}")

            # Wait for the print job to be processed
            time.sleep(5)

            return True
        finally:
            # Clean up the PowerShell script
            try:
                os.remove(ps_path)
            except:
                pass
    except Exception as e:
        logging.error(f"Error printing with PowerShell: {e}")
        return False

def print_pdf_with_acrobat_reader_dc(pdf_path: str, printer_name: str) -> bool:
    """
    Print a PDF file using Adobe Acrobat Reader DC with command line.

    Args:
        pdf_path: The path to the PDF file
        printer_name: The name of the printer

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if Adobe Reader is installed
        adobe_paths = [
            r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
            r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
            r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe"
        ]

        adobe_path = None
        for path in adobe_paths:
            if os.path.exists(path):
                adobe_path = path
                break

        if not adobe_path:
            logging.warning("Adobe Reader not found")
            return False

        logging.info(f"Printing {pdf_path} with Adobe Reader")
        logging.info(f"Using printer: {printer_name}")
        logging.info(f"Adobe Reader path: {adobe_path}")

        # Build the command
        cmd = [
            adobe_path,
            "/t", pdf_path, printer_name
        ]

        # Log the command
        logging.info(f"Adobe Reader command: {' '.join(cmd)}")

        # Execute the command
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        # Log the output
        if result.stdout:
            logging.info(f"Adobe Reader output: {result.stdout}")
        if result.stderr:
            logging.warning(f"Adobe Reader error: {result.stderr}")

        # Wait for the print job to be processed
        time.sleep(3)

        return True
    except Exception as e:
        logging.error(f"Error printing with Adobe Reader: {e}")
        return False

def print_pdf_with_direct_methods(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using multiple direct methods, trying each one until successful.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if any method was successful, False otherwise
    """
    logging.info(f"Attempting to print {pdf_path} using multiple direct methods")

    # Try PowerShell first
    logging.info("Trying PowerShell method...")
    if print_pdf_with_powershell(pdf_path, profile):
        logging.info("Successfully printed with PowerShell")
        return True

    # Try Adobe Reader
    logging.info("Trying Adobe Reader method...")
    if print_pdf_with_acrobat_reader_dc(pdf_path, profile.printer_name):
        logging.info("Successfully printed with Adobe Reader")
        return True

    # Try Ghostscript
    logging.info("Trying Ghostscript method...")
    if print_pdf_with_ghostscript(pdf_path, profile.printer_name):
        logging.info("Successfully printed with Ghostscript")
        return True

    # If all methods failed
    logging.error("All direct printing methods failed")
    return False
