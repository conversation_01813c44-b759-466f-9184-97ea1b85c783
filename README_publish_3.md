# Publish 3 Tool

This tool publishes engineering projects by updating title block data, exporting to PDF/DXF, and optionally creating manuals. It streamlines the process of finalizing engineering documentation and preparing it for distribution.

## Features

- Read title block data from the project
- Update attributes in the project
- **Wire Core Synchronization**: Automatically sync wire numbers to core properties before running reports
- **Enhanced Report Generation**: Run configurable BOM reports plus additional reports including:
  - Field Cable List
  - Field Cable List without Cores
- Export to PDF and DXF formats
- Optionally create manuals based on the selected model
- Save job data to JSON file in the project folder
- Dark mode user interface for improved visibility
- Support for multiple engineering models
- **Fill Series**: Batch publishing with auto-incrementing serial numbers
- Automatic numeric serial number detection and incrementing

## Installation

### Option 1: Run the Python Script

1. Ensure you have Python 3.8 or higher installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the script:
   ```
   python apps/publish_3.py
   ```

### Option 2: Use the Compiled Executable

1. Download the `publish_3.exe` file from the `dist` folder
2. Copy it to any location on your computer
3. Double-click to run the application

## Usage

1. Launch the application
2. Fill in the project information fields:
   - GSS Parent #
   - Serial number
   - Customer
   - Location
   - Title
   - Sales order #
3. Select the appropriate model from the dropdown
4. Click the "Browse" button to select a folder for saving files
5. Check "Create Manual" if you want to generate a technical manual
6. **Optional**: Check "Fill Series" for batch publishing:
   - Enter the quantity in the "Count" field
   - The application will automatically increment the serial number for each unit
7. Click "Publish" to process the project(s)

The application will:
- Update the title block in the project with the entered information
- Export the project to PDF format
- Export data plates to DXF format in a "DataPlates" directory
- If "Create Manual" is checked, generate a technical manual using the appropriate template for the selected model
- Save all job data to a JSON file in the selected folder for future reference
- If "Fill Series" is checked, repeat the process for each unit in the series with auto-incremented serial numbers

## Requirements

- Windows operating system
- The application that provides the COM interface must be installed and running
- Microsoft Word (for manual creation feature)
- For the Python script: Python 3.8 or higher with the required dependencies
- For the executable: No additional Python requirements

## Troubleshooting

If you encounter issues:

1. Check that the application providing the COM interface is running
2. Ensure you have the necessary permissions to access the project
3. Check the log file for detailed error messages:
   - When running the Python script: `publish_debug.log`
   - When running the executable: `%USERPROFILE%/publish_3.log`
4. For manual creation issues:
   - Ensure Microsoft Word is installed and functioning
   - Check that the template and drawings paths in models.json are correct
   - Verify that the model is properly defined in models.json

## Building the Executable

To build the executable yourself:

1. Ensure you have PyInstaller installed:
   ```
   pip install pyinstaller
   ```

2. Run the compilation batch file:
   ```
   compile_publish_3.bat
   ```

3. The executable will be created in the `dist` folder

## Configuration

The application uses the following configuration files:

- `models.json`: Contains information about engineering models, templates, and drawing paths
- `config.json`: Contains general configuration settings

These files are stored in the user data directory after setup. The application first looks for configuration files in the user data directory, then in the resources/config directory.

### Models Configuration

The `models.json` file contains definitions for all supported engineering models, including:

- Model categories and names
- Template paths for manual creation
- Drawing paths for including in manuals
- ASME certification flags
- Controls parent IDs

You can add new models using the `add_model.py` script.

## Fill Series Feature

The Fill Series feature allows for batch publishing of multiple units with automatically incrementing serial numbers:

### How It Works
- Enter the starting serial number in the "Serial number" field
- Check the "Fill Series" checkbox
- Enter the desired quantity in the "Count" field
- The application will automatically detect numeric patterns in serial numbers and increment them
- Supports both purely numeric serial numbers (e.g., "12345") and alphanumeric with trailing numbers (e.g., "ABC123")

### Example
- Starting serial: "ABC123"
- Count: 3
- Results: "ABC123", "ABC124", "ABC125"

## Notes

- The "Create Manual" feature requires Microsoft Word to be installed
- The application will create a "DataPlates" directory one level up from the selected job folder for DXF exports
- The application will save a JSON file with all job data in the selected folder
- The dark mode interface can be customized by modifying the theme settings in the code
- The application uses the COM interface to interact with the engineering software
- Error logs are saved to help troubleshoot any issues that may occur during publishing
- When using Fill Series, each unit is published sequentially with appropriate status updates

