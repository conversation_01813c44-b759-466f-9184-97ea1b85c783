"""
Unit tests for the custom exception hierarchy.
"""

import pytest
from apps.publish.exceptions import (
    PublishError, ValidationError, E3ConnectionError, ExportError,
    ConfigurationError, FileOperationError, ServiceError, 
    IntegrationError, GUIError
)


class TestPublishError:
    """Test the base PublishError class."""
    
    def test_basic_initialization(self):
        """Test basic error initialization."""
        error = PublishError("Test error message")
        assert str(error) == "Test error message"
        assert error.message == "Test error message"
        assert error.context == {}
        assert error.cause is None
    
    def test_initialization_with_context(self):
        """Test error initialization with context."""
        context = {"key1": "value1", "key2": 42}
        error = PublishError("Test error", context=context)
        assert error.context == context
        assert "key1=value1" in str(error)
        assert "key2=42" in str(error)
    
    def test_initialization_with_cause(self):
        """Test error initialization with underlying cause."""
        cause = ValueError("Original error")
        error = PublishError("Test error", cause=cause)
        assert error.cause == cause
        assert "Caused by: Original error" in str(error)
    
    def test_add_context(self):
        """Test adding context to an error."""
        error = PublishError("Test error")
        result = error.add_context("test_key", "test_value")
        
        # Should return self for chaining
        assert result is error
        assert error.context["test_key"] == "test_value"
        assert "test_key=test_value" in str(error)
    
    def test_context_chaining(self):
        """Test chaining context additions."""
        error = PublishError("Test error")
        error.add_context("key1", "value1").add_context("key2", "value2")
        
        assert error.context["key1"] == "value1"
        assert error.context["key2"] == "value2"


class TestValidationError:
    """Test the ValidationError class."""
    
    def test_basic_initialization(self):
        """Test basic validation error initialization."""
        error = ValidationError("Invalid field value")
        assert isinstance(error, PublishError)
        assert str(error) == "Invalid field value"
    
    def test_initialization_with_field_and_value(self):
        """Test validation error with field and value."""
        error = ValidationError("Invalid value", field="username", value="")
        assert error.field == "username"
        assert error.value == ""
        assert "field=username" in str(error)
        assert "value=" in str(error)
    
    def test_inheritance(self):
        """Test that ValidationError inherits from PublishError."""
        error = ValidationError("Test error")
        assert isinstance(error, PublishError)


class TestE3ConnectionError:
    """Test the E3ConnectionError class."""
    
    def test_basic_initialization(self):
        """Test basic E3 connection error initialization."""
        error = E3ConnectionError("Failed to connect to E3")
        assert isinstance(error, PublishError)
        assert str(error) == "Failed to connect to E3"
    
    def test_initialization_with_operation(self):
        """Test E3 connection error with operation."""
        error = E3ConnectionError("Connection failed", operation="GetApplication")
        assert error.operation == "GetApplication"
        assert "operation=GetApplication" in str(error)


class TestExportError:
    """Test the ExportError class."""
    
    def test_basic_initialization(self):
        """Test basic export error initialization."""
        error = ExportError("Export failed")
        assert isinstance(error, PublishError)
        assert str(error) == "Export failed"
    
    def test_initialization_with_export_details(self):
        """Test export error with export type and path."""
        error = ExportError("PDF export failed", export_type="PDF", output_path="/path/to/output.pdf")
        assert error.export_type == "PDF"
        assert error.output_path == "/path/to/output.pdf"
        assert "export_type=PDF" in str(error)
        assert "output_path=/path/to/output.pdf" in str(error)


class TestConfigurationError:
    """Test the ConfigurationError class."""
    
    def test_basic_initialization(self):
        """Test basic configuration error initialization."""
        error = ConfigurationError("Configuration loading failed")
        assert isinstance(error, PublishError)
        assert str(error) == "Configuration loading failed"
    
    def test_initialization_with_config_details(self):
        """Test configuration error with file and key."""
        error = ConfigurationError("Invalid config", config_file="config.json", config_key="database.host")
        assert error.config_file == "config.json"
        assert error.config_key == "database.host"
        assert "config_file=config.json" in str(error)
        assert "config_key=database.host" in str(error)


class TestFileOperationError:
    """Test the FileOperationError class."""
    
    def test_basic_initialization(self):
        """Test basic file operation error initialization."""
        error = FileOperationError("File operation failed")
        assert isinstance(error, PublishError)
        assert str(error) == "File operation failed"
    
    def test_initialization_with_file_details(self):
        """Test file operation error with path and operation."""
        error = FileOperationError("Cannot create directory", file_path="/path/to/dir", operation="mkdir")
        assert error.file_path == "/path/to/dir"
        assert error.operation == "mkdir"
        assert "file_path=/path/to/dir" in str(error)
        assert "operation=mkdir" in str(error)


class TestServiceError:
    """Test the ServiceError class."""
    
    def test_basic_initialization(self):
        """Test basic service error initialization."""
        error = ServiceError("Service operation failed")
        assert isinstance(error, PublishError)
        assert str(error) == "Service operation failed"
    
    def test_initialization_with_service_details(self):
        """Test service error with service and operation."""
        error = ServiceError("Publish failed", service="PublishService", operation="publish_project")
        assert error.service == "PublishService"
        assert error.operation == "publish_project"
        assert "service=PublishService" in str(error)
        assert "operation=publish_project" in str(error)


class TestIntegrationError:
    """Test the IntegrationError class."""
    
    def test_basic_initialization(self):
        """Test basic integration error initialization."""
        error = IntegrationError("Integration failed")
        assert isinstance(error, PublishError)
        assert str(error) == "Integration failed"
    
    def test_initialization_with_integration_details(self):
        """Test integration error with integration and operation."""
        error = IntegrationError("Report generation failed", integration="ReportGenerator", operation="run_bom_report")
        assert error.integration == "ReportGenerator"
        assert error.operation == "run_bom_report"
        assert "integration=ReportGenerator" in str(error)
        assert "operation=run_bom_report" in str(error)


class TestGUIError:
    """Test the GUIError class."""
    
    def test_basic_initialization(self):
        """Test basic GUI error initialization."""
        error = GUIError("GUI operation failed")
        assert isinstance(error, PublishError)
        assert str(error) == "GUI operation failed"
    
    def test_initialization_with_gui_details(self):
        """Test GUI error with widget and operation."""
        error = GUIError("Widget creation failed", widget="MainWindow", operation="setup_ui")
        assert error.widget == "MainWindow"
        assert error.operation == "setup_ui"
        assert "widget=MainWindow" in str(error)
        assert "operation=setup_ui" in str(error)


class TestExceptionHierarchy:
    """Test the overall exception hierarchy."""
    
    def test_all_exceptions_inherit_from_publish_error(self):
        """Test that all custom exceptions inherit from PublishError."""
        exceptions = [
            ValidationError("test"),
            E3ConnectionError("test"),
            ExportError("test"),
            ConfigurationError("test"),
            FileOperationError("test"),
            ServiceError("test"),
            IntegrationError("test"),
            GUIError("test")
        ]
        
        for exception in exceptions:
            assert isinstance(exception, PublishError)
            assert isinstance(exception, Exception)
    
    def test_exception_context_preservation(self):
        """Test that context is preserved through exception hierarchy."""
        original_cause = ValueError("Original error")
        context = {"test_key": "test_value"}
        
        error = ValidationError("Validation failed", context=context, cause=original_cause)
        
        assert error.context == context
        assert error.cause == original_cause
        assert isinstance(error, PublishError)