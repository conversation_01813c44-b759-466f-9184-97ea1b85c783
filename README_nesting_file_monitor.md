# Nesting File Monitor Tool

The Nesting File Monitor Tool is an automated monitoring utility that watches specified directories for new or modified STEP and PDF files, then sends email notifications via Microsoft Outlook. This tool is designed to streamline the nesting workflow by automatically notifying team members when new files are available for processing.

## Features

- **Continuous Monitoring**: Watches directories for file changes in real-time
- **File Type Detection**: Monitors STEP (.step, .stp) and PDF files specifically
- **Change Detection**: Identifies both new files and modifications to existing files
- **Email Notifications**: Automatic email alerts via Microsoft Outlook
- **File Size Validation**: Checks attachment size limits for email compatibility
- **State Persistence**: Remembers previous monitoring state between sessions
- **Batch Processing**: Groups multiple file changes into single email notifications
- **Error Handling**: Robust error handling with detailed logging

## Installation

### Prerequisites

1. Python 3.8 or higher
2. Microsoft Outlook installed and configured
3. Required Python packages:
   ```
   pip install -r requirements.txt
   ```

### Setup

1. Configure the monitoring directory and target email in the configuration files
2. Ensure Microsoft Outlook is properly configured with an active email account
3. Run the tool either directly or via the Engineering Tools Launcher

## Usage

### Direct Execution

```bash
python apps/nesting_file_monitor.py
```

### Via Engineering Tools Launcher

1. Run the launcher:
   ```
   python apps_launcher.py
   ```
2. Click "Launch" next to "Nesting File Monitor"

### Configuration

The tool requires configuration of:
- **Monitoring Directory**: The folder to watch for file changes
- **Target Email**: The recipient email address for notifications
- **File Extensions**: Supported file types (STEP and PDF by default)

## How It Works

### Monitoring Process

1. **Initial Scan**: Loads previous monitoring state from saved data
2. **File Discovery**: Scans the monitoring directory for supported file types
3. **Change Detection**: Compares current file modification times with saved state
4. **Notification**: Sends email alerts for new or modified files
5. **State Update**: Saves current state for next monitoring session

### File Detection

The monitor tracks files with these extensions:
- `.step` - STEP files (Standard for Exchange of Product Data)
- `.stp` - Alternative STEP file extension
- `.pdf` - PDF documents

### Email Notifications

When changes are detected, the tool:
- Creates an email with a descriptive subject line
- Lists all modified/new files in the email body
- Attempts to attach files (subject to size limits)
- Provides warnings for files that cannot be attached
- Includes the monitoring directory path for reference

## Configuration Files

### State Persistence

The tool maintains state information in:
- **Last Run State**: Tracks file modification times from previous execution
- **Configuration Data**: Stores monitoring directory and email settings

### Email Settings

Default configuration includes:
- **Target Email**: <EMAIL> (configurable)
- **Subject Format**: "Nesting Files - [Date] - [File Count] files modified"
- **Attachment Limit**: 20MB per file (Outlook limitation)

## Email Content

### Subject Line
```
Hey Sammy, there were [X] files modified or created today. Can you nest or renest them?
```

### Email Body
- Count of modified/new files
- List of successfully attached files
- Warnings for files that couldn't be attached (size/access issues)
- Reference to the monitoring directory for manual access

### Attachments
- All modified/new files within size limits
- Automatic size checking to prevent email delivery failures

## Troubleshooting

### Common Issues

1. **No Files Detected**
   - Verify the monitoring directory path is correct
   - Check that files have the supported extensions (.step, .stp, .pdf)
   - Ensure the tool has read access to the directory

2. **Email Send Failures**
   - Confirm Microsoft Outlook is installed and configured
   - Check network connectivity
   - Verify the target email address is valid

3. **File Access Errors**
   - Ensure files are not locked by other applications
   - Check file permissions in the monitoring directory
   - Verify the tool has appropriate access rights

4. **Large File Warnings**
   - Files over 20MB cannot be attached to emails
   - These files are listed in the email body with warnings
   - Manual transfer may be required for oversized files

### Log Files

The tool generates detailed logs for troubleshooting:
- Real-time console output during execution
- Error messages with stack traces for debugging
- File processing status and results

## Technical Details

### File Monitoring Algorithm

1. **Recursive Directory Scan**: Walks through all subdirectories
2. **Modification Time Comparison**: Uses file system timestamps
3. **State Serialization**: Saves monitoring state between runs
4. **Batch Processing**: Groups changes for efficient notification

### Email Integration

- **COM Interface**: Uses `win32com.client` for Outlook automation
- **Attachment Handling**: Automatic file size validation
- **Error Recovery**: Continues processing if individual files fail

### Performance Considerations

- **Efficient Scanning**: Only processes files with supported extensions
- **State Caching**: Avoids re-processing unchanged files
- **Memory Management**: Handles large directory structures efficiently

## Integration

### Workflow Integration

The Nesting File Monitor integrates with:
- **CAD Export Processes**: Monitors output directories from design tools
- **File Management Systems**: Works with any directory structure
- **Team Collaboration**: Automated notifications keep team members informed

### Automation Opportunities

- **Scheduled Execution**: Can be run via Windows Task Scheduler
- **Batch Processing**: Handles multiple file changes efficiently
- **Workflow Triggers**: Notifications can trigger downstream processes

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.

## Contact

For support or questions about the Nesting File Monitor Tool:
Jonathan Callahan: <EMAIL>
