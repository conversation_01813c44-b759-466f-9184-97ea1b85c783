"""
Unit tests for series generation utilities.
"""

import unittest
from apps.publish.utils.series_generator import SeriesGenerator, SerialFormat, SeriesGeneratorError


class TestSeriesGenerator(unittest.TestCase):
    """Test cases for SeriesGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = SeriesGenerator()
    
    def test_increment_serial_basic(self):
        """Test basic serial increment functionality."""
        # Test numeric increment
        result = self.generator.increment_serial("123")
        self.assertEqual(result, "124")
        
        # Test empty/None input
        result = self.generator.increment_serial("")
        self.assertIsNone(result)
        
        result = self.generator.increment_serial(None)
        self.assertIsNone(result)
    
    def test_increment_numeric_serial(self):
        """Test incrementing numeric serials."""
        test_cases = [
            ("123", "124"),
            ("999", "1000"),
            ("0", "1"),
        ]
        
        for input_serial, expected in test_cases:
            with self.subTest(input_serial=input_serial):
                result = self.generator.increment_serial(input_serial)
                self.assertEqual(result, expected)
    
    def test_validate_serial_format(self):
        """Test serial format validation."""
        valid_serials = ["123", "ABC123", "TEST001", "A", "XYZ"]
        invalid_serials = ["", "   ", None]
        
        for serial in valid_serials:
            with self.subTest(serial=serial):
                result = self.generator.validate_serial_format(serial)
                self.assertTrue(result, f"Serial {serial} should be valid")
        
        for serial in invalid_serials:
            with self.subTest(serial=serial):
                result = self.generator.validate_serial_format(serial)
                self.assertFalse(result, f"Serial {serial} should be invalid")
    
    def test_generate_series_numeric(self):
        """Test generating numeric series."""
        result = self.generator.generate_series("100", 5)
        expected = ["100", "101", "102", "103", "104"]
        self.assertEqual(result, expected)
    
    def test_generate_series_invalid_count(self):
        """Test generating series with invalid count."""
        with self.assertRaises(SeriesGeneratorError):
            self.generator.generate_series("123", 0)
        
        with self.assertRaises(SeriesGeneratorError):
            self.generator.generate_series("123", -1)
    
    def test_increment_serial_edge_cases(self):
        """Test edge cases for serial increment."""
        # Test None input
        result = self.generator.increment_serial(None)
        self.assertIsNone(result)
        
        # Test empty string
        result = self.generator.increment_serial("")
        self.assertIsNone(result)
        
        # Test alphabetic input (should increment to ABD)
        result = self.generator.increment_serial("ABC")
        self.assertEqual(result, "ABD")


if __name__ == '__main__':
    unittest.main()