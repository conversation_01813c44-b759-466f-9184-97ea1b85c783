"""
Wire Core Synchronization Integration for Publish System.

This module provides integration between the publish system and the wire core
synchronization functionality, allowing wire core sync to be run as part of
the publishing process.
"""

import logging
from typing import Optional
from ..exceptions import PublishError


class WireCoreSyncError(Exception):
    """Base exception for wire core synchronization errors."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None):
        super().__init__(message)
        self.operation = operation
        self.original_error = original_error


class WireCoreSyncClient:
    """Client for wire core synchronization integration."""
    
    def __init__(self):
        """Initialize the wire core sync client."""
        self.logger = logging.getLogger(__name__)
        
    def run_wire_core_sync(self, e3_pid: Optional[int] = None) -> bool:
        """
        Run wire core synchronization for the current E3 project.
        
        Args:
            e3_pid: Optional E3 process ID to connect to specific instance
            
        Returns:
            True if synchronization was successful, False otherwise
            
        Raises:
            WireCoreSyncError: If critical errors occur during synchronization
        """
        try:
            self.logger.info("Starting wire core synchronization")
            
            # Import the wire core synchronizer
            try:
                # Try relative import first (when running as part of publish system)
                from ....lib.e3_wire_core_sync import WireCoreSynchronizer
            except ImportError:
                try:
                    # Try absolute import (when lib is in path)
                    from lib.e3_wire_core_sync import WireCoreSynchronizer
                except ImportError:
                    # Last resort - try direct import
                    import sys
                    import os
                    
                    # Add lib directory to path
                    lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'lib')
                    if lib_path not in sys.path:
                        sys.path.insert(0, lib_path)
                    
                    from e3_wire_core_sync import WireCoreSynchronizer
            
            # Create and run the synchronizer
            synchronizer = WireCoreSynchronizer(e3_pid=e3_pid)
            success = synchronizer.run()
            
            if success:
                self.logger.info("Wire core synchronization completed successfully")
                return True
            else:
                self.logger.error("Wire core synchronization failed")
                return False
                
        except ImportError as e:
            error_msg = f"Failed to import wire core synchronizer: {e}"
            self.logger.error(error_msg)
            raise WireCoreSyncError(error_msg, "import", e)
            
        except Exception as e:
            error_msg = f"Error during wire core synchronization: {e}"
            self.logger.error(error_msg)
            raise WireCoreSyncError(error_msg, "synchronization", e)
    
    def validate_prerequisites(self) -> bool:
        """
        Validate that prerequisites for wire core sync are met.
        
        Returns:
            True if prerequisites are met, False otherwise
        """
        try:
            # Check if wire core sync module is available
            try:
                from ....lib.e3_wire_core_sync import WireCoreSynchronizer
            except ImportError:
                try:
                    from lib.e3_wire_core_sync import WireCoreSynchronizer
                except ImportError:
                    import sys
                    import os
                    
                    lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'lib')
                    if lib_path not in sys.path:
                        sys.path.insert(0, lib_path)
                    
                    from e3_wire_core_sync import WireCoreSynchronizer
            
            self.logger.debug("Wire core sync prerequisites validated successfully")
            return True
            
        except ImportError as e:
            self.logger.warning(f"Wire core sync prerequisites not met: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error validating wire core sync prerequisites: {e}")
            return False
