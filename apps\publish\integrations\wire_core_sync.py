"""
Wire Core Synchronization Integration for Publish System.

This module provides integration between the publish system and the wire core
synchronization functionality, allowing wire core sync to be run as part of
the publishing process.
"""

import logging
from typing import Optional
from ..exceptions import PublishError


class WireCoreSyncError(Exception):
    """Base exception for wire core synchronization errors."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None):
        super().__init__(message)
        self.operation = operation
        self.original_error = original_error


class WireCoreSyncClient:
    """Client for wire core synchronization integration."""
    
    def __init__(self):
        """Initialize the wire core sync client."""
        self.logger = logging.getLogger(__name__)
        
    def run_wire_core_sync(self, e3_pid: Optional[int] = None, e3_client=None) -> bool:
        """
        Run wire core synchronization for the current E3 project.

        Args:
            e3_pid: Optional E3 process ID to connect to specific instance
            e3_client: Optional existing E3Client to reuse COM objects from

        Returns:
            True if synchronization was successful, False otherwise

        Raises:
            WireCoreSyncError: If critical errors occur during synchronization
        """
        try:
            self.logger.info("Starting wire core synchronization")

            # If we have an existing E3Client, reuse its COM objects to avoid conflicts
            if e3_client and e3_client.is_connected():
                self.logger.info("Reusing existing E3Client COM objects for wire core sync")
                synchronizer = WireCoreSynchronizerFromE3Client(e3_client, logger=self.logger)
                success = synchronizer.run()
            else:
                # Fall back to creating new connection (standalone mode)
                self.logger.info("Creating new E3 connection for wire core sync")
                synchronizer = WireCoreSynchronizerIntegrated(e3_pid=e3_pid, logger=self.logger)
                success = synchronizer.run()

            if success:
                self.logger.info("Wire core synchronization completed successfully")
                return True
            else:
                self.logger.error("Wire core synchronization failed")
                return False

        except Exception as e:
            error_msg = f"Error during wire core synchronization: {e}"
            self.logger.error(error_msg)
            raise WireCoreSyncError(error_msg, "synchronization", e)
    
    def validate_prerequisites(self) -> bool:
        """
        Validate that prerequisites for wire core sync are met.
        
        Returns:
            True if prerequisites are met, False otherwise
        """
        try:
            # Check if wire core sync module is available
            try:
                from ....lib.e3_wire_core_sync import WireCoreSynchronizer
            except ImportError:
                try:
                    from lib.e3_wire_core_sync import WireCoreSynchronizer
                except ImportError:
                    import sys
                    import os
                    
                    lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'lib')
                    if lib_path not in sys.path:
                        sys.path.insert(0, lib_path)
                    
                    from e3_wire_core_sync import WireCoreSynchronizer
            
            self.logger.debug("Wire core sync prerequisites validated successfully")
            return True
            
        except ImportError as e:
            self.logger.warning(f"Wire core sync prerequisites not met: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error validating wire core sync prerequisites: {e}")
            return False


class WireCoreSynchronizerIntegrated:
    """
    Integrated wire core synchronizer that works with the publish system's COM management.

    This version doesn't call CoUninitialize() and integrates properly with the
    publish system's resource management.
    """

    def __init__(self, e3_pid: Optional[int] = None, logger=None):
        """
        Initialize the integrated wire core synchronizer.

        Args:
            e3_pid: Optional E3 process ID to connect to specific instance
            logger: Logger instance to use
        """
        self.e3_pid = e3_pid
        self.logger = logger or logging.getLogger(__name__)

        # E3 API objects
        self.app = None
        self.job = None
        self.connection = None
        self.net_segment = None
        self.pin = None

    def connect_to_e3(self):
        """Connect to E3 application using connection manager"""
        try:
            # Get E3 PID if not already provided
            if self.e3_pid is None:
                try:
                    from ....lib.e3_connection_manager import get_e3_connection_pid
                except ImportError:
                    # Handle standalone execution
                    try:
                        from lib.e3_connection_manager import get_e3_connection_pid
                    except ImportError:
                        import sys
                        import os
                        lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'lib')
                        if lib_path not in sys.path:
                            sys.path.insert(0, lib_path)
                        from e3_connection_manager import get_e3_connection_pid

                self.e3_pid = get_e3_connection_pid(self.logger)
                if self.e3_pid is None:
                    return False

            # Connect using the PID
            try:
                from ....lib.e3_connection_manager import connect_to_e3_with_pid
            except ImportError:
                # Handle standalone execution
                try:
                    from lib.e3_connection_manager import connect_to_e3_with_pid
                except ImportError:
                    import sys
                    import os
                    lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'lib')
                    if lib_path not in sys.path:
                        sys.path.insert(0, lib_path)
                    from e3_connection_manager import connect_to_e3_with_pid

            success, objects = connect_to_e3_with_pid(self.e3_pid, self.logger)
            if success:
                self.app = objects['app']
                self.job = objects['job']
                self.connection = objects['connection']
                self.net_segment = objects['net_segment']
                self.pin = objects['pin']

                # Register COM objects with resource manager
                from ..utils.resource_manager import get_resource_manager
                resource_manager = get_resource_manager()
                resource_manager.register_com_object(self.app, "WireSync_E3Application")
                resource_manager.register_com_object(self.job, "WireSync_E3Job")
                resource_manager.register_com_object(self.connection, "WireSync_E3Connection")
                resource_manager.register_com_object(self.net_segment, "WireSync_E3NetSegment")
                resource_manager.register_com_object(self.pin, "WireSync_E3Pin")

                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"Failed to connect to E3 application: {e}")
            return False

    def get_net_segments_for_connection(self, connection_id: int):
        """Get all net segment IDs for a given connection"""
        try:
            self.connection.SetId(connection_id)
            net_segment_ids_result = self.connection.GetNetSegmentIds()

            if not net_segment_ids_result:
                return []

            actual_net_segments = []
            if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                _ = net_segment_ids_result[0]  # count (not used)
                net_segment_ids = net_segment_ids_result[1]

                if isinstance(net_segment_ids, tuple):
                    actual_net_segments = [nsid for nsid in net_segment_ids if nsid is not None and nsid != 0]
                else:
                    if net_segment_ids is not None and net_segment_ids != 0:
                        actual_net_segments = [net_segment_ids]

            return actual_net_segments

        except Exception as e:
            self.logger.error(f"Error getting net segments for connection {connection_id}: {e}")
            return []

    def get_wire_number_from_net_segment(self, net_segment_id: int):
        """Get wire number attribute from a net segment"""
        try:
            self.net_segment.SetId(net_segment_id)
            wire_number = self.net_segment.GetAttributeValue("Wire number")

            # Check if wire number is empty or None
            if not wire_number or wire_number.strip() == "":
                return None

            return wire_number.strip()

        except Exception as e:
            self.logger.error(f"Error getting wire number from net segment {net_segment_id}: {e}")
            return None

    def set_connection_core_property(self, connection_id: int, wire_number: str):
        """Set the Wire Number (Core) core property on all cores of a connection"""
        try:
            self.connection.SetId(connection_id)

            # Get all core IDs for this connection
            core_ids_result = self.connection.GetCoreIds()
            if not core_ids_result:
                self.logger.debug(f"Connection {connection_id} has no cores")
                return True  # Not an error, just no cores to update

            # Parse the core IDs result
            actual_core_ids = []
            if isinstance(core_ids_result, tuple) and len(core_ids_result) >= 2:
                count = core_ids_result[0]
                core_ids = core_ids_result[1]

                if count > 0:
                    if isinstance(core_ids, tuple):
                        actual_core_ids = [cid for cid in core_ids if cid is not None and cid != 0]
                    else:
                        if core_ids is not None and core_ids != 0:
                            actual_core_ids = [core_ids]
            elif isinstance(core_ids_result, int) and core_ids_result > 0:
                # Single core ID returned directly
                actual_core_ids = [core_ids_result]

            if not actual_core_ids:
                self.logger.debug(f"Connection {connection_id} has no valid cores")
                return True

            # Set the Wire Number (Core) attribute on each core
            cores_updated = 0
            for core_id in actual_core_ids:
                try:
                    self.pin.SetId(core_id)
                    result = self.pin.SetAttributeValue("Wire Number (Core)", wire_number)

                    if result > 0:  # Success returns attribute ID
                        cores_updated += 1
                        self.logger.debug(f"Set 'Wire Number (Core)' to '{wire_number}' for core {core_id}")
                    else:
                        self.logger.warning(f"Failed to set 'Wire Number (Core)' for core {core_id}: SetAttributeValue returned {result}")

                except Exception as e:
                    self.logger.error(f"Error setting core property for core {core_id}: {e}")

            if cores_updated > 0:
                self.logger.info(f"Updated {cores_updated} cores for connection {connection_id} with wire number '{wire_number}'")
                return True
            else:
                self.logger.warning(f"Failed to update any cores for connection {connection_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error setting core property for connection {connection_id}: {e}")
            return False

    def process_connections(self):
        """Process all connections in the project"""
        try:
            # Get all connection IDs
            connection_ids_result = self.job.GetAllConnectionIds()
            if not connection_ids_result:
                self.logger.warning("No connections found in project")
                return

            # E3 API returns (count, tuple_of_ids)
            actual_connections = []
            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                count = connection_ids_result[0]
                connection_ids = connection_ids_result[1]

                self.logger.info(f"E3 reports {count} connections")

                if isinstance(connection_ids, tuple):
                    # Filter out None values
                    actual_connections = [cid for cid in connection_ids if cid is not None]
                else:
                    actual_connections = [connection_ids] if connection_ids is not None else []
            else:
                self.logger.warning(f"Unexpected connection IDs format: {type(connection_ids_result)}")
                return

            self.logger.info(f"Processing {len(actual_connections)} connections")

            updated_count = 0
            skipped_count = 0
            error_count = 0

            for connection_id in actual_connections:
                try:
                    # Get net segments for this connection
                    net_segment_ids = self.get_net_segments_for_connection(connection_id)

                    if not net_segment_ids:
                        self.logger.debug(f"Connection {connection_id} has no net segments, skipping")
                        skipped_count += 1
                        continue

                    # Get wire number from the first net segment (they should all have the same wire number)
                    wire_number = None
                    for net_segment_id in net_segment_ids:
                        wire_number = self.get_wire_number_from_net_segment(net_segment_id)
                        if wire_number:
                            break

                    if not wire_number:
                        self.logger.debug(f"Connection {connection_id} has no wire number, skipping")
                        skipped_count += 1
                        continue

                    # Set the core property
                    if self.set_connection_core_property(connection_id, wire_number):
                        updated_count += 1
                        self.logger.info(f"Updated connection {connection_id} core property to '{wire_number}'")
                    else:
                        error_count += 1
                        self.logger.warning(f"Failed to update connection {connection_id} core property")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error processing connection {connection_id}: {e}")

            self.logger.info(f"Wire core synchronization completed: {updated_count} updated, {skipped_count} skipped, {error_count} errors")

        except Exception as e:
            self.logger.error(f"Error during connection processing: {e}")
            raise

    def run(self):
        """Main execution method - integrated version that doesn't call CoUninitialize"""
        self.logger.info("Starting wire core synchronization process")

        if not self.connect_to_e3():
            self.logger.error("Failed to connect to E3. Make sure E3 is running with a project open.")
            return False

        try:
            self.process_connections()
            self.logger.info("Wire core synchronization completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error during wire core synchronization: {e}")
            return False

        finally:
            # Clean up E3.series objects but DON'T call CoUninitialize
            # The publish system's resource manager will handle proper cleanup
            self.app = None
            self.job = None
            self.connection = None
            self.net_segment = None
            self.pin = None


class WireCoreSynchronizerFromE3Client:
    """
    Wire core synchronizer that reuses existing E3Client COM objects.

    This version doesn't create new COM connections, avoiding conflicts
    with the existing E3Client connection.
    """

    def __init__(self, e3_client, logger=None):
        """
        Initialize the synchronizer using existing E3Client.

        Args:
            e3_client: Existing connected E3Client instance
            logger: Logger instance to use
        """
        self.e3_client = e3_client
        self.logger = logger or logging.getLogger(__name__)

        # Use E3Client's COM objects directly
        self.app = e3_client.app
        self.job = e3_client.job

        # Create additional COM objects we need
        self.connection = None
        self.net_segment = None
        self.pin = None

    def _create_additional_objects(self):
        """Create additional COM objects we need for wire core sync"""
        try:
            if self.app and self.job:
                # Use the job object to create the additional COM objects we need
                self.connection = self.job.CreateConnectionObject()
                self.net_segment = self.job.CreateNetSegmentObject()
                self.pin = self.job.CreatePinObject()

                # Register these with resource manager
                from ..utils.resource_manager import get_resource_manager
                resource_manager = get_resource_manager()
                resource_manager.register_com_object(self.connection, "WireSync_Connection")
                resource_manager.register_com_object(self.net_segment, "WireSync_NetSegment")
                resource_manager.register_com_object(self.pin, "WireSync_Pin")

                self.logger.debug("Successfully created additional COM objects for wire core sync")
                return True
            else:
                self.logger.error("Missing app or job objects for creating additional COM objects")
                return False
        except Exception as e:
            self.logger.error(f"Failed to create additional COM objects: {e}")
            return False

    def get_net_segments_for_connection(self, connection_id: int):
        """Get all net segment IDs for a given connection"""
        try:
            self.connection.SetId(connection_id)
            net_segment_ids_result = self.connection.GetNetSegmentIds()

            if not net_segment_ids_result:
                return []

            actual_net_segments = []
            if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                _ = net_segment_ids_result[0]  # count (not used)
                net_segment_ids = net_segment_ids_result[1]

                if isinstance(net_segment_ids, tuple):
                    actual_net_segments = [nsid for nsid in net_segment_ids if nsid is not None and nsid != 0]
                else:
                    if net_segment_ids is not None and net_segment_ids != 0:
                        actual_net_segments = [net_segment_ids]

            return actual_net_segments

        except Exception as e:
            self.logger.error(f"Error getting net segments for connection {connection_id}: {e}")
            return []

    def get_wire_number_from_net_segment(self, net_segment_id: int):
        """Get wire number attribute from a net segment"""
        try:
            self.net_segment.SetId(net_segment_id)
            wire_number = self.net_segment.GetAttributeValue("Wire number")

            # Check if wire number is empty or None
            if not wire_number or wire_number.strip() == "":
                return None

            return wire_number.strip()

        except Exception as e:
            self.logger.error(f"Error getting wire number from net segment {net_segment_id}: {e}")
            return None

    def set_connection_core_property(self, connection_id: int, wire_number: str):
        """Set the Wire Number (Core) core property on all cores of a connection"""
        try:
            self.connection.SetId(connection_id)

            # Get all core IDs for this connection
            core_ids_result = self.connection.GetCoreIds()
            if not core_ids_result:
                self.logger.debug(f"Connection {connection_id} has no cores")
                return True  # Not an error, just no cores to update

            # Parse the core IDs result
            actual_core_ids = []
            if isinstance(core_ids_result, tuple) and len(core_ids_result) >= 2:
                count = core_ids_result[0]
                core_ids = core_ids_result[1]

                if count > 0:
                    if isinstance(core_ids, tuple):
                        actual_core_ids = [cid for cid in core_ids if cid is not None and cid != 0]
                    else:
                        if core_ids is not None and core_ids != 0:
                            actual_core_ids = [core_ids]
            elif isinstance(core_ids_result, int) and core_ids_result > 0:
                # Single core ID returned directly
                actual_core_ids = [core_ids_result]

            if not actual_core_ids:
                self.logger.debug(f"Connection {connection_id} has no valid cores")
                return True

            # Set the Wire Number (Core) attribute on each core
            cores_updated = 0
            for core_id in actual_core_ids:
                try:
                    self.pin.SetId(core_id)
                    result = self.pin.SetAttributeValue("Wire Number (Core)", wire_number)

                    if result > 0:  # Success returns attribute ID
                        cores_updated += 1
                        self.logger.debug(f"Set 'Wire Number (Core)' to '{wire_number}' for core {core_id}")
                    else:
                        self.logger.warning(f"Failed to set 'Wire Number (Core)' for core {core_id}: SetAttributeValue returned {result}")

                except Exception as e:
                    self.logger.error(f"Error setting core property for core {core_id}: {e}")

            if cores_updated > 0:
                self.logger.info(f"Updated {cores_updated} cores for connection {connection_id} with wire number '{wire_number}'")
                return True
            else:
                self.logger.warning(f"Failed to update any cores for connection {connection_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error setting core property for connection {connection_id}: {e}")
            return False

    def process_connections(self):
        """Process all connections in the project"""
        try:
            # Get all connection IDs
            connection_ids_result = self.job.GetAllConnectionIds()
            if not connection_ids_result:
                self.logger.warning("No connections found in project")
                return

            # E3 API returns (count, tuple_of_ids)
            actual_connections = []
            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                count = connection_ids_result[0]
                connection_ids = connection_ids_result[1]

                self.logger.info(f"E3 reports {count} connections")

                if isinstance(connection_ids, tuple):
                    # Filter out None values
                    actual_connections = [cid for cid in connection_ids if cid is not None]
                else:
                    actual_connections = [connection_ids] if connection_ids is not None else []
            else:
                self.logger.warning(f"Unexpected connection IDs format: {type(connection_ids_result)}")
                return

            self.logger.info(f"Processing {len(actual_connections)} connections")

            updated_count = 0
            skipped_count = 0
            error_count = 0

            for connection_id in actual_connections:
                try:
                    # Get net segments for this connection
                    net_segment_ids = self.get_net_segments_for_connection(connection_id)

                    if not net_segment_ids:
                        self.logger.debug(f"Connection {connection_id} has no net segments, skipping")
                        skipped_count += 1
                        continue

                    # Get wire number from the first net segment (they should all have the same wire number)
                    wire_number = None
                    for net_segment_id in net_segment_ids:
                        wire_number = self.get_wire_number_from_net_segment(net_segment_id)
                        if wire_number:
                            break

                    if not wire_number:
                        self.logger.debug(f"Connection {connection_id} has no wire number, skipping")
                        skipped_count += 1
                        continue

                    # Set the core property
                    if self.set_connection_core_property(connection_id, wire_number):
                        updated_count += 1
                        self.logger.info(f"Updated connection {connection_id} core property to '{wire_number}'")
                    else:
                        error_count += 1
                        self.logger.warning(f"Failed to update connection {connection_id} core property")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error processing connection {connection_id}: {e}")

            self.logger.info(f"Wire core synchronization completed: {updated_count} updated, {skipped_count} skipped, {error_count} errors")

        except Exception as e:
            self.logger.error(f"Error during connection processing: {e}")
            raise

    def run(self):
        """Main execution method - reuses existing E3Client COM objects"""
        self.logger.info("Starting wire core synchronization using existing E3Client")

        # Create additional COM objects we need
        if not self._create_additional_objects():
            self.logger.error("Failed to create additional COM objects for wire core sync")
            return False

        try:
            self.process_connections()
            self.logger.info("Wire core synchronization completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error during wire core synchronization: {e}")
            return False

        finally:
            # Clean up only the additional COM objects we created
            # Don't touch the E3Client's main objects (app, job)
            self.connection = None
            self.net_segment = None
            self.pin = None
