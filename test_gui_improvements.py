#!/usr/bin/env python3
"""
Test script for GUI improvements validation.

This script tests the new GUI features including console logging,
progress bar, and background worker functionality.
"""

import sys
import os
import logging
import time
from datetime import datetime

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.abspath(__file__))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Test imports
try:
    from apps.publish.gui.communication import (
        ProgressUpdate, LogMessage, WorkerStatus, WorkerResult, 
        ThreadSafeQueue, EventBus
    )
    from apps.publish.gui.log_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevelFilter
    from apps.publish.gui.workers import BackgroundWorker
    print("✓ All communication and worker imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def test_communication_classes():
    """Test the communication data classes."""
    print("\n=== Testing Communication Classes ===")
    
    # Test ProgressUpdate
    progress = ProgressUpdate(50, 100, "Processing...", "Test Step")
    assert progress.percentage == 50.0
    assert not progress.is_complete
    print("✓ ProgressUpdate working correctly")
    
    # Test LogMessage
    log_msg = LogMessage(
        timestamp=datetime.now(),
        level="INFO",
        message="Test message",
        module="test",
        thread_id="123"
    )
    assert log_msg.formatted_message is not None
    print("✓ LogMessage working correctly")
    
    # Test WorkerResult
    result = WorkerResult(success=True, result_data="test")
    assert result.success
    assert result.warnings == []
    print("✓ WorkerResult working correctly")

def test_thread_safe_queue():
    """Test the thread-safe queue implementation."""
    print("\n=== Testing ThreadSafeQueue ===")
    
    queue = ThreadSafeQueue(maxsize=5)
    
    # Test basic operations
    assert queue.put("item1")
    assert queue.put("item2")
    assert queue.qsize() == 2
    
    item = queue.get(timeout=0.1)
    assert item == "item1"
    
    items = queue.get_all()
    assert "item2" in items
    
    print("✓ ThreadSafeQueue working correctly")

def test_event_bus():
    """Test the event bus implementation."""
    print("\n=== Testing EventBus ===")
    
    bus = EventBus()
    received_events = []
    
    def event_handler(data):
        received_events.append(data)
    
    # Subscribe and publish
    bus.subscribe("test_event", event_handler)
    bus.publish("test_event", "test_data")
    
    time.sleep(0.1)  # Allow event processing
    assert "test_data" in received_events
    
    print("✓ EventBus working correctly")

def test_log_handler():
    """Test the GUI log handler."""
    print("\n=== Testing GUILogHandler ===")
    
    handler = GUILogHandler(max_queue_size=10)
    logger = logging.getLogger("test_logger")
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    # Generate test log messages
    logger.info("Test info message")
    logger.warning("Test warning message")
    logger.error("Test error message")
    
    # Get messages
    messages = handler.get_messages()
    assert len(messages) >= 3
    
    # Test filtering
    filter_obj = LogLevelFilter("WARNING")
    filtered = filter_obj.filter_messages(messages)
    assert len(filtered) >= 2  # WARNING and ERROR
    
    print("✓ GUILogHandler working correctly")

class TestWorker(BackgroundWorker):
    """Test implementation of BackgroundWorker."""
    
    def __init__(self, work_duration=1.0, should_fail=False):
        super().__init__(name="TestWorker")
        self.work_duration = work_duration
        self.should_fail = should_fail
    
    def _do_work(self) -> WorkerResult:
        """Simulate some work."""
        steps = 10
        for i in range(steps):
            self._check_cancellation()
            
            # Report progress
            self._update_progress(
                i + 1, steps, 
                f"Processing step {i + 1}", 
                f"Step {i + 1}"
            )
            
            time.sleep(self.work_duration / steps)
        
        if self.should_fail:
            raise Exception("Simulated worker failure")
        
        return WorkerResult(
            success=True,
            result_data="Test work completed",
            metadata={"steps_completed": steps}
        )

def test_background_worker():
    """Test the background worker implementation."""
    print("\n=== Testing BackgroundWorker ===")
    
    # Test successful worker
    worker = TestWorker(work_duration=0.5)
    
    progress_updates = []
    status_updates = []
    results = []
    
    def on_progress(progress):
        progress_updates.append(progress)
    
    def on_status(status):
        status_updates.append(status)
    
    def on_result(result):
        results.append(result)
    
    worker.set_progress_callback(on_progress)
    worker.set_status_callback(on_status)
    worker.set_result_callback(on_result)
    
    # Start worker and wait for completion
    assert worker.start()
    
    # Wait for completion
    timeout = 5.0
    start_time = time.time()
    while worker.is_running() and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    assert not worker.is_running()
    assert len(results) > 0
    assert results[0].success
    assert len(progress_updates) > 0
    
    print("✓ BackgroundWorker working correctly")

def test_worker_cancellation():
    """Test worker cancellation."""
    print("\n=== Testing Worker Cancellation ===")
    
    worker = TestWorker(work_duration=2.0)  # Longer duration
    
    assert worker.start()
    time.sleep(0.2)  # Let it start
    
    # Cancel the worker
    assert worker.cancel(timeout=1.0)
    assert worker.get_status() == WorkerStatus.CANCELLED
    
    print("✓ Worker cancellation working correctly")

def main():
    """Run all tests."""
    print("Starting GUI Improvements Validation Tests")
    print("=" * 50)
    
    try:
        test_communication_classes()
        test_thread_safe_queue()
        test_event_bus()
        test_log_handler()
        test_background_worker()
        test_worker_cancellation()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed successfully!")
        print("GUI improvements are working correctly.")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
