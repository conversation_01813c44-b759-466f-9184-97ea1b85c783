"""
GUI Module - User Interface Components.

This module contains all user interface components for the publishing application,
built using CustomTkinter for a modern, professional appearance. The GUI follows
the Model-View-Controller (MVC) pattern with clear separation between presentation
and business logic.

The GUI layer is designed to be:
- Responsive and user-friendly
- Accessible and keyboard navigable
- Consistent in design and behavior
- Easily testable through dependency injection
- Decoupled from business logic

Available Components:
    - **MainWindow**: Primary application window with form fields and controls
    - **Custom Widgets**: Reusable UI components (ModelDropdown, SeriesControls)
    - **Dialogs**: Error, confirmation, and information dialogs

Design Principles:
    - MVC Pattern: Clear separation between view and business logic
    - Dependency Injection: Services injected through constructor
    - Event-Driven: User interactions trigger events handled by controllers
    - Responsive Design: UI adapts to different screen sizes and content
    - Accessibility: Keyboard navigation and screen reader support
    - Consistency: Uniform styling and behavior across components

Usage Example:
    ```python
    import customtkinter as ctk
    from apps.publish.gui import MainWindow
    from apps.publish.container import ServiceContainer
    
    # Create application window
    root = ctk.CTk()
    container = ServiceContainer()
    
    # Initialize main window with dependency injection
    main_window = MainWindow(root, container)
    
    # Run application
    root.mainloop()
    ```

Component Architecture:
    - **MainWindow**: Coordinates overall application flow
    - **Widgets**: Encapsulate reusable UI functionality
    - **Dialogs**: Handle user interactions and feedback
    - **Event Handlers**: Process user input and delegate to services

User Experience Features:
    - Form validation with real-time feedback
    - Progress indicators for long-running operations
    - Contextual help and tooltips
    - Keyboard shortcuts for common operations
    - Drag-and-drop support where appropriate
    - Auto-save of user preferences

Error Handling:
    - User-friendly error messages
    - Graceful degradation on component failures
    - Input validation with clear feedback
    - Recovery suggestions for common errors
    - Detailed logging for debugging

Accessibility Features:
    - Keyboard navigation support
    - High contrast mode compatibility
    - Screen reader friendly labels
    - Consistent tab order
    - Appropriate focus indicators

Customization:
    - Theme support (light/dark modes)
    - Configurable layouts
    - User preference persistence
    - Scalable fonts and UI elements

Testing:
    GUI components support automated testing:
    - Mock service injection for unit tests
    - Event simulation for interaction testing
    - Component isolation for focused testing
    - Accessibility testing support

Performance:
    - Lazy loading of heavy components
    - Efficient event handling
    - Minimal UI blocking operations
    - Responsive progress feedback
    - Memory-efficient widget management

Author: E3 Automation Team
"""

from .main_window import MainWindow
from .widgets import ModelDropdown, SeriesControls
from .dialogs import ErrorDialog, ConfirmationDialog

__all__ = [
    'MainWindow',
    'ModelDropdown',
    'SeriesControls', 
    'ErrorDialog',
    'ConfirmationDialog',
]