"""
Unit tests for the PDF Section Processor module.
"""

import os
import sys
import unittest
import tempfile
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.pdf_section_processor import (
    detect_page_type,
    PAGE_TYPE_LETTER,
    PAGE_TYPE_TABLOID,
    PAGE_TYPE_OTHER,
    PDFSection,
    group_pages_into_sections
)

class TestPDFSectionProcessor(unittest.TestCase):
    """Test cases for the PDF Section Processor module."""
    
    def test_detect_page_type_letter(self):
        """Test detecting Letter size pages."""
        # Letter size in points (8.5 x 11 inches)
        self.assertEqual(detect_page_type(612, 792), PAGE_TYPE_LETTER)
        # Letter size with slight variation
        self.assertEqual(detect_page_type(610, 790), PAGE_TYPE_LETTER)
        # Letter size in landscape
        self.assertEqual(detect_page_type(792, 612), PAGE_TYPE_LETTER)
    
    def test_detect_page_type_tabloid(self):
        """Test detecting Tabloid size pages."""
        # Tabloid size in points (11 x 17 inches)
        self.assertEqual(detect_page_type(792, 1224), PAGE_TYPE_TABLOID)
        # Tabloid size with slight variation
        self.assertEqual(detect_page_type(790, 1220), PAGE_TYPE_TABLOID)
        # Tabloid size in landscape
        self.assertEqual(detect_page_type(1224, 792), PAGE_TYPE_TABLOID)
    
    def test_detect_page_type_other(self):
        """Test detecting other size pages."""
        # A4 size in points (8.27 x 11.69 inches)
        self.assertEqual(detect_page_type(595, 842), PAGE_TYPE_OTHER)
        # Legal size in points (8.5 x 14 inches)
        self.assertEqual(detect_page_type(612, 1008), PAGE_TYPE_OTHER)
    
    def test_pdf_section_str(self):
        """Test string representation of PDFSection."""
        # Single page section
        section = PDFSection(PAGE_TYPE_LETTER, 0)
        self.assertEqual(str(section), "Letter (1)")
        
        # Multi-page section
        section = PDFSection(PAGE_TYPE_TABLOID, 2, 5)
        self.assertEqual(str(section), "Tabloid (3-6)")
    
    def test_pdf_section_get_page_count(self):
        """Test getting page count of PDFSection."""
        # Single page section
        section = PDFSection(PAGE_TYPE_LETTER, 0)
        self.assertEqual(section.get_page_count(), 1)
        
        # Multi-page section
        section = PDFSection(PAGE_TYPE_TABLOID, 2, 5)
        self.assertEqual(section.get_page_count(), 4)
    
    def test_group_pages_into_sections_empty(self):
        """Test grouping empty page types list."""
        sections = group_pages_into_sections([])
        self.assertEqual(len(sections), 0)
    
    def test_group_pages_into_sections_single_type(self):
        """Test grouping pages of a single type."""
        page_types = [PAGE_TYPE_LETTER] * 5
        sections = group_pages_into_sections(page_types)
        
        self.assertEqual(len(sections), 1)
        self.assertEqual(sections[0].page_type, PAGE_TYPE_LETTER)
        self.assertEqual(sections[0].start_page, 0)
        self.assertEqual(sections[0].end_page, 4)
    
    def test_group_pages_into_sections_multiple_types(self):
        """Test grouping pages of multiple types."""
        page_types = [
            PAGE_TYPE_LETTER, PAGE_TYPE_LETTER,
            PAGE_TYPE_TABLOID, PAGE_TYPE_TABLOID, PAGE_TYPE_TABLOID,
            PAGE_TYPE_LETTER, PAGE_TYPE_LETTER, PAGE_TYPE_LETTER,
            PAGE_TYPE_OTHER
        ]
        sections = group_pages_into_sections(page_types)
        
        self.assertEqual(len(sections), 4)
        
        self.assertEqual(sections[0].page_type, PAGE_TYPE_LETTER)
        self.assertEqual(sections[0].start_page, 0)
        self.assertEqual(sections[0].end_page, 1)
        
        self.assertEqual(sections[1].page_type, PAGE_TYPE_TABLOID)
        self.assertEqual(sections[1].start_page, 2)
        self.assertEqual(sections[1].end_page, 4)
        
        self.assertEqual(sections[2].page_type, PAGE_TYPE_LETTER)
        self.assertEqual(sections[2].start_page, 5)
        self.assertEqual(sections[2].end_page, 7)
        
        self.assertEqual(sections[3].page_type, PAGE_TYPE_OTHER)
        self.assertEqual(sections[3].start_page, 8)
        self.assertEqual(sections[3].end_page, 8)

if __name__ == '__main__':
    unittest.main()
