<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Classes|TextInterface" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>GetPictogram - e3Text</title>
        <link href="../../../SkinSupport/Slideshow.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <link href="../../../SkinSupport/MadCap.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor495"></a><a name="kanchor496"></a><a name="kanchor497"></a>e3Text.GetPictogram()
        </h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">Integer</span> GetPictogram()</p>
            <h4>Description</h4>
            <p>Gets the text item's flag value determining if the text is displayed in the pictogram language.</p>
            <h4>Parameters</h4>
            <p>No parameters defined.</p>
            <h4>Return Values</h4>
            <table style="width: 100%;border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;border-left-style: solid;border-left-width: 1px;border-left-color: ;border-right-style: solid;border-right-width: 1px;border-right-color: ;border-top-style: solid;border-top-width: 1px;border-top-color: ;border-bottom-style: solid;border-bottom-width: 1px;border-bottom-color: ;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 169px;" class="TableStyle-Rows-Column-Column1" />
                <col style="width: 140px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Status</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">1</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">Success</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Text is displayed in the pictogram language</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">0</td>
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">Inconclusive</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">Text is not displayed in the pictogram language or an error occurred</td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>Due caution is recommended on relying on the return value of 0. 0 means either the text is not displayed in the pictogram language or an error occurred.</p>
            <p>The pictogram language flag value can be modified using <a href="SetPictogram.htm">SetPictogram()</a>.</p>
            <h4>Examples</h4>
            <div class="codeSnippet_0 codeSnippet">
                <div class="codeSnippetCaption_0 codeSnippetCaption">Visual Basic Script</div>
                <div MadCap:useLineNumbers="False" MadCap:lineNumberStart="1" MadCap:continue="False" style="mc-code-lang: VB;" class="codeSnippetBody1 codeSnippetBody">
                    <table style="border-collapse: collapse; border-spacing: 0; border: none;">
                        <tbody>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Application = CreateObject( <span style="color: #df5000; ">"CT.Application"</span> )&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Job = e3Application.CreateJobObject()</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Text = e3Job.CreateTextObject()</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">textCount = e3Job.GetSelectedTextIds( textIds )&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #969896; ">'get selected texts&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">If</span> textCount &gt; 0 <span style="color: #a71d5d; ">Then</span>&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">For</span> textIndex = 1 <span style="color: #a71d5d; ">To</span> textCount&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;textId = e3Text.SetId( textIds( textIndex ) )&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;result = e3Text.GetPictogram()</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">If</span> result = 0 <span style="color: #a71d5d; ">Then</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;message = <span style="color: #df5000; ">"Text item "</span> &amp; textId &amp; <span style="color: #df5000; ">" is not using pictogram"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Else</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;message = <span style="color: #df5000; ">"Text item "</span> &amp; textId &amp; <span style="color: #df5000; ">" is using pictogram"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">End</span>&#160;<span style="color: #a71d5d; ">If</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;e3Application.PutInfo 0, message, textId&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #969896; ">'output result of operation&#160;&#160;</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Next</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Else</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;&#160;&#160;&#160;e3Application.PutWarning 0, <span style="color: #df5000; ">"No text items selected on sheet"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">End</span>&#160;<span style="color: #a71d5d; ">If</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; ">&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Text = <span style="color: #63a35c; ">Nothing</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Job = <span style="color: #63a35c; ">Nothing</span>&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Consolas'; "><span style="color: #a71d5d; ">Set</span> e3Application = <span style="color: #63a35c; ">Nothing</span>&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <h4>Version Information</h4>
            <p>Introduced in v2017-17.70.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="Overview.htm">e3Text - Overview</a>
                </li>
            </ul>
            <ul>
                <li><a href="SetPictogram.htm">SetPictogram()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>