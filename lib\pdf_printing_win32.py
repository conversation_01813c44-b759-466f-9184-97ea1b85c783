"""
PDF Printing Windows API Module

This module provides direct Windows API functions for printing PDF files with specific settings.
"""

import os
import sys
import logging
import tempfile
import time
import subprocess
import ctypes
from ctypes import wintypes
import win32print
import win32api
import win32con
from typing import Dict, Optional, Any

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_printing_engine import PrinterProfile, PAPER_LETTER, PAPER_TABLOID
from lib.pdf_printing_engine import ORIENTATION_PORTRAIT, ORIENTATION_LANDSCAPE
from lib.pdf_printing_engine import DUPLEX_SIMPLEX, DUPLEX_VERTICAL, DUPLEX_HORIZONTAL
from lib.pdf_printing_engine import COLOR_MONOCHROME, COLOR_COLOR

# Windows API constants
DM_OUT_BUFFER = 2
DM_IN_BUFFER = 8

# Device mode structure fields
DM_ORIENTATION = 0x00000001
DM_PAPERSIZE = 0x00000002
DM_PAPERLENGTH = 0x00000004
DM_PAPERWIDTH = 0x00000008
DM_COPIES = 0x00000100
DM_DEFAULTSOURCE = 0x00000200
DM_PRINTQUALITY = 0x00000400
DM_COLOR = 0x00000800
DM_DUPLEX = 0x00001000
DM_COLLATE = 0x00008000

# Paper sizes
DMPAPER_LETTER = 1
DMPAPER_TABLOID = 3

# Orientations
DMORIENT_PORTRAIT = 1
DMORIENT_LANDSCAPE = 2

# Duplex modes
DMDUP_SIMPLEX = 1
DMDUP_VERTICAL = 2
DMDUP_HORIZONTAL = 3

# Color modes
DMCOLOR_MONOCHROME = 1
DMCOLOR_COLOR = 2

class DEVMODE(ctypes.Structure):
    """Windows DEVMODE structure for printer settings."""
    _fields_ = [
        ("dmDeviceName", ctypes.c_wchar * 32),
        ("dmSpecVersion", ctypes.c_ushort),
        ("dmDriverVersion", ctypes.c_ushort),
        ("dmSize", ctypes.c_ushort),
        ("dmDriverExtra", ctypes.c_ushort),
        ("dmFields", ctypes.c_ulong),
        ("dmOrientation", ctypes.c_short),
        ("dmPaperSize", ctypes.c_short),
        ("dmPaperLength", ctypes.c_short),
        ("dmPaperWidth", ctypes.c_short),
        ("dmScale", ctypes.c_short),
        ("dmCopies", ctypes.c_short),
        ("dmDefaultSource", ctypes.c_short),
        ("dmPrintQuality", ctypes.c_short),
        ("dmColor", ctypes.c_short),
        ("dmDuplex", ctypes.c_short),
        ("dmYResolution", ctypes.c_short),
        ("dmTTOption", ctypes.c_short),
        ("dmCollate", ctypes.c_short),
        ("dmFormName", ctypes.c_wchar * 32),
        ("dmLogPixels", ctypes.c_ushort),
        ("dmBitsPerPel", ctypes.c_ulong),
        ("dmPelsWidth", ctypes.c_ulong),
        ("dmPelsHeight", ctypes.c_ulong),
        ("dmDisplayFlags", ctypes.c_ulong),
        ("dmDisplayFrequency", ctypes.c_ulong),
        ("dmICMMethod", ctypes.c_ulong),
        ("dmICMIntent", ctypes.c_ulong),
        ("dmMediaType", ctypes.c_ulong),
        ("dmDitherType", ctypes.c_ulong),
        ("dmReserved1", ctypes.c_ulong),
        ("dmReserved2", ctypes.c_ulong),
        ("dmPanningWidth", ctypes.c_ulong),
        ("dmPanningHeight", ctypes.c_ulong),
    ]

def get_printer_devmode(printer_name: str) -> Optional[DEVMODE]:
    """
    Get the DEVMODE structure for a printer.

    Args:
        printer_name: The name of the printer

    Returns:
        Optional[DEVMODE]: The DEVMODE structure, or None if an error occurred
    """
    try:
        # Open the printer
        h_printer = win32print.OpenPrinter(printer_name)
        if not h_printer:
            logging.error(f"Failed to open printer: {printer_name}")
            return None

        try:
            # Get the default DEVMODE for the printer
            devmode_data = win32print.GetPrinter(h_printer, 8)["pDevMode"]

            # Convert the DEVMODE data to a DEVMODE structure
            devmode = DEVMODE()
            ctypes.memmove(ctypes.byref(devmode), devmode_data, ctypes.sizeof(devmode))

            return devmode
        finally:
            win32print.ClosePrinter(h_printer)
    except Exception as e:
        logging.error(f"Error getting printer DEVMODE: {e}")
        return None

def apply_profile_to_devmode(devmode: DEVMODE, profile: PrinterProfile) -> DEVMODE:
    """
    Apply a printer profile to a DEVMODE structure.

    Args:
        devmode: The DEVMODE structure
        profile: The printer profile

    Returns:
        DEVMODE: The updated DEVMODE structure
    """
    # Set the fields that will be modified
    devmode.dmFields = 0

    # Set paper size
    if profile.paper_size == PAPER_LETTER:
        devmode.dmPaperSize = DMPAPER_LETTER
        devmode.dmFields |= DM_PAPERSIZE
        logging.info(f"Setting paper size to Letter (DMPAPER_LETTER={DMPAPER_LETTER})")
    elif profile.paper_size == PAPER_TABLOID:
        devmode.dmPaperSize = DMPAPER_TABLOID
        devmode.dmFields |= DM_PAPERSIZE
        logging.info(f"Setting paper size to Tabloid (DMPAPER_TABLOID={DMPAPER_TABLOID})")

    # Set orientation
    if profile.orientation == ORIENTATION_PORTRAIT:
        devmode.dmOrientation = DMORIENT_PORTRAIT
        devmode.dmFields |= DM_ORIENTATION
        logging.info(f"Setting orientation to Portrait (DMORIENT_PORTRAIT={DMORIENT_PORTRAIT})")
    elif profile.orientation == ORIENTATION_LANDSCAPE:
        devmode.dmOrientation = DMORIENT_LANDSCAPE
        devmode.dmFields |= DM_ORIENTATION
        logging.info(f"Setting orientation to Landscape (DMORIENT_LANDSCAPE={DMORIENT_LANDSCAPE})")

    # Set duplex mode
    if profile.duplex == DUPLEX_SIMPLEX:
        devmode.dmDuplex = DMDUP_SIMPLEX
        devmode.dmFields |= DM_DUPLEX
        logging.info(f"Setting duplex to Simplex (DMDUP_SIMPLEX={DMDUP_SIMPLEX})")
    elif profile.duplex == DUPLEX_VERTICAL:
        devmode.dmDuplex = DMDUP_VERTICAL
        devmode.dmFields |= DM_DUPLEX
        logging.info(f"Setting duplex to Vertical (DMDUP_VERTICAL={DMDUP_VERTICAL})")
    elif profile.duplex == DUPLEX_HORIZONTAL:
        devmode.dmDuplex = DMDUP_HORIZONTAL
        devmode.dmFields |= DM_DUPLEX
        logging.info(f"Setting duplex to Horizontal (DMDUP_HORIZONTAL={DMDUP_HORIZONTAL})")

    # Set color mode
    if profile.color == COLOR_MONOCHROME:
        devmode.dmColor = DMCOLOR_MONOCHROME
        devmode.dmFields |= DM_COLOR
        logging.info(f"Setting color to Monochrome (DMCOLOR_MONOCHROME={DMCOLOR_MONOCHROME})")
    elif profile.color == COLOR_COLOR:
        devmode.dmColor = DMCOLOR_COLOR
        devmode.dmFields |= DM_COLOR
        logging.info(f"Setting color to Color (DMCOLOR_COLOR={DMCOLOR_COLOR})")

    return devmode

def set_printer_devmode(printer_name: str, devmode: DEVMODE) -> bool:
    """
    Set the DEVMODE structure for a printer.

    Args:
        printer_name: The name of the printer
        devmode: The DEVMODE structure

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Open the printer
        h_printer = win32print.OpenPrinter(printer_name)
        if not h_printer:
            logging.error(f"Failed to open printer: {printer_name}")
            return False

        try:
            # Convert the DEVMODE structure to a buffer
            devmode_size = ctypes.sizeof(devmode)
            devmode_buffer = ctypes.create_string_buffer(devmode_size)
            ctypes.memmove(devmode_buffer, ctypes.byref(devmode), devmode_size)

            # Set the printer DEVMODE
            win32print.DocumentProperties(
                0,  # hwnd
                h_printer,  # hPrinter
                printer_name,  # pDeviceName
                devmode_buffer,  # pDevModeOutput
                devmode_buffer,  # pDevModeInput
                DM_IN_BUFFER | DM_OUT_BUFFER  # fMode
            )

            return True
        finally:
            win32print.ClosePrinter(h_printer)
    except Exception as e:
        logging.error(f"Error setting printer DEVMODE: {e}")
        return False

def print_pdf_with_direct_api(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using direct Windows API calls.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logging.info(f"Printing {pdf_path} with Direct Windows API")
        logging.info(f"Using printer: {profile.printer_name}")
        logging.info(f"Page type settings:")
        logging.info(f"  - Paper size: {profile.paper_size} (1=Letter, 3=Tabloid)")
        logging.info(f"  - Orientation: {profile.orientation} (1=Portrait, 2=Landscape)")
        logging.info(f"  - Duplex: {profile.duplex} (1=Simplex, 2=Duplex)")
        logging.info(f"  - Color: {profile.color} (1=Monochrome, 2=Color)")

        # First, set the printer settings
        try:
            # Determine the settings based on the profile
            if profile.paper_size == PAPER_LETTER:
                paper_size = "LETTER"
            elif profile.paper_size == PAPER_TABLOID:
                paper_size = "TABLOID"
            else:
                paper_size = "LETTER"

            if profile.orientation == ORIENTATION_PORTRAIT:
                orientation = "PORTRAIT"
            elif profile.orientation == ORIENTATION_LANDSCAPE:
                orientation = "LANDSCAPE"
            else:
                orientation = "PORTRAIT"

            if profile.duplex == DUPLEX_SIMPLEX:
                duplex = "NONE"
            elif profile.duplex == DUPLEX_VERTICAL:
                duplex = "VERTICAL"
            else:
                duplex = "NONE"

            # Set the printer as default and configure settings
            logging.info(f"Setting printer settings for {profile.printer_name}")
            subprocess.run(["RUNDLL32", "PRINTUI.DLL,PrintUIEntry", "/y", "/n", profile.printer_name],
                           check=True, capture_output=True)
            subprocess.run(["RUNDLL32", "PRINTUI.DLL,PrintUIEntry", "/Xs", "/n", profile.printer_name,
                           "/a", f"paper={paper_size}", f"orientation={orientation}", f"duplex={duplex}"],
                           check=True, capture_output=True)

            # Wait for settings to apply
            time.sleep(2)
        except Exception as e:
            logging.warning(f"Error setting printer settings: {e}")
            # Continue anyway, as we'll try multiple printing methods

        # Try our new direct printing methods
        from lib.pdf_printing_direct import print_pdf_with_direct_methods
        if print_pdf_with_direct_methods(pdf_path, profile):
            logging.info("Successfully printed with direct methods")
            return True

        # If direct methods failed, try the other methods
        logging.info("Direct methods failed, trying other methods")

        # Try PDFtoPrinter
        try:
            logging.info(f"Trying PDFtoPrinter")
            from lib.pdf_printing_external import print_pdf_with_pdftopdf
            if print_pdf_with_pdftopdf(pdf_path, profile):
                logging.info(f"Successfully printed with PDFtoPrinter")
                return True
        except Exception as e:
            logging.error(f"Error with PDFtoPrinter: {e}")

        # Try SumatraPDF
        try:
            logging.info(f"Trying SumatraPDF")
            from lib.pdf_printing_engine import print_pdf_with_sumatra
            if print_pdf_with_sumatra(pdf_path, profile):
                logging.info(f"Successfully printed with SumatraPDF")
                return True
        except Exception as e:
            logging.error(f"Error with SumatraPDF: {e}")

        # Try Adobe Reader if available
        try:
            logging.info(f"Trying Adobe Reader")
            from lib.pdf_printing_engine import print_pdf_with_adobe
            if print_pdf_with_adobe(pdf_path, profile):
                logging.info(f"Successfully printed with Adobe Reader")
                return True
        except Exception as e:
            logging.error(f"Error with Adobe Reader: {e}")

        # Try VBScript as a last resort
        try:
            logging.info(f"Trying VBScript as a last resort")
            from lib.pdf_printing_vbs import print_with_vbscript
            if print_with_vbscript(pdf_path, profile.printer_name,
                                 "Letter" if profile.paper_size == PAPER_LETTER else
                                 "Tabloid" if profile.paper_size == PAPER_TABLOID else
                                 "Other"):
                logging.info(f"Successfully printed with VBScript")
                return True
        except Exception as e:
            logging.error(f"Error with VBScript: {e}")

        # Try a simple ShellExecute as a very last resort
        try:
            logging.info(f"Trying simple ShellExecute as a very last resort")
            win32api.ShellExecute(
                0,
                "print",
                pdf_path,
                None,  # No parameters
                ".",
                0
            )
            time.sleep(5)
            logging.info(f"ShellExecute command sent, check printer queue")
            return True  # We'll assume it worked since we can't easily check
        except Exception as e:
            logging.error(f"Error with ShellExecute: {e}")

        logging.error(f"All printing methods failed")
        return False
    except Exception as e:
        logging.error(f"Error printing with Direct Windows API: {e}")
        return False
