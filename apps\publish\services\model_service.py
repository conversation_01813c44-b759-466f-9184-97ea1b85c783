"""
Model data management and lookup service.

This module contains the ModelService class for handling model configuration
data and providing model lookup functionality.
"""

from typing import List, Dict, Optional
import logging


class ModelService:
    """Service for model data management and lookup operations."""
    
    def __init__(self, models_config: Dict = None):
        """
        Initialize the model service.
        
        Args:
            models_config: Dictionary containing model configuration data
        """
        self.models = models_config or {}
        self.logger = logging.getLogger(__name__)
        
    def get_models_for_gss(self, gss_number: str) -> List[str]:
        """
        Find models matching the specified GSS parent number.
        
        Args:
            gss_number: GSS parent number to match
            
        Returns:
            List of model names that match the GSS parent number
        """
        self.logger.debug(f"Looking up models for GSS parent: {gss_number}")
        matching_models = []
        
        for category in self.models:
            for model, details in self.models[category].items():
                # Handle both dict format (new) and list/tuple format (legacy)
                controls_parent = None
                if isinstance(details, dict):
                    controls_parent = details.get('controls_parent')
                elif isinstance(details, (list, tuple)) and len(details) > 3:
                    controls_parent = details[3]  # Assuming index 3 is controls_parent
                
                if controls_parent == gss_number:
                    matching_models.append(model)
                    self.logger.debug(f"Found matching model {model} for GSS parent {gss_number}")
        
        return sorted(matching_models)
        
    def get_model_data(self, model: str) -> Optional['ModelData']:
        """
        Get configuration data for a specific model.
        
        Args:
            model: Model name to look up
            
        Returns:
            ModelData object or None if not found
        """
        self.logger.debug(f"Getting data for model: {model}")
        
        for category in self.models:
            if model in self.models[category]:
                details = self.models[category][model]
                
                # Handle both dict format (new) and list/tuple format (legacy)
                if isinstance(details, dict):
                    return ModelData(
                        template_path=details.get('template_path', ''),
                        drawings_path=details.get('drawings_path', ''),
                        asme_flag=details.get('asme_flag', False),
                        controls_parent=details.get('controls_parent', '')
                    )
                elif isinstance(details, (list, tuple)) and len(details) >= 4:
                    return ModelData(
                        template_path=details[0] if len(details) > 0 else '',
                        drawings_path=details[1] if len(details) > 1 else '',
                        asme_flag=details[2] if len(details) > 2 else False,
                        controls_parent=details[3] if len(details) > 3 else ''
                    )
        
        self.logger.warning(f"Model {model} not found in configuration")
        return None


class ModelData:
    """Data structure for model configuration."""
    
    def __init__(self, template_path: str = "", drawings_path: str = "", 
                 asme_flag: bool = False, controls_parent: str = ""):
        """
        Initialize model data.
        
        Args:
            template_path: Path to model template
            drawings_path: Path to model drawings
            asme_flag: Whether ASME standards apply
            controls_parent: Parent controls identifier
        """
        self.template_path = template_path
        self.drawings_path = drawings_path
        self.asme_flag = asme_flag
        self.controls_parent = controls_parent