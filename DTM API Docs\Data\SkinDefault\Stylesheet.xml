<?xml version="1.0" encoding="utf-8"?>
<Stylesheet>
    <Styles>
        <Style Name="Home Button">
            <Properties>
                <Property Name="LabelWithNoneOption">Home</Property>
            </Properties>
        </Style>
        <Style Name="Navigation Element">
            <Classes>
                <StyleClass Name="TOC - Home Page Row">
                    <Properties>
                        <Property Name="LabelWithNoneOption">Table of Contents</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Index - Home Page Row">
                    <Properties>
                        <Property Name="LabelWithNoneOption">Index</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Glossary - Home Page Row">
                    <Properties>
                        <Property Name="LabelWithNoneOption">Glossary</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Browse Sequence - Home Page Row">
                    <Properties>
                        <Property Name="LabelWithNoneOption">Browse Sequences</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="About - Home Page Row">
                    <Properties>
                        <Property Name="LabelWithNoneOption">About</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Index Page">
                    <Properties>
                        <Property Name="SeeReference">See</Property>
                        <Property Name="SeeAlsoReference">See also</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Search Results Page">
                    <Properties>
                        <Property Name="NoResultsFoundString">No results found.</Property>
                        <Property Name="SearchErrorString">There was an error during the search. Please try again.</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="Formats">
            <Properties>
                <Property Name="CodeSnippetCopyButtonText">Copy</Property>
                <Property Name="CrossReferenceFormat">{paratext}</Property>
                <Property Name="CrossReferencePrintFormat">"{paratext}" {pageref}</Property>
                <Property Name="CrossReferenceBelow">below</Property>
                <Property Name="CrossReferenceAbove">above</Property>
                <Property Name="CrossReferenceOnPage">on page&#160;</Property>
                <Property Name="CrossReferenceOnPreviousPage">on the previous page</Property>
                <Property Name="CrossReferenceOnNextPage">on the next page</Property>
                <Property Name="CrossReferenceOnFacingPage">on the facing page</Property>
                <Property Name="BreadcrumbsYouAreHereText">You are here: </Property>
                <Property Name="KeywordLinkText">Search Index</Property>
                <Property Name="KeywordLinkIconAltText">Keyword Link Icon</Property>
                <Property Name="RelatedTopicsText">Related Topics</Property>
                <Property Name="RelatedTopicsIconAltText">Related Topics Link Icon</Property>
                <Property Name="ConceptLinkText">See Also</Property>
                <Property Name="ConceptLinkIconAltText">Concept Link Icon</Property>
                <Property Name="DropDownOpenAltText">Open</Property>
                <Property Name="DropDownClosedAltText">Closed</Property>
                <Property Name="ExpandingOpenAltText">Open</Property>
                <Property Name="ExpandingClosedAltText">Closed</Property>
                <Property Name="GlossaryTermOpenAltText">Open</Property>
                <Property Name="GlossaryTermClosedAltText">Closed</Property>
                <Property Name="TogglerOpenAltText">Open</Property>
                <Property Name="TogglerClosedAltText">Closed</Property>
            </Properties>
        </Style>
        <Style Name="Relationships">
            <Classes>
                <StyleClass Name="concept">
                    <Properties>
                        <Property Name="Label">Concept Information</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="task">
                    <Properties>
                        <Property Name="Label">Related Tasks</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="reference">
                    <Properties>
                        <Property Name="Label">Reference Materials</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="TocEntry">
            <Properties>
                <Property Name="TopicIcon">url('Topic.gif')</Property>
                <Property Name="BookIcon">url('Book.gif')</Property>
                <Property Name="BookOpenIcon">url('BookOpen.gif')</Property>
            </Properties>
        </Style>
        <Style Name="AccordionItem">
            <Properties>
                <Property Name="ItemHeight">28px</Property>
            </Properties>
            <Classes>
                <StyleClass Name="IconTray">
                    <Properties>
                        <Property Name="ItemHeight">28px</Property>
                        <Property Name="BackgroundImage">url('AccordionIconsBackground.jpg')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="TOC">
                    <Properties>
                        <Property Name="Label">TOC</Property>
                        <Property Name="Icon">url('TocIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('TocAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('TocAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Index">
                    <Properties>
                        <Property Name="Label">Index</Property>
                        <Property Name="Icon">url('IndexIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('IndexAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('IndexAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Favorites">
                    <Properties>
                        <Property Name="Label">Favorites</Property>
                        <Property Name="Icon">url('FavoritesIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('FavoritesAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('FavoritesAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Glossary">
                    <Properties>
                        <Property Name="Label">Glossary</Property>
                        <Property Name="Icon">url('GlossaryIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('GlossaryAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('GlossaryAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="BrowseSequence">
                    <Properties>
                        <Property Name="Label">Browse Sequences</Property>
                        <Property Name="Icon">url('BrowsesequencesIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('BrowsesequencesAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('BrowsesequencesAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Search">
                    <Properties>
                        <Property Name="Label">Search</Property>
                        <Property Name="Icon">url('SearchIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('SearchAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('SearchAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Community">
                    <Properties>
                        <Property Name="Label">Community</Property>
                        <Property Name="Icon">url('CommunityIcon.gif')</Property>
                        <Property Name="BackgroundImage">url('CommunityAccordionBackground.jpg')</Property>
                        <Property Name="BackgroundImageHover">url('CommunityAccordionBackground_over.jpg')</Property>
                        <Property Name="ItemHeight">28px</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="ToolbarItem">
            <Classes>
                <StyleClass Name="AccordionTitle">
                    <Properties>
                        <Property Name="ControlType">AccordionTitle</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="QuickSearch">
                    <Properties>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/QuickSearch.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/QuickSearch_selected.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/QuickSearch_over.gif')</Property>
                        <Property Name="ControlType">QuickSearch</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Logo">
                    <Properties>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/LogoIcon.gif')</Property>
                        <Property Name="ControlType">Logo</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="ExpandAll">
                    <Properties>
                        <Property Name="ControlType">ExpandAll</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Expand.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Expand_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Expand_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CollapseAll">
                    <Properties>
                        <Property Name="ControlType">CollapseAll</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Collapse.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Collapse_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Collapse_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="RemoveHighlight">
                    <Properties>
                        <Property Name="ControlType">RemoveHighlight</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Highlight.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Highlight_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Highlight_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Print">
                    <Properties>
                        <Property Name="ControlType">Print</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Print.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Print_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Print_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="ToggleNavigationPane">
                    <Properties>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/HideNavigation.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/HideNavigation_selected.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/HideNavigation_over.gif')</Property>
                        <Property Name="CheckedIcon">url('resources:WebHelp/Default.flwht/Images/HideNavigation_checked.gif')</Property>
                        <Property Name="ControlType">ToggleNavigationPane</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Back">
                    <Properties>
                        <Property Name="ControlType">Back</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Back.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Back_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Back_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Forward">
                    <Properties>
                        <Property Name="ControlType">Forward</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Forward.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Forward_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Forward_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Home">
                    <Properties>
                        <Property Name="ControlType">Home</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Home.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Home_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Home_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Stop">
                    <Properties>
                        <Property Name="ControlType">Stop</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Stop.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Stop_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Stop_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Refresh">
                    <Properties>
                        <Property Name="ControlType">Refresh</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Refresh.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Refresh_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Refresh_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectTOC">
                    <Properties>
                        <Property Name="ControlType">SelectTOC</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectToc.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectToc_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectToc_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectIndex">
                    <Properties>
                        <Property Name="ControlType">SelectIndex</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectIndex.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectIndex_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectIndex_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectSearch">
                    <Properties>
                        <Property Name="ControlType">SelectSearch</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectSearch.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectSearch_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectSearch_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectFavorites">
                    <Properties>
                        <Property Name="ControlType">SelectFavorites</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectFavorites.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectFavorites_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectFavorites_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectGlossary">
                    <Properties>
                        <Property Name="ControlType">SelectGlossary</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectGlossary.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectGlossary_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectGlossary_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectBrowseSequence">
                    <Properties>
                        <Property Name="ControlType">SelectBrowseSequence</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/SelectBrowseSequences.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/SelectBrowseSequences_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/SelectBrowseSequences_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectCommunity">
                    <Properties>
                        <Property Name="ControlType">SelectCommunity</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/Community.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/Community_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/Community_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="AddTopicToFavorites">
                    <Properties>
                        <Property Name="ControlType">AddTopicToFavorites</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/AddTopicToFavorites.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/AddTopicToFavorites_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/AddTopicToFavorites_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="TopicRatings">
                    <Properties>
                        <Property Name="EmptyIcon">url('resources:WebHelp/Default.flwht/Images/Rating0.gif')</Property>
                        <Property Name="FullIcon">url('resources:WebHelp/Default.flwht/Images/RatingGold100.gif')</Property>
                        <Property Name="RatingSubmittedMessage">Thank you for submitting your rating!</Property>
                        <Property Name="ControlType">TopicRatings</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="EditUserProfile">
                    <Properties>
                        <Property Name="ControlType">EditUserProfile</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/EditUserProfile.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/EditUserProfile_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/EditUserProfile_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="PreviousTopic">
                    <Properties>
                        <Property Name="ControlType">PreviousTopic</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/PreviousTopic.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/PreviousTopic_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/PreviousTopic_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="NextTopic">
                    <Properties>
                        <Property Name="ControlType">NextTopic</Property>
                        <Property Name="Icon">url('resources:WebHelp/Default.flwht/Images/NextTopic.gif')</Property>
                        <Property Name="HoverIcon">url('resources:WebHelp/Default.flwht/Images/NextTopic_over.gif')</Property>
                        <Property Name="PressedIcon">url('resources:WebHelp/Default.flwht/Images/NextTopic_selected.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CurrentTopicIndex">
                    <Properties>
                        <Property Name="PaddingLeft">2px</Property>
                        <Property Name="PaddingRight">2px</Property>
                        <Property Name="Label">Page {n} of {total}</Property>
                        <Property Name="ControlType">CurrentTopicIndex</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Separator">
                    <Properties>
                        <Property Name="ControlType">Separator</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectLanguage">
                    <Properties>
                        <Property Name="ControlType">SelectLanguage</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SelectSkin">
                    <Properties>
                        <Property Name="ControlType">SelectSkin</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="Frame">
            <Classes>
                <StyleClass Name="Toolbar">
                    <Properties>
                        <Property Name="BackgroundImage">url('ToolbarBackground.jpg')</Property>
                        <Property Name="Scrolling">no</Property>
                        <Property Name="Height">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="TopicToolbar">
                    <Properties>
                        <Property Name="Height">28px</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="NavigationTopDivider">
                    <Properties>
                        <Property Name="BackgroundImage">url('NavigationTopGradient.jpg')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="NavigationDragHandle">
                    <Properties>
                        <Property Name="BackgroundImage">url('NavigationBottomGradient.jpg')</Property>
                        <Property Name="BackgroundImagePressed">url('NavigationBottomGradient_selected.jpg')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="BodyComments">
                    <Properties>
                        <Property Name="Label">Comments</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="Control">
            <Classes>
                <StyleClass Name="SearchFavoritesDeleteButton">
                    <Properties>
                        <Property Name="Icon">url('Delete.gif')</Property>
                        <Property Name="PressedIcon">url('Delete_selected.gif')</Property>
                        <Property Name="HoverIcon">url('Delete_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="TopicFavoritesDeleteButton">
                    <Properties>
                        <Property Name="Icon">url('Delete.gif')</Property>
                        <Property Name="PressedIcon">url('Delete_selected.gif')</Property>
                        <Property Name="HoverIcon">url('Delete_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="AddSearchToFavoritesButton">
                    <Properties>
                        <Property Name="Icon">url('AddSearchToFavorites.gif')</Property>
                        <Property Name="PressedIcon">url('AddSearchToFavorites_selected.gif')</Property>
                        <Property Name="HoverIcon">url('AddSearchToFavorites_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="SearchResults">
                    <Properties>
                        <Property Name="TableSummary">This table contains the results of the search that was performed. The first column indicates the search rank and the second column indicates the title of the topic that contains the search result.</Property>
                        <Property Name="RankLabel">Rank</Property>
                        <Property Name="TitleLabel">Title</Property>
                        <Property Name="TopicResultsLabel">Topic Results</Property>
                        <Property Name="CommunityResultsLabel">Community Results</Property>
                        <Property Name="ShowAllCommunityResults">Show all community results</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentsAddButton">
                    <Properties>
                        <Property Name="Icon">url('AddComment.gif')</Property>
                        <Property Name="PressedIcon">url('AddComment_selected.gif')</Property>
                        <Property Name="HoverIcon">url('AddComment_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentsReplyButton">
                    <Properties>
                        <Property Name="Icon">url('ReplyComment.gif')</Property>
                        <Property Name="PressedIcon">url('ReplyComment_selected.gif')</Property>
                        <Property Name="HoverIcon">url('ReplyComment_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentsRefreshButton">
                    <Properties>
                        <Property Name="Icon">url('RefreshTopicComments.gif')</Property>
                        <Property Name="PressedIcon">url('RefreshTopicComments_selected.gif')</Property>
                        <Property Name="HoverIcon">url('RefreshTopicComments_over.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentNode">
                    <Properties>
                        <Property Name="Icon">url('Comment.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentReplyNode">
                    <Properties>
                        <Property Name="Icon">url('CommentReply.gif')</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="NavigationLinkTop">
                    <Properties>
                        <Property Name="Label">Open topic with navigation</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="NavigationLinkBottom">
                    <Properties>
                        <Property Name="Label">Open topic with navigation</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="Dialog">
            <Classes>
                <StyleClass Name="AddComment">
                    <Properties>
                        <Property Name="CommentLengthExceeded">The maximum comment length was exceeded by {n} characters.</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="ReplyComment">
                    <Properties>
                        <Property Name="CommentLengthExceeded">The maximum comment length was exceeded by {n} characters.</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
        <Style Name="FeedbackUserProfileItem">
            <Classes>
                <StyleClass Name="Username">
                    <Properties>
                        <Property Name="Required">true</Property>
                        <Property Name="Label">Username</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="EmailAddress">
                    <Properties>
                        <Property Name="Required">true</Property>
                        <Property Name="Label">Email Address</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="FirstName">
                    <Properties>
                        <Property Name="Label">First Name</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="MiddleName">
                    <Properties>
                        <Property Name="Label">Middle Name</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="LastName">
                    <Properties>
                        <Property Name="Label">Last Name</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Address1">
                    <Properties>
                        <Property Name="Label">Address</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Address2">
                    <Properties>
                        <Property Name="Label">Address2</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Address3">
                    <Properties>
                        <Property Name="Label">Address3</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Address4">
                    <Properties>
                        <Property Name="Label">Address4</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="City">
                    <Properties>
                        <Property Name="Label">City</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="State">
                    <Properties>
                        <Property Name="Label">State</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="PostalCode">
                    <Properties>
                        <Property Name="Label">Postal Code</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Country">
                    <Properties>
                        <Property Name="Label">Country</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Gender">
                    <Properties>
                        <Property Name="Label">Gender</Property>
                        <Property Name="GenderFemaleName">Female</Property>
                        <Property Name="GenderMaleName">Male</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Phone1">
                    <Properties>
                        <Property Name="Label">Home Phone</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Phone2">
                    <Properties>
                        <Property Name="Label">Work Phone</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Phone3">
                    <Properties>
                        <Property Name="Label">Mobile Phone</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Fax">
                    <Properties>
                        <Property Name="Label">Fax</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Birthdate">
                    <Properties>
                        <Property Name="Label">Birthdate</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Date">
                    <Properties>
                        <Property Name="Label">Date</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Employer">
                    <Properties>
                        <Property Name="Label">Employer</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Occupation">
                    <Properties>
                        <Property Name="Label">Occupation</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Department">
                    <Properties>
                        <Property Name="Label">Department</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom1">
                    <Properties>
                        <Property Name="Label">Custom1</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom2">
                    <Properties>
                        <Property Name="Label">Custom2</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom3">
                    <Properties>
                        <Property Name="Label">Custom3</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom4">
                    <Properties>
                        <Property Name="Label">Custom4</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom5">
                    <Properties>
                        <Property Name="Label">Custom5</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom6">
                    <Properties>
                        <Property Name="Label">Custom6</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom7">
                    <Properties>
                        <Property Name="Label">Custom7</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom8">
                    <Properties>
                        <Property Name="Label">Custom8</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom9">
                    <Properties>
                        <Property Name="Label">Custom9</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="Custom10">
                    <Properties>
                        <Property Name="Label">Custom10</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="EmailNotificationsGroup">
                    <Properties>
                        <Property Name="Label">Email Notifications</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="EmailNotificationsHeading">
                    <Properties>
                        <Property Name="Label">I want to receive an email when...</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentReplyNotification">
                    <Properties>
                        <Property Name="Label">a reply is left to one of my comments</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentSameTopicNotification">
                    <Properties>
                        <Property Name="Label">a comment is left on a topic that I commented on</Property>
                    </Properties>
                </StyleClass>
                <StyleClass Name="CommentSameHelpSystemNotification">
                    <Properties>
                        <Property Name="Label">a comment is left on any topic in the Help system</Property>
                    </Properties>
                </StyleClass>
            </Classes>
        </Style>
    </Styles>
    <ResourcesInfo>
        <Resource Name="TocIcon.gif" Width="16" Height="16" />
        <Resource Name="TocAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="TocAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="IndexIcon.gif" Width="16" Height="16" />
        <Resource Name="IndexAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="IndexAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="SearchIcon.gif" Width="16" Height="16" />
        <Resource Name="SearchAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="SearchAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="FavoritesIcon.gif" Width="16" Height="16" />
        <Resource Name="FavoritesAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="FavoritesAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="BrowsesequencesIcon.gif" Width="16" Height="16" />
        <Resource Name="BrowsesequencesAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="BrowsesequencesAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="GlossaryIcon.gif" Width="16" Height="16" />
        <Resource Name="GlossaryAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="GlossaryAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="CommunityIcon.gif" Width="16" Height="16" />
        <Resource Name="CommunityAccordionBackground.jpg" Width="2" Height="28" />
        <Resource Name="CommunityAccordionBackground_over.jpg" Width="2" Height="28" />
        <Resource Name="AccordionIconsBackground.jpg" Width="2" Height="28" />
        <Resource Name="Topic.gif" Width="16" Height="16" />
        <Resource Name="Book.gif" Width="16" Height="16" />
        <Resource Name="BookOpen.gif" Width="16" Height="16" />
        <Resource Name="ToolbarBackground.jpg" Width="2" Height="28" />
        <Resource Name="NavigationTopGradient.jpg" Width="2" Height="8" />
        <Resource Name="NavigationBottomGradient.jpg" Width="2" Height="7" />
        <Resource Name="NavigationBottomGradient_selected.jpg" Width="2" Height="7" />
        <Resource Name="Delete.gif" Width="23" Height="22" />
        <Resource Name="Delete_selected.gif" Width="23" Height="22" />
        <Resource Name="Delete_over.gif" Width="23" Height="22" />
        <Resource Name="AddSearchToFavorites.gif" Width="23" Height="22" />
        <Resource Name="AddSearchToFavorites_selected.gif" Width="23" Height="22" />
        <Resource Name="AddSearchToFavorites_over.gif" Width="23" Height="22" />
        <Resource Name="AddComment.gif" Width="23" Height="22" />
        <Resource Name="AddComment_selected.gif" Width="23" Height="22" />
        <Resource Name="AddComment_over.gif" Width="23" Height="22" />
        <Resource Name="ReplyComment.gif" Width="23" Height="22" />
        <Resource Name="ReplyComment_selected.gif" Width="23" Height="22" />
        <Resource Name="ReplyComment_over.gif" Width="23" Height="22" />
        <Resource Name="RefreshTopicComments.gif" Width="23" Height="22" />
        <Resource Name="RefreshTopicComments_selected.gif" Width="23" Height="22" />
        <Resource Name="RefreshTopicComments_over.gif" Width="23" Height="22" />
        <Resource Name="Comment.gif" Width="16" Height="16" />
        <Resource Name="CommentReply.gif" Width="16" Height="16" />
    </ResourcesInfo>
</Stylesheet>