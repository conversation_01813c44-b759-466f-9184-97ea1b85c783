# DXF Emailer Tool

The DXF Emailer Tool is a specialized utility for automatically sending DXF (Drawing Exchange Format) files via email using Microsoft Outlook. This tool provides a simple interface for selecting directories containing DXF files and sending them as email attachments with automated subject lines and body content.

## Features

- **Automatic DXF Detection**: Scans selected directories for all DXF files
- **Microsoft Outlook Integration**: Uses COM interface for seamless email sending
- **Customizable Recipients**: Default recipient with option to modify email addresses
- **Automated Email Content**: Generates appropriate subject lines and body text
- **File Validation**: Checks file accessibility before attempting to attach
- **Modern Interface**: Dark mode GUI using CustomTkinter
- **Error Handling**: Comprehensive error logging and user feedback

## Installation

### Option 1: Run the Python Script

1. Ensure you have Python 3.8 or higher installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the script:
   ```
   python apps/dxf_emailer.py
   ```

### Option 2: Use via Engineering Tools Launcher

1. Run the launcher:
   ```
   python apps_launcher.py
   ```
2. Click "Launch" next to "DXF Emailer" in the tools list

## Usage

1. **Launch the application**
2. **Select Folder**: Click "Select Folder" to choose a directory containing DXF files
3. **Review Files**: The application will automatically detect and list all DXF files found
4. **Verify Recipient**: Check or modify the recipient email address (<NAME_EMAIL>)
5. **Send Email**: Click "Send DXF Files" to create and send the email

### Email Content

The application automatically generates:
- **Subject**: "DXF Files from [folder_name]"
- **Body**: Description including number of files and source directory
- **Attachments**: All DXF files found in the selected directory

## Requirements

- Windows operating system
- Microsoft Outlook installed and configured
- Python 3.8 or higher (for script version)
- Required Python packages:
  - customtkinter
  - pywin32

## Troubleshooting

### Common Issues

- **Outlook not found**: Ensure Microsoft Outlook is installed and properly configured
- **COM interface error**: Make sure Outlook is running or can be started by the application
- **File access denied**: Check that DXF files are not locked by other applications
- **Email send failure**: Verify Outlook account is configured and connected

### Error Messages

If you encounter issues:

1. Check that Microsoft Outlook is installed and configured
2. Ensure the selected directory contains accessible DXF files
3. Verify network connectivity for email sending
4. Check the application log for detailed error messages

### Log Files

Error logs are automatically generated to help with troubleshooting:
- Console output shows real-time status
- Detailed error information is logged for debugging

## Technical Details

### File Detection

The tool searches for files with the following extensions:
- `.dxf` (standard DXF files)
- `.DXF` (uppercase extension)

### Email Integration

- Uses `win32com.client` for Outlook automation
- Creates `olMailItem` objects for email composition
- Automatically attaches files using Outlook's attachment system
- Sends emails through the configured Outlook account

### Security Considerations

- The tool uses the local Outlook installation and account
- No email credentials are stored or transmitted
- Files are attached directly through Outlook's secure interface

## Customization

### Default Recipient

To change the default recipient email address, modify the `email_var` initialization in the source code:

```python
self.email_var.set("<EMAIL>")
```

### Email Template

The email subject and body can be customized by modifying the email creation section in the `send_dxf_files` method.

## Integration

The DXF Emailer integrates seamlessly with:
- **Engineering Tools Launcher**: Available as a one-click tool
- **Project Workflows**: Can be used after DXF generation/export
- **File Management**: Works with any directory structure containing DXF files

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.

## Contact

For support or questions about the DXF Emailer Tool:
Jonathan Callahan: <EMAIL>
