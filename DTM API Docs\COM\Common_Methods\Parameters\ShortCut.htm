<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Parameters" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Short Cut</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor5593"></a><a name="kanchor5594"></a><a name="kanchor5595"></a><a name="kanchor5596"></a><a name="kanchor5597"></a><a name="kanchor5598"></a>Short Cut
		</h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">String </span><i>shortcut</i>
            </p>
            <h4>Description</h4>
            <p>Parameter represents a menu item's shortcut key combination.</p>
            <h4>Possible Values</h4>
            <p>The short cut definition format is "<b>&lt;Modifier Keys&gt;&lt;Key&gt;</b>"</p>
            <p>For example:</p>
            <p style="text-indent: 0.5in;">"W" - for the <b style="font-style: italic;">w</b> key</p>
            <p style="text-indent: 0.5in;">"^G" for <b style="font-style: italic;">Ctrl-G</b> key combination</p>
            <p style="text-indent: 0.5in;">"^!I" for <b style="font-style: italic;">Ctrl-Alt-I</b> key combination</p>
            <p style="text-indent: 0.5in;">"+^Home" for <b style="font-style: italic;">Shift-Ctrl-Home</b> key combination</p>
            <h4>Remarks</h4>
            <p>Combinations of the following modifier key values are possible:</p>
            <table style="width: 100%;border-top-left-radius: 1px;border-top-right-radius: 1px;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;border-bottom-style: solid;border-bottom-width: 1px;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 251px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Modifier Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Empty&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No modifier key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"!"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Alt key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"^"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Control (Ctrl) key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">"+"</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">Shift key</td>
                    </tr>
                </tbody>
            </table>
            <p>&#160;</p>
            <p>The following key values are possible:</p>
            <table style="width: 100%;border-top-left-radius: 1px;border-top-right-radius: 1px;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;border-bottom-style: solid;border-bottom-width: 1px;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 251px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Key Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Empty&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Single Alphabetic Letter&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">A to Z keys</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Single Normal Character&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Non-alphanumeric keys</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Single Numeric Character&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">0 to 9 keys</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"End"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">End key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"ESC"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Escape key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Home"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Home key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Ins"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Insert key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"PgDn"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Page Down key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"PgUp"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Page Up key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"F&lt;1..24&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Function keys
				    	<p>For example: "F2"</p></td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Down"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Down cursor key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Left"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Left cursor key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Right"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Right cursor key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Up"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Up cursor key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Numpad&lt;&lt;0..9&gt;&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Numerical keypad number keys
				   		<p>For example: "Numpad&lt;4&gt;"</p></td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"NumpadAdd"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Add numerical keypad key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"NumpadDiv"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Divide numerical keypad key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"NumpadDot"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Decimal point numerical keypad key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"NumpadEnter"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Enter numerical keypad key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"NumpadMult"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Multiply numerical keypad key</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">"NumpadSub"</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">Subtract numerical keypad key</td>
                    </tr>
                </tbody>
            </table>
            <h4>Version Information</h4>
            <p>Introduced in v2009-8.50.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="../../Classes/e3UserMenuItem/Create.htm">e3UserMenuItem.Create()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3UserMenuItem/GetShortCut.htm">e3UserMenuItem.GetShortCut()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3UserMenuItem/SetShortCut.htm">e3UserMenuItem.SetShortCut()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>