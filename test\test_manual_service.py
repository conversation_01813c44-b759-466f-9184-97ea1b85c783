"""
Unit tests for ManualService.

This module contains tests for the ManualService class functionality
including manual creation operations and model validation.
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os
import tempfile
import shutil

# Add the parent directory to the path to import from apps
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.services.manual_service import (
    ManualService, ManualResult, ValidationResult, ManualError
)


class MockProjectData:
    """Mock project data for testing."""
    
    def __init__(self, gss_parent="TEST123", serial_number="001", model="P408"):
        self.gss_parent = gss_parent
        self.serial_number = serial_number
        self.model = model
        self.customer = "Test Customer"
        self.location = "Test Location"
        self.title = "Test Title"
        self.sales_order = "SO123"


class TestManualService(unittest.TestCase):
    """Test cases for ManualService class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_models_config = {
            "Chiller": {
                "P408": {
                    "template_path": "/path/to/template.dotx",
                    "drawings_path": "/path/to/drawings",
                    "asme_flag": False,
                    "controls_parent": "503390"
                },
                "P410": {
                    "template_path": "/path/to/template2.dotx",
                    "drawings_path": "/path/to/drawings2",
                    "asme_flag": True,
                    "controls_parent": "503390"
                }
            },
            "Pump": {
                "PSP-4": {
                    "template_path": "/path/to/pump_template.dotx",
                    "drawings_path": "/path/to/pump_drawings",
                    "asme_flag": False,
                    "controls_parent": "500432"
                }
            }
        }
        self.service = ManualService(self.mock_models_config)
        self.project_data = MockProjectData()
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_init_with_config(self):
        """Test ManualService initialization with configuration."""
        service = ManualService(self.mock_models_config)
        self.assertEqual(service.models_config, self.mock_models_config)
    
    def test_init_without_config(self):
        """Test ManualService initialization without configuration."""
        service = ManualService()
        self.assertEqual(service.models_config, {})
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', True)
    @patch('apps.publish.services.manual_service.ManualCreator')
    def test_create_manual_success(self, mock_manual_creator_class):
        """Test successful manual creation."""
        # Setup mock
        mock_manual_creator = Mock()
        mock_manual_creator_class.return_value = mock_manual_creator
        
        # Create expected output files
        expected_files = [
            os.path.join(self.temp_dir, "001_manual.docx"),
            os.path.join(self.temp_dir, "001_manual.pdf")
        ]
        for file_path in expected_files:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                f.write("test content")
        
        result = self.service.create_manual(self.project_data, self.temp_dir)
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.created_files), 2)
        mock_manual_creator.create_manual.assert_called_once()
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', False)
    def test_create_manual_not_available(self):
        """Test manual creation when functionality is not available."""
        with self.assertRaises(ManualError) as context:
            self.service.create_manual(self.project_data, self.temp_dir)
        
        self.assertIn("not available", str(context.exception))
    
    def test_create_manual_invalid_project_data(self):
        """Test manual creation with invalid project data."""
        invalid_data = MockProjectData()
        invalid_data.gss_parent = None  # Missing required field
        
        self.service._manual_creator_available_override = True
        with self.assertRaises(ManualError) as context:
            self.service.create_manual(invalid_data, self.temp_dir)
        
        self.assertIn("validation failed", str(context.exception))
    
    def test_create_manual_unknown_model(self):
        """Test manual creation with unknown model."""
        unknown_model_data = MockProjectData(model="UNKNOWN-MODEL")
        
        self.service._manual_creator_available_override = True
        with self.assertRaises(ManualError) as context:
            self.service.create_manual(unknown_model_data, self.temp_dir)
        
        self.assertIn("not configured for manual creation", str(context.exception))
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', True)
    @patch('apps.publish.services.manual_service.ManualCreator')
    def test_create_manual_creator_exception(self, mock_manual_creator_class):
        """Test manual creation when ManualCreator raises exception."""
        mock_manual_creator_class.side_effect = Exception("ManualCreator failed")
        
        with self.assertRaises(ManualError) as context:
            self.service.create_manual(self.project_data, self.temp_dir)
        
        self.assertIn("Failed to create ManualCreator instance", str(context.exception))
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', True)
    @patch('apps.publish.services.manual_service.ManualCreator')
    def test_create_manual_process_exception(self, mock_manual_creator_class):
        """Test manual creation when manual creation process fails."""
        mock_manual_creator = Mock()
        mock_manual_creator.create_manual.side_effect = Exception("Manual creation failed")
        mock_manual_creator_class.return_value = mock_manual_creator
        
        with self.assertRaises(ManualError) as context:
            self.service.create_manual(self.project_data, self.temp_dir)
        
        self.assertIn("Manual creation process failed", str(context.exception))
    
    def test_is_manual_creation_available_true(self):
        """Test manual creation availability check when available."""
        self.service._manual_creator_available_override = True
        result = self.service.is_manual_creation_available()
        self.assertTrue(result)
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', False)
    def test_is_manual_creation_available_false(self):
        """Test manual creation availability check when not available."""
        result = self.service.is_manual_creation_available()
        self.assertFalse(result)
    
    def test_get_supported_models(self):
        """Test getting supported models."""
        supported = self.service.get_supported_models()
        
        expected = {
            "Chiller": ["P408", "P410"],
            "Pump": ["PSP-4"]
        }
        self.assertEqual(supported, expected)
    
    def test_get_supported_models_empty_config(self):
        """Test getting supported models with empty configuration."""
        service = ManualService({})
        supported = service.get_supported_models()
        self.assertEqual(supported, {})
    
    def test_can_create_manual_for_model_true(self):
        """Test checking if manual can be created for existing model."""
        self.service._manual_creator_available_override = True
        result = self.service.can_create_manual_for_model("P408")
        self.assertTrue(result)
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', True)
    def test_can_create_manual_for_model_false(self):
        """Test checking if manual can be created for non-existing model."""
        result = self.service.can_create_manual_for_model("UNKNOWN")
        self.assertFalse(result)
    
    @patch('apps.publish.services.manual_service.MANUAL_CREATOR_AVAILABLE', False)
    def test_can_create_manual_for_model_not_available(self):
        """Test checking if manual can be created when functionality not available."""
        result = self.service.can_create_manual_for_model("P408")
        self.assertFalse(result)
    
    def test_validate_project_data_valid(self):
        """Test project data validation with valid data."""
        result = self.service._validate_project_data(self.project_data)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
        self.assertEqual(result.error_message, "")
    
    def test_validate_project_data_missing_gss_parent(self):
        """Test project data validation with missing GSS parent."""
        invalid_data = MockProjectData()
        invalid_data.gss_parent = None
        
        result = self.service._validate_project_data(invalid_data)
        
        self.assertFalse(result.is_valid)
        self.assertIn("GSS Parent number is required", result.error_message)
    
    def test_validate_project_data_missing_serial_number(self):
        """Test project data validation with missing serial number."""
        invalid_data = MockProjectData()
        invalid_data.serial_number = ""
        
        result = self.service._validate_project_data(invalid_data)
        
        self.assertFalse(result.is_valid)
        self.assertIn("Serial number is required", result.error_message)
    
    def test_validate_project_data_missing_model(self):
        """Test project data validation with missing model."""
        invalid_data = MockProjectData()
        invalid_data.model = None
        
        result = self.service._validate_project_data(invalid_data)
        
        self.assertFalse(result.is_valid)
        self.assertIn("Model is required", result.error_message)
    
    def test_validate_project_data_unknown_model(self):
        """Test project data validation with unknown model."""
        invalid_data = MockProjectData(model="UNKNOWN")
        
        result = self.service._validate_project_data(invalid_data)
        
        self.assertFalse(result.is_valid)
        self.assertIn("not configured for manual creation", result.error_message)
    
    def test_determine_category_found(self):
        """Test determining category for existing model."""
        category = self.service._determine_category("P408")
        self.assertEqual(category, "Chiller")
    
    def test_determine_category_not_found(self):
        """Test determining category for non-existing model."""
        category = self.service._determine_category("UNKNOWN")
        self.assertIsNone(category)
    
    def test_get_model_data_dict_format(self):
        """Test getting model data in dictionary format."""
        model_data = self.service._get_model_data("Chiller", "P408")
        
        expected = (
            "/path/to/template.dotx",
            "/path/to/drawings",
            False
        )
        self.assertEqual(model_data, expected)
    
    def test_get_model_data_legacy_format(self):
        """Test getting model data in legacy tuple format."""
        legacy_config = {
            "Legacy": {
                "OLD-MODEL": [
                    "/legacy/template.dotx",
                    "/legacy/drawings",
                    True,
                    "LEGACY123"
                ]
            }
        }
        service = ManualService(legacy_config)
        
        model_data = service._get_model_data("Legacy", "OLD-MODEL")
        
        expected = (
            "/legacy/template.dotx",
            "/legacy/drawings",
            True
        )
        self.assertEqual(model_data, expected)
    
    def test_get_model_data_not_found(self):
        """Test getting model data for non-existing model."""
        model_data = self.service._get_model_data("Chiller", "UNKNOWN")
        self.assertIsNone(model_data)
    
    def test_get_expected_manual_files(self):
        """Test getting expected manual output files."""
        expected_files = self.service._get_expected_manual_files(self.temp_dir, "001")
        
        expected = [
            os.path.join(self.temp_dir, "001_manual.docx"),
            os.path.join(self.temp_dir, "001_manual.pdf"),
            os.path.join(self.temp_dir, "merged", "001 Manual.pdf")
        ]
        self.assertEqual(expected_files, expected)


class TestManualResult(unittest.TestCase):
    """Test cases for ManualResult class."""
    
    def test_init(self):
        """Test ManualResult initialization."""
        result = ManualResult()
        
        self.assertFalse(result.success)
        self.assertEqual(result.created_files, [])
        self.assertEqual(result.output_folder, "")
        self.assertEqual(result.warnings, [])
        self.assertEqual(result.errors, [])
    
    def test_str_success(self):
        """Test string representation for successful result."""
        result = ManualResult()
        result.success = True
        result.created_files = ["file1.pdf", "file2.docx", "file3.pdf"]
        
        str_repr = str(result)
        self.assertIn("SUCCESS", str_repr)
        self.assertIn("3 files created", str_repr)
    
    def test_str_failure(self):
        """Test string representation for failed result."""
        result = ManualResult()
        result.success = False
        result.created_files = ["file1.pdf"]
        
        str_repr = str(result)
        self.assertIn("FAILED", str_repr)
        self.assertIn("1 files created", str_repr)


class TestValidationResult(unittest.TestCase):
    """Test cases for ValidationResult class."""
    
    def test_init(self):
        """Test ValidationResult initialization."""
        result = ValidationResult()
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.errors, [])
        self.assertEqual(result.error_message, "")


class TestManualError(unittest.TestCase):
    """Test cases for ManualError class."""
    
    def test_init_basic(self):
        """Test ManualError initialization with basic message."""
        error = ManualError("Test error")
        
        self.assertEqual(error.message, "Test error")
        self.assertIsNone(error.operation)
        self.assertIsNone(error.original_error)
        self.assertIsNone(error.details)
    
    def test_init_with_operation(self):
        """Test ManualError initialization with operation."""
        error = ManualError("Test error", "manual_creation")
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "manual_creation")
        self.assertIsNone(error.original_error)
    
    def test_init_with_original_error(self):
        """Test ManualError initialization with original error."""
        original = ValueError("Original error")
        error = ManualError("Test error", "manual_creation", original)
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "manual_creation")
        self.assertEqual(error.original_error, original)
    
    def test_init_with_details(self):
        """Test ManualError initialization with details."""
        details = ["detail1", "detail2"]
        error = ManualError("Test error", "manual_creation", None, details)
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "manual_creation")
        self.assertEqual(error.details, details)
    
    def test_str_basic(self):
        """Test string representation with basic message."""
        error = ManualError("Test error")
        self.assertEqual(str(error), "Test error")
    
    def test_str_with_operation(self):
        """Test string representation with operation."""
        error = ManualError("Test error", "manual_creation")
        expected = "Manual operation 'manual_creation' failed: Test error"
        self.assertEqual(str(error), expected)
    
    def test_str_with_original_error(self):
        """Test string representation with original error."""
        original = ValueError("Original error")
        error = ManualError("Test error", "manual_creation", original)
        expected = "Manual operation 'manual_creation' failed: Test error (Original error: Original error)"
        self.assertEqual(str(error), expected)


if __name__ == '__main__':
    unittest.main()