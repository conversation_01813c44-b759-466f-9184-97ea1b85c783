# Sum Motor FLA Tool

The Sum Motor FLA Tool is an engineering utility that automatically calculates the total Full Load Amperage (FLA) for all motors in a project, identifies the motor with the largest FLA value, identifies the motor with the largest horsepower, and updates the project attributes with this information. This tool helps engineers ensure that electrical systems are properly sized for the total motor load.

## Features

- Automatically identifies all motors in the project (devices with designations starting with "-M")
- Calculates the sum of all motor FLA values for proper electrical system sizing
- Identifies and records the FLA of the motor with the largest FLA value
- Identifies and records the horsepower of the largest motor in the project
- Updates the project attributes with the calculated total FLA, largest motor HP, and largest FLA
- Provides a clean, dark-mode user interface
- Logs all operations for troubleshooting and verification

## Installation

### Option 1: Run the Python Script

1. Ensure you have Python 3.8 or higher installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the script:
   ```
   python apps/sum_motor_fla.py
   ```

### Option 2: Use the Compiled Executable

1. Download the `sum_motor_fla.exe` file from the `dist` folder
2. Copy it to any location on your computer
3. Double-click to run the application

## Usage

1. Launch the application
2. Click the "Process Project" button to analyze the current project
3. The application will:
   - Connect to the engineering application via COM interface
   - Retrieve all devices in the project
   - Identify motors (devices with designations starting with "-M")
   - Calculate the total FLA
   - Identify the motor with the largest FLA
   - Identify the motor with the largest horsepower
   - Update the project attributes with the calculated values
4. Results will be displayed in the application window and logged to a file

The tool is designed to be simple to use with a single-click operation. No additional configuration is required as long as the engineering application is running and a project is open.

## Requirements

- Windows operating system
- The application that provides the COM interface must be installed and running
- For the Python script: Python 3.8 or higher with the required dependencies
- For the executable: No additional requirements

## Troubleshooting

If you encounter issues:

1. Check that the engineering application providing the COM interface is running
2. Ensure you have the necessary permissions to access the project
3. Verify that the project contains devices with designations starting with "-M"
4. Check the log file for detailed error messages:
   - When running the Python script: `apps/sum_motor_fla.log`
   - When running the executable: `%USERPROFILE%/sum_motor_fla.log`

### Common Issues

- **No motors found**: The tool only identifies devices with designations starting with "-M". Check your device naming conventions.
- **COM interface error**: Make sure the engineering application is running and accessible.
- **Permission denied**: Ensure you have the necessary permissions to modify project attributes.

## Building the Executable

To build the executable yourself:

1. Ensure you have PyInstaller installed:
   ```
   pip install pyinstaller
   ```

2. Run the compilation batch file:
   ```
   compile_sum_motor_fla.bat
   ```

3. The executable will be created in the `dist` folder

## Technical Details

### How Motor Detection Works

The tool identifies motors by looking for devices with designations that start with "-M". For each motor found, it extracts:

- The FLA value from the device properties
- The horsepower value from the device properties

### Attribute Updates

The tool updates the following project attributes:

- `FLA`: The sum of all motor FLA values
- `LARGEST_MOTOR`: The horsepower of the largest motor
- `LARGEST_LOAD`: The FLA value of the motor with the largest FLA

These attributes can then be used in other parts of the project, such as in title blocks or for electrical calculations.

### Processing Details

The application processes motors in the following sequence:
1. Connects to the engineering application via COM interface
2. Retrieves all device IDs from the current project
3. Filters devices to identify motors (designations starting with "-M")
4. Extracts FLA and horsepower values from each motor
5. Calculates totals and identifies largest values
6. Updates project attributes with calculated results
7. Displays comprehensive results including motor count and individual largest values

The tool handles both tuple and single device ID formats and includes robust error handling for invalid or missing data values.
