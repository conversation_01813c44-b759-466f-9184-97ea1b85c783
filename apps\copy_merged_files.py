#!/usr/bin/env python3
"""
Manual Files Copy Application

This application provides a GUI to search all subfolders of a selected location,
find files in folders named 'MERGED', and copy them to a 'manual' folder at the selected location.
"""

import os
import sys
import shutil
import logging
import traceback
from datetime import datetime
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions if available
try:
    from utils import ensure_dir_exists, setup_logging
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import ensure_dir_exists, setup_logging
        from lib.theme_utils import apply_theme
    except ImportError:
        # Define basic utility functions if import fails
        def ensure_dir_exists(directory):
            """Ensure a directory exists, creating it if necessary"""
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            return directory

        def setup_logging(log_name):
            """Set up basic logging to file and console"""
            log_dir = ensure_dir_exists(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs'))
            log_file = os.path.join(log_dir, f"{log_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s [%(levelname)s] %(message)s",
                handlers=[
                    logging.FileHandler(log_file),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            return log_file

        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

class StatusLogger:
    """Simple logger that updates a status label in the UI."""

    def __init__(self, status_label):
        self.status_label = status_label

    def info(self, message):
        """Log an info message and update status."""
        logging.info(message)
        self.status_label.configure(text=message, text_color="#2AA876")  # Green
        self.status_label.update()

    def warning(self, message):
        """Log a warning message and update status."""
        logging.warning(message)
        self.status_label.configure(text=message, text_color="#FFA500")  # Orange
        self.status_label.update()

    def error(self, message):
        """Log an error message and update status."""
        logging.error(message)
        self.status_label.configure(text=message, text_color="#FF0000")  # Red
        self.status_label.update()

    def exception(self, e, message="An error occurred"):
        """Log an exception and update status."""
        logging.exception(f"{message}: {str(e)}")
        self.status_label.configure(text=f"{message}: {str(e)}", text_color="#FF0000")  # Red
        self.status_label.update()

class MergedFilesCopyApp(ctk.CTk):
    """Application for finding and copying files from MERGED folders."""

    def __init__(self):
        """Initialize the application."""
        super().__init__()

        # Set up the window
        self.title("MERGED Files Copy Tool")
        self.geometry("800x600")
        self.minsize(600, 400)

        # Initialize variables
        self.current_directory = None
        self.merged_folders = []
        self.copied_files = []

        # Create widgets
        self.create_widgets()

        # Set up status logger
        self.status_logger = StatusLogger(self.status_label)

        # Log application start
        self.status_logger.info("Application started")

    def create_widgets(self):
        """Create the widgets for the application window."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

        # Create directory selection frame
        self.dir_frame = ctk.CTkFrame(self)
        self.dir_frame.grid(row=0, column=0, padx=20, pady=10, sticky="ew")

        self.btn_select = ctk.CTkButton(
            self.dir_frame,
            text="Select Directory",
            command=self.select_directory,
            width=120,
            corner_radius=8
        )
        self.btn_select.pack(side="left", padx=5, pady=10)

        self.lbl_directory = ctk.CTkLabel(
            self.dir_frame,
            text="No directory selected",
            anchor="w",
            wraplength=500  # Allow text to wrap
        )
        self.lbl_directory.pack(side="left", fill="x", expand=True, padx=5)

        # Create main content frame
        self.content_frame = ctk.CTkFrame(self)
        self.content_frame.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_rowconfigure(1, weight=1)

        # Create results label
        self.results_label = ctk.CTkLabel(
            self.content_frame,
            text="Files Found in MERGED Folders",
            font=("Arial", 14, "bold")
        )
        self.results_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        # Create scrollable text area for results
        self.results_text = ctk.CTkTextbox(
            self.content_frame,
            wrap="word",
            font=("Courier New", 12),
            text_color="white"  # Ensure good contrast in dark mode
        )
        self.results_text.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")

        # Create button frame
        self.button_frame = ctk.CTkFrame(self)
        self.button_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")

        self.btn_search = ctk.CTkButton(
            self.button_frame,
            text="Search for MERGED Folders",
            command=self.search_merged_folders,
            width=180,
            corner_radius=8
        )
        self.btn_search.pack(side="left", padx=5, pady=10)

        self.btn_copy = ctk.CTkButton(
            self.button_frame,
            text="Copy Files to 'manual' Folder",
            command=self.copy_files,
            width=180,
            corner_radius=8,
            state="disabled"  # Initially disabled
        )
        self.btn_copy.pack(side="right", padx=5, pady=10)

        # Create status bar
        self.status_label = ctk.CTkLabel(
            self,
            text="Ready",
            anchor="w",
            height=25
        )
        self.status_label.grid(row=3, column=0, padx=20, pady=5, sticky="ew")

    def select_directory(self):
        """Open a directory selection dialog."""
        directory = filedialog.askdirectory()
        if directory:
            self.current_directory = directory
            self.lbl_directory.configure(text=directory)
            self.status_logger.info(f"Selected directory: {directory}")

            # Clear previous results
            self.results_text.delete("1.0", tk.END)
            self.merged_folders = []
            self.copied_files = []
            self.btn_copy.configure(state="disabled")

    def search_merged_folders(self):
        """Search for MERGED folders in the selected directory."""
        if not self.current_directory:
            messagebox.showerror("Error", "Please select a directory first")
            return

        # Clear previous results
        self.results_text.delete("1.0", tk.END)
        self.merged_folders = []
        self.copied_files = []

        self.status_logger.info("Searching for MERGED folders...")

        try:
            # Walk through the directory tree
            for root, dirs, files in os.walk(self.current_directory):
                # Check if this is a MERGED folder
                if os.path.basename(root).upper() == "MERGED":
                    self.merged_folders.append(root)

                    # Find files in this folder
                    for file in files:
                        file_path = os.path.join(root, file)
                        self.copied_files.append(file_path)

                        # Add to results text
                        rel_path = os.path.relpath(file_path, self.current_directory)
                        self.results_text.insert(tk.END, f"{rel_path}\n")

            if not self.merged_folders:
                self.status_logger.warning("No MERGED folders found")
            else:
                self.status_logger.info(f"Found {len(self.merged_folders)} MERGED folders with {len(self.copied_files)} files")
                self.btn_copy.configure(state="normal")

        except Exception as e:
            self.status_logger.exception(e, "Error searching for MERGED folders")

    def copy_files(self):
        """Copy files from MERGED folders to a 'manual' folder."""
        if not self.current_directory or not self.copied_files:
            messagebox.showerror("Error", "No files to copy")
            return

        # Create the manual folder
        manual_folder = os.path.join(self.current_directory, "manual")
        try:
            ensure_dir_exists(manual_folder)
        except Exception as e:
            self.status_logger.exception(e, "Error creating manual folder")
            return

        self.status_logger.info(f"Copying files to {manual_folder}...")

        # Clear results text and prepare for copy results
        self.results_text.delete("1.0", tk.END)

        # Copy files
        copied_count = 0
        errors = 0

        for file_path in self.copied_files:
            try:
                # Get the filename
                filename = os.path.basename(file_path)

                # Create destination path
                dest_path = os.path.join(manual_folder, filename)

                # Handle duplicate filenames by adding a suffix
                if os.path.exists(dest_path):
                    base, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(dest_path):
                        new_filename = f"{base}_{counter}{ext}"
                        dest_path = os.path.join(manual_folder, new_filename)
                        counter += 1

                # Copy the file
                shutil.copy2(file_path, dest_path)
                copied_count += 1

                # Add to results text
                rel_src = os.path.relpath(file_path, self.current_directory)
                rel_dst = os.path.relpath(dest_path, self.current_directory)
                self.results_text.insert(tk.END, f"Copied: {rel_src} -> {rel_dst}\n")

            except Exception as e:
                errors += 1
                logging.error(f"Error copying {file_path}: {str(e)}")
                self.results_text.insert(tk.END, f"ERROR: {file_path} - {str(e)}\n")

        # Update status
        if errors == 0:
            self.status_logger.info(f"Successfully copied {copied_count} files to {manual_folder}")
        else:
            self.status_logger.warning(f"Copied {copied_count} files with {errors} errors")

        # Open the manual folder in explorer if files were copied
        if copied_count > 0:
            if messagebox.askyesno("Copy Complete", f"Successfully copied {copied_count} files to 'manual' folder. Open the folder?"):
                os.startfile(manual_folder)

def main():
    """Main entry point for the application."""
    try:
        # Set up logging
        log_file = setup_logging("copy_merged_files")
        logging.info("Starting MERGED Files Copy Application")

        # Apply the red theme
        apply_theme("red", "dark")

        # Create and run the application
        app = MergedFilesCopyApp()
        app.mainloop()

        logging.info("Application closed")
    except Exception as e:
        logging.error(f"Unhandled exception: {e}")
        logging.debug(traceback.format_exc())
        print(f"An error occurred: {e}")
        print(f"See log file for details: {log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
