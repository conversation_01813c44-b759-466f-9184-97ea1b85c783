"""
Unit tests for publish logging configuration.
"""

import logging
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

from apps.publish.config.logging_config import (
    PublishLoggingConfig,
    setup_publish_logging,
    get_publish_logger,
    log_operation_start,
    log_operation_success,
    log_operation_error
)


class TestPublishLoggingConfig(unittest.TestCase):
    """Test cases for PublishLoggingConfig class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Reset the class state before each test
        PublishLoggingConfig._initialized = False
        PublishLoggingConfig._log_file_path = None
        
    def tearDown(self):
        """Clean up after tests."""
        # Reset logging state
        PublishLoggingConfig._initialized = False
        PublishLoggingConfig._log_file_path = None
        
        # Clear all handlers from root logger
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_setup_logging_success(self, mock_lib_setup):
        """Test successful logging setup."""
        mock_lib_setup.return_value = "/test/path/publish_3.log"
        
        log_path = PublishLoggingConfig.setup_logging()
        
        self.assertEqual(log_path, "/test/path/publish_3.log")
        self.assertTrue(PublishLoggingConfig.is_initialized())
        self.assertEqual(PublishLoggingConfig.get_log_file_path(), "/test/path/publish_3.log")
        
        # Verify lib_setup_logging was called with correct parameters
        mock_lib_setup.assert_called_once_with(
            app_name="publish_3",
            log_level=logging.INFO,
            console_level=logging.WARNING,
            max_file_size=10 * 1024 * 1024,
            backup_count=5
        )
        
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_setup_logging_custom_params(self, mock_lib_setup):
        """Test logging setup with custom parameters."""
        mock_lib_setup.return_value = "/test/path/custom.log"
        
        log_path = PublishLoggingConfig.setup_logging(
            log_level=logging.DEBUG,
            console_level=logging.INFO,
            app_name="custom_app",
            max_file_size=5 * 1024 * 1024,
            backup_count=3
        )
        
        self.assertEqual(log_path, "/test/path/custom.log")
        
        # Verify lib_setup_logging was called with custom parameters
        mock_lib_setup.assert_called_once_with(
            app_name="custom_app",
            log_level=logging.DEBUG,
            console_level=logging.INFO,
            max_file_size=5 * 1024 * 1024,
            backup_count=3
        )
        
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_setup_logging_already_initialized(self, mock_lib_setup):
        """Test that setup_logging doesn't reinitialize if already done."""
        mock_lib_setup.return_value = "/test/path/publish_3.log"
        
        # First call
        log_path1 = PublishLoggingConfig.setup_logging()
        
        # Second call should return same path without calling lib_setup_logging again
        log_path2 = PublishLoggingConfig.setup_logging()
        
        self.assertEqual(log_path1, log_path2)
        mock_lib_setup.assert_called_once()  # Should only be called once
        
    @patch('apps.publish.config.logging_config.get_logger')
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_get_logger(self, mock_lib_setup, mock_get_logger):
        """Test getting a logger."""
        mock_lib_setup.return_value = "/test/path/publish_3.log"
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        logger = PublishLoggingConfig.get_logger("test.module")
        
        self.assertEqual(logger, mock_logger)
        mock_get_logger.assert_called_with("test.module")
        
    @patch('apps.publish.config.logging_config.get_logger')
    @patch('apps.publish.config.logging_config.lib_setup_logging')
    def test_get_logger_auto_initialize(self, mock_lib_setup, mock_get_logger):
        """Test that get_logger auto-initializes logging if not done."""
        mock_lib_setup.return_value = "/test/path/publish_3.log"
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        # Ensure not initialized
        self.assertFalse(PublishLoggingConfig.is_initialized())
        
        logger = PublishLoggingConfig.get_logger("test.module")
        
        # Should have auto-initialized
        self.assertTrue(PublishLoggingConfig.is_initialized())
        mock_lib_setup.assert_called_once()
        
    def test_configure_module_logging(self):
        """Test configuring logging for a specific module."""
        with patch.object(PublishLoggingConfig, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logger = PublishLoggingConfig.configure_module_logging("test.module", logging.DEBUG)
            
            self.assertEqual(logger, mock_logger)
            mock_logger.setLevel.assert_called_once_with(logging.DEBUG)
            
    def test_set_log_level_specific_logger(self):
        """Test setting log level for a specific logger."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            PublishLoggingConfig.set_log_level(logging.DEBUG, "test.logger")
            
            mock_get_logger.assert_called_once_with("test.logger")
            mock_logger.setLevel.assert_called_once_with(logging.DEBUG)
            
    def test_set_log_level_root_logger(self):
        """Test setting log level for root logger."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            PublishLoggingConfig.set_log_level(logging.DEBUG)
            
            mock_get_logger.assert_called_once_with()
            mock_logger.setLevel.assert_called_once_with(logging.DEBUG)


class TestConvenienceFunctions(unittest.TestCase):
    """Test cases for convenience functions."""
    
    def setUp(self):
        """Set up test fixtures."""
        PublishLoggingConfig._initialized = False
        PublishLoggingConfig._log_file_path = None
        
    def tearDown(self):
        """Clean up after tests."""
        PublishLoggingConfig._initialized = False
        PublishLoggingConfig._log_file_path = None
        
    @patch('apps.publish.config.logging_config.PublishLoggingConfig.setup_logging')
    def test_setup_publish_logging_default_config(self, mock_setup):
        """Test setup_publish_logging with default configuration."""
        mock_setup.return_value = "/test/path/publish_3.log"
        
        log_path = setup_publish_logging()
        
        self.assertEqual(log_path, "/test/path/publish_3.log")
        mock_setup.assert_called_once_with(
            log_level=logging.INFO,
            console_level=logging.WARNING,
            app_name="publish_3",
            max_file_size=10 * 1024 * 1024,
            backup_count=5
        )
        
    @patch('apps.publish.config.logging_config.PublishLoggingConfig.setup_logging')
    def test_setup_publish_logging_custom_config(self, mock_setup):
        """Test setup_publish_logging with custom configuration."""
        mock_setup.return_value = "/test/path/custom.log"
        
        config = {
            'log_level': 'DEBUG',
            'console_level': 'INFO',
            'app_name': 'custom_app',
            'max_file_size': 5 * 1024 * 1024,
            'backup_count': 3
        }
        
        log_path = setup_publish_logging(config)
        
        self.assertEqual(log_path, "/test/path/custom.log")
        mock_setup.assert_called_once_with(
            log_level=logging.DEBUG,
            console_level=logging.INFO,
            app_name="custom_app",
            max_file_size=5 * 1024 * 1024,
            backup_count=3
        )
        
    @patch('apps.publish.config.logging_config.PublishLoggingConfig.get_logger')
    def test_get_publish_logger(self, mock_get_logger):
        """Test get_publish_logger convenience function."""
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        logger = get_publish_logger("test.module")
        
        self.assertEqual(logger, mock_logger)
        mock_get_logger.assert_called_once_with("test.module")
        
    def test_log_operation_start(self):
        """Test log_operation_start function."""
        mock_logger = MagicMock()
        
        log_operation_start(mock_logger, "test operation", param1="value1", param2="value2")
        
        mock_logger.info.assert_called_once_with("Starting test operation - param1=value1, param2=value2")
        
    def test_log_operation_start_no_kwargs(self):
        """Test log_operation_start function without kwargs."""
        mock_logger = MagicMock()
        
        log_operation_start(mock_logger, "test operation")
        
        mock_logger.info.assert_called_once_with("Starting test operation")
        
    def test_log_operation_success(self):
        """Test log_operation_success function."""
        mock_logger = MagicMock()
        
        log_operation_success(mock_logger, "test operation", result="success")
        
        mock_logger.info.assert_called_once_with("Completed test operation successfully - result=success")
        
    def test_log_operation_error(self):
        """Test log_operation_error function."""
        mock_logger = MagicMock()
        test_error = ValueError("Test error")
        
        log_operation_error(mock_logger, "test operation", test_error, context="test")
        
        mock_logger.error.assert_called_once_with(
            "Error in test operation - context=test: Test error",
            exc_info=True
        )


if __name__ == '__main__':
    unittest.main()