# Publish Package API Documentation

## Table of Contents

1. [Quick Start](#quick-start)
2. [Core Services](#core-services)
3. [Data Models](#data-models)
4. [Integrations](#integrations)
5. [GUI Components](#gui-components)
6. [Utilities](#utilities)
7. [Configuration](#configuration)
8. [Error Handling](#error-handling)
9. [Testing](#testing)
10. [Advanced Usage](#advanced-usage)

## Quick Start

### Basic Publishing Workflow

```python
from apps.publish.container import ServiceContainer
from apps.publish.models.project_data import ProjectData
from apps.publish.services.publish_service import PublishConfig

# Initialize the service container
container = ServiceContainer()
publish_service = container.get_service(PublishService)

# Create project data
project_data = ProjectData(
    gss_parent="GSS123",
    serial_number="001",
    customer="Example Corporation",
    location="New York",
    title="Control Panel Assembly",
    sales_order="SO-2024-001",
    model="CP-Standard",
    folder_path="/output/projects"
)

# Configure publishing options
config = PublishConfig()
config.output_base_path = "/output/projects"
config.export_formats = ['pdf', 'dxf']
config.create_manual = True
config.update_bom_reports = True

# Publish the project
result = publish_service.publish_project(project_data, config)

if result.success:
    print(f"✅ Project published successfully!")
    print(f"📁 Output: {result.output_path}")
    print(f"📄 Files: {len(result.exported_files)} exported")
else:
    print(f"❌ Publishing failed:")
    for error in result.errors:
        print(f"   • {error}")
```

### Series Publishing

```python
# Publish a series of 10 projects with incremented serial numbers
results = publish_service.publish_series(project_data, config, 10)

successful = [r for r in results if r.success]
failed = [r for r in results if not r.success]

print(f"📊 Series Results: {len(successful)}/{len(results)} successful")

# Process results
for result in successful:
    print(f"✅ {result.serial_number}: {result.output_path}")

for result in failed:
    print(f"❌ {result.serial_number}: {result.errors[0]}")
```

## Core Services

### PublishService

The main orchestrator for publishing operations.

```python
from apps.publish.services.publish_service import PublishService, PublishConfig

class PublishService:
    def publish_project(self, project_data: ProjectData, config: PublishConfig) -> PublishResult:
        """Publish a single project with the given configuration."""
        
    def publish_series(self, base_data: ProjectData, config: PublishConfig, count: int) -> List[PublishResult]:
        """Publish a series of projects with incremented serial numbers."""
```

**Usage Examples:**

```python
# Single project publishing
result = publish_service.publish_project(project_data, config)

# Series publishing with custom configuration
config.stop_series_on_error = True  # Stop on first error
config.fail_on_export_errors = False  # Continue on export failures
results = publish_service.publish_series(base_data, config, 5)

# Check detailed results
for result in results:
    print(f"Project: {result.serial_number}")
    print(f"Success: {result.success}")
    print(f"Steps: {', '.join(result.steps_completed)}")
    print(f"Files: {len(result.exported_files)} exported")
    if result.manual_created:
        print(f"Manual: {len(result.manual_files)} files created")
```

### ModelService

Manages model data and lookup operations.

```python
from apps.publish.services.model_service import ModelService

# Get available models for a GSS parent number
model_service = container.get_service(ModelService)
models = model_service.get_models_for_gss("GSS123")
print(f"Available models: {models}")

# Get detailed model information
model_data = model_service.get_model_data("CP-Standard")
print(f"Template path: {model_data.template_path}")
print(f"ASME compliant: {model_data.asme_flag}")
```

### ExportService

Handles PDF and DXF export operations.

```python
from apps.publish.services.export_service import ExportService

export_service = container.get_service(ExportService)

# Export project in multiple formats
export_result = export_service.export_project(
    project_data, 
    output_folder="/path/to/output",
    formats=['pdf', 'dxf']
)

print(f"Export success: {export_result.success}")
print(f"PDF files: {export_result.pdf_files}")
print(f"DXF files: {export_result.dxf_files}")
```

### ManualService

Manages manual creation processes.

```python
from apps.publish.services.manual_service import ManualService

manual_service = container.get_service(ManualService)

# Check if manual creation is supported for a model
if manual_service.can_create_manual_for_model("CP-Standard"):
    manual_result = manual_service.create_manual(project_data, output_folder)
    
    if manual_result.success:
        print(f"Manual created: {manual_result.created_files}")
    else:
        print(f"Manual creation failed: {manual_result.errors}")
```

## Data Models

### ProjectData

Core project information with built-in validation.

```python
from apps.publish.models.project_data import ProjectData

# Create project data
project = ProjectData(
    gss_parent="GSS123",
    serial_number="001",
    customer="Example Corp",
    location="New York",
    title="Control Panel",
    sales_order="SO-001",
    model="CP-Standard",
    folder_path="/output"
)

# Validate data
validation_result = project.validate()
if not validation_result.is_valid:
    print("Validation errors:")
    for error in validation_result.errors:
        print(f"  • {error}")

if validation_result.has_warnings():
    print("Warnings:")
    for warning in validation_result.warnings:
        print(f"  ⚠ {warning}")

# Convert to/from dictionary
project_dict = project.to_dict()
restored_project = ProjectData.from_dict(project_dict)

# Check if project data is empty
if project.is_empty():
    print("Project data is empty")
```

### TitleBlockData

E3 title block information.

```python
from apps.publish.models.project_data import TitleBlockData

# Create title block data
title_block = TitleBlockData(
    document_number="GSS123",
    model="CP-Standard",
    description="Control Panel Assembly",
    customer="Example Corp",
    location="New York",
    sales_order="SO-001",
    serial_number="001"
)

# Convert to project data
project_data = title_block.to_project_data(
    folder_path="/output",
    model_override="CP-Custom"
)

# Validate title block
if title_block.is_valid():
    print("Title block contains essential information")
```

### PublishConfig

Publishing configuration options.

```python
from apps.publish.services.publish_service import PublishConfig

config = PublishConfig()

# Basic settings
config.output_base_path = "/path/to/output"
config.export_formats = ['pdf', 'dxf']
config.create_manual = True
config.update_bom_reports = True
config.run_wire_core_sync = True
config.bom_reports = ["PHOENIX BOM", "Simplified BOM"]
config.additional_reports = ["Field Cable List", "Field Cable List without Cores"]
config.export_gss_bom = True
config.save_project = True
config.save_job_data = True

# Error handling options
config.fail_on_e3_errors = False
config.fail_on_export_errors = False
config.fail_on_manual_errors = False
config.fail_on_report_errors = False
config.stop_series_on_error = True
```

## Integrations

### E3Client

E3 Series COM API integration with resource management.

```python
from apps.publish.integrations.e3_client import E3Client

# Use with context manager for automatic cleanup
with E3Client() as e3:
    # Check connection
    if e3.is_connected():
        print("Connected to E3 Series")
        
        # Read title block data
        title_block = e3.read_title_block_data()
        if title_block:
            print(f"Document: {title_block.document_number}")
            print(f"Customer: {title_block.customer}")
        
        # Update project attributes
        e3.update_attributes(project_data)
        
        # Export to PDF
        success = e3.export_pdf("/output/project.pdf")
        if success:
            print("PDF exported successfully")
        
        # Export dataplates to DXF
        dxf_files = e3.export_dxf_dataplates(
            "/output/dxf", 
            "GSS123", 
            "001"
        )
        print(f"Exported {len(dxf_files)} DXF files")

# Manual connection management
e3_client = E3Client()
try:
    if e3_client.connect():
        # Perform operations
        process_id = e3_client.get_process_id()
        sheet_ids = e3_client.get_all_sheet_ids()
finally:
    e3_client.disconnect()
```

### ReportGeneratorClient

Integration with report generation tools.

```python
from apps.publish.integrations.report_generator import ReportGeneratorClient

report_client = ReportGeneratorClient()

# Update BOM reports
e3_process_id = e3_client.get_process_id()
bom_results = report_client.update_bom_reports(e3_process_id)

print("BOM Report Results:")
for report_name, success in bom_results.items():
    status = "✅" if success else "❌"
    print(f"  {status} {report_name}")

# Export GSS BOM
gss_bom_path = report_client.export_gss_bom(
    e3_process_id, 
    "GSS123", 
    "001"
)
if gss_bom_path:
    print(f"GSS BOM exported to: {gss_bom_path}")
```

## GUI Components

### MainWindow

Primary application window.

```python
import customtkinter as ctk
from apps.publish.gui.main_window import MainWindow
from apps.publish.container import ServiceContainer

# Create application
root = ctk.CTk()
root.title("E3 Publishing System")
root.geometry("800x600")

# Initialize with dependency injection
container = ServiceContainer()
main_window = MainWindow(root, container)

# Alternative: create window directly
main_window = MainWindow(None, container)
app_window = main_window.create_window(
    title="Custom Title",
    geometry="1000x700"
)

# Run application
root.mainloop()
```

### Custom Widgets

Reusable UI components.

```python
from apps.publish.gui.widgets import ModelDropdown, SeriesControls

# Model dropdown with dynamic updates
model_dropdown = ModelDropdown(
    parent_frame,
    values=["Model A", "Model B", "Model C"],
    width=200
)

# Update models based on GSS parent
models = model_service.get_models_for_gss("GSS123")
model_dropdown.update_models(models)

# Series controls for batch publishing
series_controls = SeriesControls(parent_frame)
series_controls.pack(fill="x", padx=5, pady=5)

# Get series settings
if series_controls.is_series_enabled():
    count = series_controls.get_series_count()
    create_manual = series_controls.get_create_manual()
```

### Dialogs

Error and confirmation dialogs.

```python
from apps.publish.gui.dialogs import ErrorDialog, ConfirmationDialog

# Show error dialog
ErrorDialog(
    parent_window,
    "Publishing Error",
    "Failed to export PDF files:\n• File permission denied\n• Disk space full"
).show()

# Show confirmation dialog
if ConfirmationDialog(
    parent_window,
    "Confirm Series Publishing",
    "This will create 50 projects. Continue?"
).show():
    # User confirmed
    proceed_with_series_publishing()
```

## Utilities

### FileOperations

File and directory management utilities.

```python
from apps.publish.utils.file_operations import FileOperations

file_ops = FileOperations()

# Create project folder
output_folder = file_ops.create_serial_folder(
    base_path="/output",
    gss_parent="GSS123",
    serial_number="001"
)
print(f"Created folder: {output_folder}")

# Save job data as JSON
json_path = file_ops.save_job_data_json(project_data, output_folder)
print(f"Job data saved: {json_path}")

# Validate file paths
if file_ops.validate_output_path("/output/projects"):
    print("Output path is valid")

# Clean up old files
cleaned_count = file_ops.cleanup_old_files("/output", days_old=30)
print(f"Cleaned up {cleaned_count} old files")
```

### SeriesGenerator

Serial number generation and manipulation.

```python
from apps.publish.utils.series_generator import SeriesGenerator, SerialFormat

series_gen = SeriesGenerator()

# Generate a series of serial numbers
series = series_gen.generate_series("001", 5)
print(f"Generated series: {series}")  # ["001", "002", "003", "004", "005"]

# Increment individual serial numbers
next_serial = series_gen.increment_serial("ABC123")
print(f"Next serial: {next_serial}")  # "ABC124"

# Detect serial format
format_type = series_gen.detect_serial_format("ABC123")
print(f"Format: {format_type}")  # SerialFormat.ALPHANUMERIC

# Validate serial format
if series_gen.validate_serial_format("001A"):
    print("Serial format is valid for incrementing")

# Generate range of serials
range_series = series_gen.get_serial_range("001", "005")
print(f"Range: {range_series}")  # ["001", "002", "003", "004", "005"]

# Parse serial components
prefix, numeric, suffix = series_gen.parse_serial_components("ABC123XYZ")
print(f"Prefix: {prefix}, Numeric: {numeric}, Suffix: {suffix}")
```

## Configuration

### ConfigurationManager

Centralized configuration management.

```python
from apps.publish.config.configuration_manager import ConfigurationManager

config_manager = ConfigurationManager()

# Load configurations
app_config = config_manager.get_app_config()
models_config = config_manager.get_models_config()

# Access configuration values
output_path = app_config.get('output_base_path', '/default/output')
timeout = app_config.get('timeout_seconds', 300)
export_formats = app_config.get('export_formats', ['pdf'])

# Reload configuration
config_manager.reload_configuration()

# Validate configuration
validation_result = config_manager.validate_configuration()
if not validation_result.is_valid:
    print("Configuration errors:")
    for error in validation_result.errors:
        print(f"  • {error}")
```

### Logging Configuration

Centralized logging setup.

```python
from apps.publish.config.logging_config import (
    setup_publish_logging,
    get_publish_logger,
    log_operation_start,
    log_operation_success,
    log_operation_error
)

# Setup logging
setup_publish_logging(
    log_level="INFO",
    log_file="/logs/publish.log",
    enable_console=True
)

# Get logger for module
logger = get_publish_logger(__name__)

# Use structured logging helpers
log_operation_start("project_publishing", {"gss_parent": "GSS123"})

try:
    # Perform operation
    result = publish_service.publish_project(project_data, config)
    log_operation_success("project_publishing", {"serial": result.serial_number})
except Exception as e:
    log_operation_error("project_publishing", e, {"gss_parent": "GSS123"})
```

## Error Handling

### Exception Hierarchy

```python
from apps.publish.exceptions import (
    PublishError,
    ValidationError,
    E3ConnectionError,
    ExportError,
    ConfigurationError,
    FileOperationError,
    ServiceError,
    IntegrationError,
    GUIError
)

# Catch specific exceptions
try:
    result = publish_service.publish_project(project_data, config)
except ValidationError as e:
    print(f"Validation failed: {e.message}")
    print(f"Field: {e.field}, Value: {e.value}")
except E3ConnectionError as e:
    print(f"E3 connection failed: {e.message}")
    print(f"Operation: {e.operation}")
except ExportError as e:
    print(f"Export failed: {e.message}")
    print(f"Type: {e.export_type}, Path: {e.output_path}")
except PublishError as e:
    print(f"Publishing error: {e.message}")
    print(f"Context: {e.context}")
    if e.cause:
        print(f"Caused by: {e.cause}")

# Add context to exceptions
try:
    # Some operation
    pass
except Exception as e:
    raise PublishError("Operation failed").add_context("project", "GSS123")
```

### Error Recovery

```python
from apps.publish.error_handler import ErrorHandler

error_handler = ErrorHandler()

# Register error recovery strategies
error_handler.register_recovery_strategy(
    E3ConnectionError,
    lambda error: reconnect_to_e3()
)

# Handle errors with recovery
try:
    result = publish_service.publish_project(project_data, config)
except PublishError as e:
    recovery_result = error_handler.handle_error(e)
    if recovery_result.recovered:
        # Retry operation
        result = publish_service.publish_project(project_data, config)
    else:
        # Handle unrecoverable error
        show_error_dialog(e.message)
```

## Testing

### Unit Testing

```python
import pytest
from unittest.mock import Mock, patch
from apps.publish.services.publish_service import PublishService
from apps.publish.models.project_data import ProjectData

class TestPublishService:
    def setup_method(self):
        # Setup mocks
        self.mock_e3_client = Mock()
        self.mock_export_service = Mock()
        
        # Create service with mocked dependencies
        self.publish_service = PublishService(
            e3_client=self.mock_e3_client,
            export_service=self.mock_export_service
        )
    
    def test_publish_project_success(self):
        # Setup test data
        project_data = ProjectData(
            gss_parent="GSS123",
            serial_number="001",
            customer="Test Corp"
        )
        config = PublishConfig()
        
        # Configure mocks
        self.mock_export_service.export_project.return_value = Mock(success=True)
        
        # Execute test
        result = self.publish_service.publish_project(project_data, config)
        
        # Verify results
        assert result.success
        assert result.serial_number == "001"
        self.mock_e3_client.update_attributes.assert_called_once()
    
    def test_publish_project_validation_error(self):
        # Test with invalid data
        project_data = ProjectData()  # Empty data
        config = PublishConfig()
        
        # Should raise validation error
        with pytest.raises(PublishError):
            self.publish_service.publish_project(project_data, config)
```

### Integration Testing

```python
import pytest
from apps.publish.container import ServiceContainer
from apps.publish.models.project_data import ProjectData

class TestPublishingWorkflow:
    def setup_method(self):
        self.container = ServiceContainer()
        self.publish_service = self.container.get_service(PublishService)
    
    @pytest.mark.integration
    def test_complete_publishing_workflow(self):
        # Create test project data
        project_data = ProjectData(
            gss_parent="TEST123",
            serial_number="001",
            customer="Integration Test Corp",
            model="Test-Model"
        )
        
        # Configure for testing
        config = PublishConfig()
        config.output_base_path = "/tmp/test_output"
        config.export_formats = ['pdf']
        config.create_manual = False  # Skip manual creation in tests
        
        # Execute workflow
        result = self.publish_service.publish_project(project_data, config)
        
        # Verify results
        assert result.success
        assert result.output_path.startswith("/tmp/test_output")
        assert len(result.exported_files) > 0
```

## Advanced Usage

### Custom Service Implementation

```python
from apps.publish.services.publish_service import PublishService
from apps.publish.models.project_data import ProjectData

class CustomPublishService(PublishService):
    """Custom publish service with additional functionality."""
    
    def publish_with_approval(self, project_data: ProjectData, config: PublishConfig) -> PublishResult:
        """Publish project with approval workflow."""
        
        # Custom validation
        if not self._validate_for_approval(project_data):
            raise ValidationError("Project not ready for approval workflow")
        
        # Send for approval
        approval_result = self._request_approval(project_data)
        if not approval_result.approved:
            raise PublishError(f"Project not approved: {approval_result.reason}")
        
        # Standard publishing with approval context
        config.approval_id = approval_result.approval_id
        return super().publish_project(project_data, config)
    
    def _validate_for_approval(self, project_data: ProjectData) -> bool:
        """Custom validation for approval workflow."""
        # Implement custom validation logic
        return True
    
    def _request_approval(self, project_data: ProjectData):
        """Request approval for project."""
        # Implement approval request logic
        pass
```

### Plugin System

```python
from apps.publish.plugins import PluginManager, PublishPlugin

class CustomExportPlugin(PublishPlugin):
    """Custom export plugin for additional formats."""
    
    def get_name(self) -> str:
        return "custom_export"
    
    def get_supported_formats(self) -> List[str]:
        return ["dwg", "step"]
    
    def export(self, project_data: ProjectData, output_path: str, format: str) -> bool:
        """Export project in custom format."""
        if format == "dwg":
            return self._export_dwg(project_data, output_path)
        elif format == "step":
            return self._export_step(project_data, output_path)
        return False

# Register plugin
plugin_manager = PluginManager()
plugin_manager.register_plugin(CustomExportPlugin())

# Use plugin in service
export_service = container.get_service(ExportService)
export_service.set_plugin_manager(plugin_manager)
```

### Batch Processing

```python
from apps.publish.batch import BatchProcessor
from apps.publish.models.project_data import ProjectData

# Create batch processor
batch_processor = BatchProcessor(container)

# Add projects to batch
projects = [
    ProjectData(gss_parent="GSS123", serial_number="001"),
    ProjectData(gss_parent="GSS124", serial_number="001"),
    ProjectData(gss_parent="GSS125", serial_number="001"),
]

for project in projects:
    batch_processor.add_project(project)

# Configure batch processing
batch_config = BatchConfig()
batch_config.parallel_workers = 4
batch_config.continue_on_error = True
batch_config.progress_callback = lambda progress: print(f"Progress: {progress}%")

# Execute batch
batch_results = batch_processor.execute_batch(batch_config)

# Process results
successful = [r for r in batch_results if r.success]
failed = [r for r in batch_results if not r.success]

print(f"Batch completed: {len(successful)}/{len(batch_results)} successful")
```

This comprehensive API documentation provides detailed examples and usage patterns for all major components of the publish package. It serves as both a reference guide and a tutorial for developers working with the system.