# Comprehensive Error Handling System

This document describes the comprehensive error handling system implemented for the publish application. The system provides consistent error handling, logging, and user-friendly error messages throughout the application.

## Overview

The error handling system consists of several components:

1. **Custom Exception Hierarchy** - Application-specific exceptions with context information
2. **Error Handler** - Centralized error handling and logging
3. **GUI Error Handler** - Specialized error handling for GUI operations
4. **Error Middleware** - Middleware for integrating error handling into GUI components
5. **Error Recovery** - Utilities for retrying operations and safe cleanup
6. **Decorators** - Easy-to-use decorators for adding error handling to functions

## Custom Exception Hierarchy

### Base Exception: PublishError

All application-specific exceptions inherit from `PublishError`, which provides:

- Context information storage
- Cause tracking (underlying exceptions)
- Method chaining for adding context

```python
from apps.publish.exceptions import PublishError

# Basic usage
error = PublishError("Operation failed")

# With context
error = PublishError("Operation failed")
error.add_context("operation", "publish_project")
error.add_context("project_id", "12345")

# With cause
try:
    # Some operation
    pass
except Exception as e:
    raise PublishError("Higher level operation failed", cause=e)
```

### Specific Exception Types

- **ValidationError** - Data validation failures
- **E3ConnectionError** - E3 Series connection issues
- **ExportError** - PDF/DXF export failures
- **ConfigurationError** - Configuration loading/validation issues
- **FileOperationError** - File system operation failures
- **ServiceError** - Business logic service failures
- **IntegrationError** - External system integration failures
- **GUIError** - User interface operation failures

```python
from apps.publish.exceptions import ValidationError, E3ConnectionError

# Validation error with field information
error = ValidationError("Invalid value", field="username", value="")

# E3 connection error with operation context
error = E3ConnectionError("Connection failed", operation="GetApplication")
```

## Error Handler

The `ErrorHandler` class provides centralized error handling with logging and user-friendly messages.

```python
from apps.publish.error_handler import ErrorHandler
import logging

# Initialize
logger = logging.getLogger(__name__)
error_handler = ErrorHandler(logger)

# Handle an error
try:
    # Some operation that might fail
    pass
except Exception as e:
    user_message = error_handler.handle_error(e)
    print(f"User message: {user_message}")
```

### Function Wrapping

You can wrap functions with error handling:

```python
def risky_operation():
    raise ValidationError("Something went wrong")

# Wrap with error handling
safe_operation = error_handler.wrap_with_error_handling(
    risky_operation,
    error_callback=lambda msg: print(f"Error: {msg}")
)

result = safe_operation()  # Returns None on error, calls callback
```

## GUI Error Handler

The `GUIErrorHandler` extends the base error handler with GUI-specific functionality.

```python
from apps.publish.error_handler import GUIErrorHandler

def show_error_dialog(title: str, message: str):
    # Your GUI dialog implementation
    print(f"Dialog - {title}: {message}")

gui_handler = GUIErrorHandler(logger, show_error_dialog)

# Handle GUI errors
try:
    # GUI operation
    pass
except Exception as e:
    gui_handler.handle_gui_error(e, "Operation Error")
```

## Error Middleware

The `ErrorMiddleware` class provides centralized error handling that can be integrated into GUI components.

```python
from apps.publish.error_handler import ErrorMiddleware

# Initialize middleware
middleware = ErrorMiddleware(logger)
middleware.set_gui_error_handler(gui_handler)

# Handle operation errors
try:
    # Some operation
    pass
except Exception as e:
    middleware.handle_operation_error(e, "Publish Operation", show_dialog=True)

# Wrap GUI methods
def gui_method():
    raise ValidationError("Invalid input")

wrapped_method = middleware.wrap_gui_method(gui_method, "Input Validation")
wrapped_method()  # Handles error automatically
```

## Decorators

### Error Handler Decorator

```python
from apps.publish.error_handler import error_handler_decorator

@error_handler_decorator()
def my_function():
    raise ValidationError("Something went wrong")

# With custom error callback
@error_handler_decorator(lambda msg: print(f"Custom: {msg}"))
def my_other_function():
    raise ValidationError("Something else went wrong")
```

### GUI Error Handler Decorator

```python
from apps.publish.error_handler import gui_error_handler

@gui_error_handler("Operation Error")
def gui_operation():
    raise ValidationError("GUI operation failed")
```

## Error Recovery

The `ErrorRecovery` class provides utilities for retrying operations and safe cleanup.

### Retry Operations

```python
from apps.publish.error_handler import ErrorRecovery

recovery = ErrorRecovery(logger)

def flaky_operation():
    # Operation that might fail
    pass

# Retry with exponential backoff
result = recovery.retry_operation(
    flaky_operation,
    max_retries=3,
    delay=1.0,
    backoff_factor=2.0
)
```

### Safe Cleanup

```python
def cleanup1():
    # Cleanup operation 1
    pass

def cleanup2():
    # Cleanup operation 2 (might fail)
    raise Exception("Cleanup failed")

def cleanup3():
    # Cleanup operation 3
    pass

# Execute all cleanup operations, continuing even if some fail
recovery.safe_cleanup([cleanup1, cleanup2, cleanup3])
```

## Integration with GUI Components

### Main Window Integration

```python
from apps.publish.error_handler import ErrorMiddleware, GUIErrorHandler

class MainWindow:
    def __init__(self):
        self.setup_error_handling()
        
    def setup_error_handling(self):
        # Set up error handling
        self.gui_error_handler = GUIErrorHandler(
            logger=logging.getLogger(__name__),
            show_error_dialog=self.show_error_dialog
        )
        
        self.error_middleware = ErrorMiddleware()
        self.error_middleware.set_gui_error_handler(self.gui_error_handler)
    
    def show_error_dialog(self, title: str, message: str):
        # Your dialog implementation
        from .dialogs import ErrorDialog
        dialog = ErrorDialog(self, title, message)
        dialog.show()
    
    @gui_error_handler("Publish Operation")
    def on_publish_clicked(self):
        # This method is automatically wrapped with error handling
        # Any exceptions will be caught and displayed to the user
        self.publish_service.publish_project(self.get_project_data())
```

### Service Layer Integration

```python
from apps.publish.exceptions import ServiceError, ValidationError

class PublishService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def publish_project(self, project_data):
        try:
            # Validate data
            if not project_data.is_valid():
                raise ValidationError("Invalid project data", 
                                    field="project_data")
            
            # Perform publishing
            self._do_publish(project_data)
            
        except Exception as e:
            # Wrap in service error with context
            raise ServiceError("Failed to publish project", 
                             service="PublishService", 
                             operation="publish_project") from e
```

## Best Practices

1. **Use Specific Exception Types** - Use the most specific exception type available
2. **Add Context Information** - Always add relevant context to exceptions
3. **Preserve Original Exceptions** - Use the `cause` parameter to preserve underlying exceptions
4. **Log at Appropriate Levels** - Use WARNING for validation errors, ERROR for system errors
5. **Provide User-Friendly Messages** - The error handler automatically provides user-friendly messages
6. **Use Decorators for Simple Cases** - Use decorators for simple error handling scenarios
7. **Use Middleware for Complex GUI Integration** - Use middleware for comprehensive GUI error handling

## Testing

The error handling system includes comprehensive unit tests. Run them with:

```bash
python -m pytest test/test_exceptions.py test/test_error_handler.py -v
```

## Demo

A complete demo of the error handling system is available:

```bash
python -m apps.publish.error_handling_demo
```

This demo shows all the features of the error handling system in action.