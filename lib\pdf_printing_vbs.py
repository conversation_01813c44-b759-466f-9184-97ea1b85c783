"""
PDF Printing with VBScript Module

This module provides functions for printing PDF files using VBScript with specific settings.
"""

import os
import sys
import logging
import subprocess
import time
import tempfile
from typing import Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER

def create_vbs_print_script(pdf_path: str, printer_name: str, section_type: str) -> Optional[str]:
    """
    Create a VBScript file to print a PDF with specific settings.

    Args:
        pdf_path: The path to the PDF file
        printer_name: The name of the printer
        section_type: The type of section (Letter, Tabloid, Other)

    Returns:
        Optional[str]: The path to the VBScript file, or None if creation failed
    """
    try:
        # Create a temporary file
        fd, vbs_path = tempfile.mkstemp(suffix='.vbs')
        os.close(fd)

        # Determine the settings based on the section type
        if section_type == PAGE_TYPE_LETTER:
            # Letter, portrait, duplex, color
            paper_size = 1  # DMPAPER_LETTER
            orientation = 1  # DMORIENT_PORTRAIT
            duplex = 2  # DMDUP_VERTICAL (long edge)
            color = 2  # DMCOLOR_COLOR
        elif section_type == PAGE_TYPE_TABLOID:
            # Tabloid, landscape, simplex, color
            paper_size = 3  # DMPAPER_TABLOID
            orientation = 2  # DMORIENT_LANDSCAPE
            duplex = 1  # DMDUP_SIMPLEX
            color = 2  # DMCOLOR_COLOR
        else:
            # Default to Letter, portrait, simplex, color
            paper_size = 1  # DMPAPER_LETTER
            orientation = 1  # DMORIENT_PORTRAIT
            duplex = 1  # DMDUP_SIMPLEX
            color = 2  # DMCOLOR_COLOR

        # Create the log file path
        log_file_path = os.path.join(tempfile.gettempdir(), 'pdf_print.log')

        # Write the VBScript content using single quotes to avoid escaping issues
        vbs_content = f'''
' VBScript to print PDF with specific settings
Option Explicit

' Log file for debugging
Const ForAppending = 8
Dim logFile
Set logFile = CreateObject("Scripting.FileSystemObject").OpenTextFile("{log_file_path}", ForAppending, True)

Sub LogMessage(message)
    logFile.WriteLine(Now & " - " & message)
End Sub

LogMessage "Starting PDF print script"
LogMessage "PDF Path: {pdf_path}"
LogMessage "Printer: {printer_name}"
LogMessage "Section Type: {section_type}"
LogMessage "Paper Size: {paper_size}"
LogMessage "Orientation: {orientation}"
LogMessage "Duplex: {duplex}"
LogMessage "Color: {color}"

' Create Shell object
Dim WshShell
Set WshShell = CreateObject("WScript.Shell")
LogMessage "Created WScript.Shell object"

' Set default printer
WshShell.Run "RUNDLL32 PRINTUI.DLL,PrintUIEntry /y /n """ & "{printer_name}" & """", 0, True
LogMessage "Set default printer to {printer_name}"

' Set printer settings
Dim cmd
cmd = "RUNDLL32 PRINTUI.DLL,PrintUIEntry /Xs /n """ & "{printer_name}" & """ /a ""paper={paper_size}"" ""orientation={orientation}"" ""duplex={duplex}"" ""color={color}"""
LogMessage "Running command: " & cmd
WshShell.Run cmd, 0, True
LogMessage "Set printer settings"

' Try multiple printing methods
LogMessage "Trying multiple printing methods"

' Method 1: Use ShellExecute with rundll32
Dim objShell
Set objShell = CreateObject("Shell.Application")
LogMessage "Created Shell.Application object"

LogMessage "Method 1: Using ShellExecute with rundll32"
objShell.ShellExecute "rundll32.exe", "shell32.dll,ShellExec_RunDLL """ & "{pdf_path}" & """ /p", "", "open", 0
LogMessage "Sent print command with Method 1"

' Wait for printing to complete
WScript.Sleep 3000
LogMessage "Waited 3 seconds for Method 1"

' Method 2: Use WshShell.Run with print command
LogMessage "Method 2: Using WshShell.Run with print command"
WshShell.Run "print """ & "{pdf_path}" & """", 0, True
LogMessage "Sent print command with Method 2"

' Wait for printing to complete
WScript.Sleep 3000
LogMessage "Waited 3 seconds for Method 2"

' Method 3: Try using the default application's print method
LogMessage "Method 3: Using default application's print method"
objShell.ShellExecute "{pdf_path}", "/p", "", "print", 0
LogMessage "Sent print command with Method 3"

' Wait for printing to complete
WScript.Sleep 3000
LogMessage "Waited 3 seconds for Method 3"

' Clean up
Set objShell = Nothing
Set WshShell = Nothing
LogMessage "Cleaned up objects"
LogMessage "Print script completed"
logFile.Close
'''

        with open(vbs_path, 'w') as f:
            f.write(vbs_content)

        return vbs_path
    except Exception as e:
        logging.error(f"Error creating VBScript file: {e}")
        return None

def print_with_vbscript(pdf_path: str, printer_name: str, section_type: str) -> bool:
    """
    Print a PDF file using VBScript with specific settings.

    Args:
        pdf_path: The path to the PDF file
        printer_name: The name of the printer
        section_type: The type of section (Letter, Tabloid, Other)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logging.info(f"Printing {pdf_path} with VBScript for {section_type}")
        logging.info(f"Using printer: {printer_name}")

        # Create a VBScript file
        vbs_path = create_vbs_print_script(pdf_path, printer_name, section_type)
        if not vbs_path:
            logging.error("Failed to create VBScript file")
            return False

        try:
            # Execute the VBScript
            logging.info(f"Executing VBScript: {vbs_path}")
            result = subprocess.run(["cscript.exe", "//NoLogo", vbs_path], check=True, capture_output=True, text=True)

            # Log the output
            if result.stdout:
                logging.info(f"VBScript output: {result.stdout}")
            if result.stderr:
                logging.warning(f"VBScript error: {result.stderr}")

            # Wait for the print job to be processed
            time.sleep(5)

            return True
        finally:
            # Clean up the VBScript file
            try:
                os.remove(vbs_path)
            except:
                pass
    except Exception as e:
        logging.error(f"Error printing with VBScript: {e}")
        return False

def print_section_with_vbscript(section: PDFSection, printer_name: str) -> bool:
    """
    Print a PDF section using VBScript with appropriate settings.

    Args:
        section: The PDF section to print
        printer_name: The name of the printer

    Returns:
        bool: True if successful, False otherwise
    """
    if not section.temp_file_path or not os.path.exists(section.temp_file_path):
        logging.error(f"Temporary file for section {section} not found")
        return False

    # Print with VBScript
    return print_with_vbscript(section.temp_file_path, printer_name, section.page_type)
