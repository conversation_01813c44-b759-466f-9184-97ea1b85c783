<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Common Methods|Parameters" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>Attribute Definition Properties</title>
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor1968"></a><a name="kanchor1969"></a><a name="kanchor1970"></a><a name="kanchor1971"></a><a name="kanchor1972"></a><a name="kanchor1973"></a>Attribute Definition Properties
		</h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">2D Array</span> <i>properties</i></p>
            <h4>Description</h4>
            <p>Parameter represents an array of attribute definition properties and their values.</p>
            <h4>Possible Values</h4>
            <table style="width: 100%;border-top-left-radius: 1px;border-top-right-radius: 1px;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;border-bottom-style: solid;border-bottom-width: 1px;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 288px;" class="TableStyle-Rows-Column-Column1" />
                <col style="width: 204px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Property Name</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Property Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Block connector</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"2"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Block device</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"3"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Block pin</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"4"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Bundle</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"5"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Cable</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"6"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Cable core</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"7"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Cable core end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"8"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Cable end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"9"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Cable type</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"10"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Cable type end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"11"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Component</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"12"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Component pin</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"13"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Connector</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"14"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Connector pin</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"15"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Core type</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"16"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Core type end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"17"
					</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Database symbol</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"18"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Device</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"19"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Device pin</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"20"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Dimension</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"21"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Field symbol</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"22"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Functional port</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"23"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Functional unit</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"24"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Graphic</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"25"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Group</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"26"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Hose/tube</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"27"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Hose/tube end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"28"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Hose/tube inside</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"29"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Hose/tube inside end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"30"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Hose/tube inside type</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"31"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Hose/tube inside type end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"32"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Hose/tube type</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"33"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Hose/tube type end</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"34"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Model</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"35"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Module</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"36"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Net</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"37"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Net node</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"38"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Net segment</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"39"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Project</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"40"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Sheet</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"41"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Sheet (database)</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"42"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Signal</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"43"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Signal class</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"44"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Signal node</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"45"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Symbol</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"46"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Text</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Owner"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"47"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Variant/options</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Type"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Integer</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Type"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"2"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Real</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Type"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"3"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Linear measure</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Type"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"4"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">String</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Type"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"5"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Boolean ( yes/no )</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Single instance"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Single instance"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Not unique</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Object</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"2"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Project</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"3"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Assignment</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"4"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Location</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Unique value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"5"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Assignment and location</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Must exist"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Must exist"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Changeable by script only"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Changeable by script only"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Default value"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Free text including an empty string</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"List of values"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Attribute list name or an empty string</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Changeable when owner is locked"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Changeable when owner is locked"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Allow change of lock behavior"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Allow change of lock behavior"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Format"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Free text including an empty string</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Colour"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Color&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">See <a href="Color.htm">Colors</a> for a list of values</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Size"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Positive real value</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Pos x"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Real value</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Pos y"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Real value</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Ratio"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Normal</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Ratio"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"2"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Narrow</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Ratio"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"3"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Wide</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Direction"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"1"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Left aligned</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Direction"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"2"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Center aligned</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Direction"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"3"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Right aligned</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"Level"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">"&lt;Value&gt;"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Integer between 1 and 256</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"Visibility"</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">"0"</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">"Visibility"</td>
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">"1"</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">Yes</td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>The owner property may appear more than once with different property values. All other properties should appear once.</p>
            <p>Property names are case-insensitive.</p>
            <h4>Version Information</h4>
            <p>Introduced in v2017-18.00.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="../../Classes/e3AttributeDefinition/Create.htm">e3AttributeDefinition.Create()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3AttributeDefinition/Get.htm">e3AttributeDefinition.Get()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3AttributeDefinition/GetFromDatabase.htm">e3AttributeDefinition.GetFromDatabase()</a>
                </li>
            </ul>
            <ul>
                <li><a href="../../Classes/e3AttributeDefinition/Set.htm">e3AttributeDefinition.Set()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>