"""
Centralized logging configuration for the publish application.

This module provides logging setup specifically for the publish application,
integrating with the existing lib.logging_config module while providing
publish-specific configuration.
"""

import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

from lib.logging_config import setup_logging as lib_setup_logging, get_logger


class PublishLoggingConfig:
    """
    Manages logging configuration for the publish application.
    
    This class provides centralized logging setup and configuration
    management for all publish application modules.
    """
    
    _initialized = False
    _log_file_path: Optional[str] = None
    
    @classmethod
    def setup_logging(
        cls,
        log_level: int = logging.INFO,
        console_level: int = logging.WARNING,
        app_name: str = "publish_3",
        max_file_size: int = 10 * 1024 * 1024,  # 10 MB
        backup_count: int = 5
    ) -> str:
        """
        Set up centralized logging for the publish application.
        
        Args:
            log_level: Logging level for file logging
            console_level: Logging level for console output
            app_name: Application name for log file
            max_file_size: Maximum size of log file before rotation
            backup_count: Number of backup log files to keep
            
        Returns:
            str: Path to the log file
        """
        if cls._initialized:
            return cls._log_file_path
            
        # Use the existing lib logging setup
        cls._log_file_path = lib_setup_logging(
            app_name=app_name,
            log_level=log_level,
            console_level=console_level,
            max_file_size=max_file_size,
            backup_count=backup_count
        )
        
        cls._initialized = True
        
        # Log initialization message
        logger = cls.get_logger(__name__)
        logger.info("Publish application logging initialized")
        logger.debug(f"Log file: {cls._log_file_path}")
        
        return cls._log_file_path
        
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        Get a logger with the specified name.
        
        Args:
            name: Name for the logger (typically __name__)
            
        Returns:
            logging.Logger: Configured logger
        """
        # Ensure logging is initialized
        if not cls._initialized:
            cls.setup_logging()
            
        return get_logger(name)
        
    @classmethod
    def configure_module_logging(cls, module_name: str, level: Optional[int] = None) -> logging.Logger:
        """
        Configure logging for a specific module.
        
        Args:
            module_name: Name of the module
            level: Optional specific log level for this module
            
        Returns:
            logging.Logger: Configured logger for the module
        """
        logger = cls.get_logger(module_name)
        
        if level is not None:
            logger.setLevel(level)
            
        return logger
        
    @classmethod
    def set_log_level(cls, level: int, logger_name: Optional[str] = None) -> None:
        """
        Set log level for a specific logger or root logger.
        
        Args:
            level: New log level
            logger_name: Optional specific logger name. If None, sets root logger level
        """
        if logger_name:
            logger = logging.getLogger(logger_name)
            logger.setLevel(level)
        else:
            logging.getLogger().setLevel(level)
            
    @classmethod
    def get_log_file_path(cls) -> Optional[str]:
        """
        Get the current log file path.
        
        Returns:
            Optional[str]: Path to log file or None if not initialized
        """
        return cls._log_file_path
        
    @classmethod
    def is_initialized(cls) -> bool:
        """
        Check if logging has been initialized.
        
        Returns:
            bool: True if logging is initialized
        """
        return cls._initialized


def setup_publish_logging(config: Optional[Dict[str, Any]] = None) -> str:
    """
    Convenience function to set up publish logging with optional configuration.
    
    Args:
        config: Optional configuration dictionary with logging settings
        
    Returns:
        str: Path to the log file
    """
    if config is None:
        config = {}
        
    # Extract logging configuration with defaults
    log_level_name = config.get('log_level', 'INFO').upper()
    console_level_name = config.get('console_level', 'WARNING').upper()
    
    # Convert level names to integers
    log_level = getattr(logging, log_level_name, logging.INFO)
    console_level = getattr(logging, console_level_name, logging.WARNING)
    
    app_name = config.get('app_name', 'publish_3')
    max_file_size = config.get('max_file_size', 10 * 1024 * 1024)
    backup_count = config.get('backup_count', 5)
    
    return PublishLoggingConfig.setup_logging(
        log_level=log_level,
        console_level=console_level,
        app_name=app_name,
        max_file_size=max_file_size,
        backup_count=backup_count
    )


def get_publish_logger(name: str) -> logging.Logger:
    """
    Convenience function to get a logger for publish modules.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        logging.Logger: Configured logger
    """
    return PublishLoggingConfig.get_logger(name)


# Module-level convenience functions for common logging patterns
def log_operation_start(logger: logging.Logger, operation: str, **kwargs) -> None:
    """
    Log the start of an operation with context.
    
    Args:
        logger: Logger instance
        operation: Description of the operation
        **kwargs: Additional context to log
    """
    context = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    if context:
        logger.info(f"Starting {operation} - {context}")
    else:
        logger.info(f"Starting {operation}")


def log_operation_success(logger: logging.Logger, operation: str, **kwargs) -> None:
    """
    Log successful completion of an operation.
    
    Args:
        logger: Logger instance
        operation: Description of the operation
        **kwargs: Additional context to log
    """
    context = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    if context:
        logger.info(f"Completed {operation} successfully - {context}")
    else:
        logger.info(f"Completed {operation} successfully")


def log_operation_error(logger: logging.Logger, operation: str, error: Exception, **kwargs) -> None:
    """
    Log an error during an operation.
    
    Args:
        logger: Logger instance
        operation: Description of the operation
        error: Exception that occurred
        **kwargs: Additional context to log
    """
    context = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    if context:
        logger.error(f"Error in {operation} - {context}: {error}", exc_info=True)
    else:
        logger.error(f"Error in {operation}: {error}", exc_info=True)