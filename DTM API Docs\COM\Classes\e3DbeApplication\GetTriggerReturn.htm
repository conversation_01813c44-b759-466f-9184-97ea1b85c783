<?xml version="1.0" encoding="Windows-1252"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="HtmlHelp" MadCap:tocPath="Classes|DbeApplicationInterface" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="com.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=Windows-1252" /><title>GetTriggerReturn - e3DbeApplication</title>
        <link href="../../../SkinSupport/Slideshow.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <link href="../../../SkinSupport/MadCap.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <link href="../../../default.css" rel="stylesheet" type="text/css" />
        <link href="../../../Resources/TableStyles/Rows.css" rel="stylesheet" MadCap:stylesheetType="table" />
        <script language="JavaScript" src="../../../Resources/HelpDesign.js">
        </script>
        <script src="../../../SkinSupport/jquery-1.12.4.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapGlobal.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapSlideshow.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapMerging.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapAliasFile.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapUtilities.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapBody.js" type="text/javascript">
        </script>
        <script src="../../../SkinSupport/MadCapHighlighter.js" type="text/javascript">
        </script>
    </head>
    <body>
        <div style="width: 100%;position: relative;">
            <p style="margin-top: 0pt;margin-bottom: 0pt;text-align: right;" align="right">
                <img src="../../../Resources/Images/zuken_logo.gif" style="border: none;width: 961px;height: 94px;float: none;border-style: none;border-style: none;" width="961" height="94" border="0" />
            </p>
            <p style="text-align: right;"><b><span style="font-weight: bold;font-family: Verdana;" class="mc-variable Primary.Version variable">v2023-24.30</span></b>
            </p>
        </div>
        <p>&#160;</p>
        <div role="main" id="mc-main-content">
            <h3><a name="kanchor1782"></a><a name="kanchor1783"></a><a name="kanchor1784"></a>e3DbeApplication.GetTriggerReturn()
        </h3>
            <h4>Syntax</h4>
            <p class="Syntax"><span style="color: #0000ff;">Integer</span> GetTriggerReturn()</p>
            <h4>Description</h4>
            <p>Gets the current trigger script return value.</p>
            <h4>Parameters</h4>
            <p>No parameters defined.</p>
            <h4>Return Values</h4>
            <table style="width: 100%;border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;border-left-style: solid;border-left-width: 1px;border-left-color: ;border-right-style: solid;border-right-width: 1px;border-right-color: ;border-top-style: solid;border-top-width: 1px;border-top-color: ;border-bottom-style: solid;border-bottom-width: 1px;border-bottom-color: ;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 169px;" class="TableStyle-Rows-Column-Column1" />
                <col style="width: 141px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Status</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">0</td>
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">Success</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">No trigger is active</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">1..7</td>
                        <td class="TableStyle-Rows-BodyB-Column1-Body1">Success</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body1">Trigger script return value</td>
                    </tr>
                </tbody>
            </table>
            <h4>Remarks</h4>
            <p>The trigger script return can be one of the following values:</p>
            <table style="width: 100%;border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;border-left-style: solid;border-left-width: 1px;border-left-color: ;border-right-style: solid;border-right-width: 1px;border-right-color: ;border-top-style: solid;border-top-width: 1px;border-top-color: ;border-bottom-style: solid;border-bottom-width: 1px;border-bottom-color: ;mc-table-style: url('../../../Resources/TableStyles/Rows.css');" class="TableStyle-Rows" cellspacing="3">
                <col style="width: 170px;" class="TableStyle-Rows-Column-Column1" />
                <col class="TableStyle-Rows-Column-Column1" />
                <tbody>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyE-Column1-Body1">Value</td>
                        <td style="font-weight: bold;" class="TableStyle-Rows-BodyD-Column1-Body1">Description</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">1</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Ok</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">2</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Cancel</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">3</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Abort</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">4</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Retry</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyE-Column1-Body2">5</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body2">Ignore</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body1">
                        <td class="TableStyle-Rows-BodyE-Column1-Body1">6</td>
                        <td class="TableStyle-Rows-BodyD-Column1-Body1">Yes</td>
                    </tr>
                    <tr class="TableStyle-Rows-Body-Body2">
                        <td class="TableStyle-Rows-BodyB-Column1-Body2">7</td>
                        <td class="TableStyle-Rows-BodyA-Column1-Body2">No</td>
                    </tr>
                </tbody>
            </table>
            <p>The function returns the default value of the individual trigger or the value set by <a href="SetTriggerReturn.htm">SetTriggerReturn()</a>.</p>
            <h4>Examples</h4>
            <p>The best results from the example can be achieved by creating a trigger script with the example code and activating a trigger to call it.</p>
            <div class="codeSnippet_0 codeSnippet">
                <div class="codeSnippetCaption_0 codeSnippetCaption">Visual Basic Script</div>
                <div MadCap:useLineNumbers="False" MadCap:lineNumberStart="1" MadCap:continue="False" style="font-family: 'Courier New';mc-code-lang: VB;" class="codeSnippetBody_0 codeSnippetBody">
                    <table style="border-collapse: collapse; border-spacing: 0; border: none;">
                        <tbody>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; "><span style="color: #a71d5d; ">Set</span> e3DbeApplication = CreateObject( <span style="color: #df5000; ">"CT.DbeApplication"</span> )&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">e3DbeApplication.SetTriggerReturn 1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #969896; ">'set the return value to ok</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">result = e3DbeApplication.GetTriggerReturn()</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; "><span style="color: #a71d5d; ">If</span> result = 0 <span style="color: #a71d5d; ">Then</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;message = <span style="color: #df5000; ">"Trigger is inactive"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; "><span style="color: #a71d5d; ">Else</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Select</span>&#160;<span style="color: #a71d5d; ">Case</span> result&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 1</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"ok"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 2</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"cancel"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 3</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"abort"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 4</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"retry"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 5</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"ignore"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 6</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"yes"</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">Case</span> 7</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;value = <span style="color: #df5000; ">"no"</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;<span style="color: #a71d5d; ">End</span>&#160;<span style="color: #a71d5d; ">Select</span>&#160;&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;message = <span style="color: #df5000; ">"Trigger return value is "</span> &amp; value</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; "><span style="color: #a71d5d; ">End</span>&#160;<span style="color: #a71d5d; ">If</span>&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">e3DbeApplication.PutInfo 0, message&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style="color: #969896; ">'output result of operation</span></pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; ">&#160;&#160;&#160;&#160;</pre>
                                </td>
                            </tr>
                            <tr>
                                <td MadCap:isCodeCell="True" style="border: none; padding-left: 1em;"><pre style="margin: 0; font-family: 'Courier New'; "><span style="color: #a71d5d; ">Set</span> e3DbeApplication = <span style="color: #63a35c; ">Nothing</span>&#160;</pre>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <h4>Version Information</h4>
            <p>Introduced in v2011-10.00.</p>
            <hr width="100%" size="0" align="center" />
            <h4>See Also</h4>
            <ul>
                <li><a href="Overview.htm">e3DbeApplication - Overview</a>
                </li>
            </ul>
            <ul>
                <li><a href="SetTriggerReturn.htm">SetTriggerReturn()</a>
                </li>
            </ul>
            <hr width="100%" size="0" align="center" />
        </div>
        <p>&#160;</p>
        <p style="text-align: right;"><span style="font-family: Verdana; font-size: 10pt;"><span style="FONT-FAMILY: Verdana;">More questions? Please contact your local support office or Zuken Global Support (ZGS) if support calls can be logged by the user </span>(<a href="https://support.zuken.com/global/" style="font-weight: bold;" target="_blank">https://support.zuken.com/global/</a>)</span><span style="font-family: Verdana; font-size: 10pt;">.</span>
        </p>
        <script type="text/javascript" src="../../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>