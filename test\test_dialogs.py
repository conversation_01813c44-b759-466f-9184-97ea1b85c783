"""
Unit tests for dialog components.

This module contains tests to verify the dialog components work correctly
and meet the requirements for error display and confirmation dialogs.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestDialogComponents(unittest.TestCase):
    """Test cases for dialog components."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock parent window
        self.mock_parent = Mock()
        self.mock_parent.winfo_x.return_value = 100
        self.mock_parent.winfo_y.return_value = 100
        self.mock_parent.winfo_width.return_value = 800
        self.mock_parent.winfo_height.return_value = 600
        
    def test_imports(self):
        """Test that all dialog classes can be imported."""
        try:
            from apps.publish.gui.dialogs import (
                BaseDialog, ErrorDialog, ConfirmationDialog, 
                ProgressDialog, ValidationErrorDialog, ResultsDialog
            )
            
            # Verify classes exist
            self.assertTrue(callable(BaseDialog))
            self.assertTrue(callable(ErrorDialog))
            self.assertTrue(callable(ConfirmationDialog))
            self.assertTrue(callable(ProgressDialog))
            self.assertTrue(callable(ValidationErrorDialog))
            self.assertTrue(callable(ResultsDialog))
            
        except ImportError as e:
            if "customtkinter" in str(e).lower():
                self.skipTest("CustomTkinter not available")
            else:
                raise
                
    def test_base_dialog_initialization(self):
        """Test BaseDialog initialization."""
        try:
            from apps.publish.gui.dialogs import BaseDialog
            
            dialog = BaseDialog(self.mock_parent, "Test Title", 400, 300)
            
            self.assertEqual(dialog.parent, self.mock_parent)
            self.assertEqual(dialog.title, "Test Title")
            self.assertEqual(dialog.width, 400)
            self.assertEqual(dialog.height, 300)
            self.assertIsNone(dialog.result)
            self.assertIsNone(dialog.dialog)
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_error_dialog_initialization(self):
        """Test ErrorDialog initialization and interface."""
        try:
            from apps.publish.gui.dialogs import ErrorDialog
            
            error_dialog = ErrorDialog(
                self.mock_parent, 
                "Test Error", 
                "This is a test error message"
            )
            
            self.assertEqual(error_dialog.title, "Test Error")
            self.assertEqual(error_dialog.message, "This is a test error message")
            self.assertTrue(hasattr(error_dialog, 'show'))
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_confirmation_dialog_initialization(self):
        """Test ConfirmationDialog initialization and interface."""
        try:
            from apps.publish.gui.dialogs import ConfirmationDialog
            
            confirm_dialog = ConfirmationDialog(
                self.mock_parent,
                "Test Confirmation",
                "Do you want to continue?"
            )
            
            self.assertEqual(confirm_dialog.title, "Test Confirmation")
            self.assertEqual(confirm_dialog.message, "Do you want to continue?")
            self.assertTrue(hasattr(confirm_dialog, 'show'))
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_validation_error_dialog_initialization(self):
        """Test ValidationErrorDialog initialization."""
        try:
            from apps.publish.gui.dialogs import ValidationErrorDialog
            
            errors = ["Field is required", "Invalid format"]
            warnings = ["This might cause issues"]
            
            validation_dialog = ValidationErrorDialog(
                self.mock_parent,
                errors,
                warnings
            )
            
            self.assertEqual(validation_dialog.errors, errors)
            self.assertEqual(validation_dialog.warnings, warnings)
            self.assertEqual(validation_dialog.title, "Validation Errors")
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_progress_dialog_initialization(self):
        """Test ProgressDialog initialization."""
        try:
            from apps.publish.gui.dialogs import ProgressDialog
            
            progress_dialog = ProgressDialog(
                self.mock_parent,
                "Processing",
                "Please wait..."
            )
            
            self.assertEqual(progress_dialog.title, "Processing")
            self.assertEqual(progress_dialog.message, "Please wait...")
            self.assertFalse(progress_dialog.cancelled)
            self.assertTrue(hasattr(progress_dialog, 'update_progress'))
            self.assertTrue(hasattr(progress_dialog, 'is_cancelled'))
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_results_dialog_initialization(self):
        """Test ResultsDialog initialization."""
        try:
            from apps.publish.gui.dialogs import ResultsDialog
            
            results = [
                {'success': True, 'serial_number': '001'},
                {'success': False, 'serial_number': '002', 'errors': ['Failed to export']}
            ]
            
            results_dialog = ResultsDialog(
                self.mock_parent,
                "Operation Results",
                results
            )
            
            self.assertEqual(results_dialog.title, "Operation Results")
            self.assertEqual(results_dialog.results, results)
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    @patch('tkinter.messagebox.showerror')
    def test_error_dialog_simple_message(self, mock_showerror):
        """Test ErrorDialog with simple message uses system dialog."""
        try:
            from apps.publish.gui.dialogs import ErrorDialog
            
            error_dialog = ErrorDialog(
                self.mock_parent,
                "Simple Error",
                "Short message"
            )
            
            error_dialog.show()
            
            # Should use system messagebox for simple messages
            mock_showerror.assert_called_once_with(
                "Simple Error",
                "Short message",
                parent=self.mock_parent
            )
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    @patch('tkinter.messagebox.askyesno')
    def test_confirmation_dialog_simple_message(self, mock_askyesno):
        """Test ConfirmationDialog with simple message uses system dialog."""
        try:
            from apps.publish.gui.dialogs import ConfirmationDialog
            
            mock_askyesno.return_value = True
            
            confirm_dialog = ConfirmationDialog(
                self.mock_parent,
                "Simple Confirm",
                "Are you sure?"
            )
            
            result = confirm_dialog.show()
            
            # Should use system messagebox for simple messages
            mock_askyesno.assert_called_once_with(
                "Simple Confirm",
                "Are you sure?",
                parent=self.mock_parent
            )
            self.assertTrue(result)
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_consistent_interface(self):
        """Test that dialogs provide consistent interface (Requirement 3.4)."""
        try:
            from apps.publish.gui.dialogs import ErrorDialog, ConfirmationDialog
            
            # Both dialogs should have show() method
            error_dialog = ErrorDialog(self.mock_parent, "Error", "Message")
            confirm_dialog = ConfirmationDialog(self.mock_parent, "Confirm", "Message")
            
            self.assertTrue(hasattr(error_dialog, 'show'))
            self.assertTrue(hasattr(confirm_dialog, 'show'))
            
            # Both should inherit from BaseDialog
            from apps.publish.gui.dialogs import BaseDialog
            self.assertIsInstance(error_dialog, BaseDialog)
            self.assertIsInstance(confirm_dialog, BaseDialog)
            
        except ImportError:
            self.skipTest("CustomTkinter not available")
            
    def test_error_handling_requirement(self):
        """Test that dialogs support error display (Requirement 2.1)."""
        try:
            from apps.publish.gui.dialogs import ErrorDialog, ValidationErrorDialog
            
            # ErrorDialog should handle various error types
            simple_error = ErrorDialog(self.mock_parent, "Error", "Simple error")
            self.assertTrue(hasattr(simple_error, 'show'))
            
            # ValidationErrorDialog should handle validation errors specifically
            validation_error = ValidationErrorDialog(
                self.mock_parent,
                ["Error 1", "Error 2"],
                ["Warning 1"]
            )
            self.assertTrue(hasattr(validation_error, 'show'))
            self.assertIn("Error 1", validation_error.message)
            self.assertIn("Warning 1", validation_error.message)
            
        except ImportError:
            self.skipTest("CustomTkinter not available")


if __name__ == '__main__':
    unittest.main()