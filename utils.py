import os
import sys
import json
import logging
import appdirs

def get_app_dir():
    """Get the application directory, works for both development and PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # We're running in a normal Python environment
        # utils.py is in the root directory, so just get its directory
        base_path = os.path.dirname(os.path.abspath(__file__))
    return base_path

def get_user_data_dir():
    """Get the user data directory for storing configuration files"""
    return appdirs.user_data_dir("EngineeringTools", "Phoenix")

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    app_dir = get_app_dir()
    return os.path.join(app_dir, relative_path)

def get_config_path(filename):
    """Get path to a configuration file, checking user directory first, then app directory"""
    # First check in user data directory
    user_config = os.path.join(get_user_data_dir(), filename)
    if os.path.exists(user_config):
        return user_config
    
    # Then check in app directory
    return get_resource_path(os.path.join('resources', 'config', filename))

def ensure_dir_exists(directory):
    """Ensure a directory exists, creating it if necessary"""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
    return directory

def load_json_config(filename):
    """Load a JSON configuration file"""
    config_path = get_config_path(filename)
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Failed to load {filename}: {str(e)}")
        return {}

def save_json_config(filename, data):
    """Save a JSON configuration file to the user data directory"""
    user_dir = get_user_data_dir()
    ensure_dir_exists(user_dir)
    config_path = os.path.join(user_dir, filename)
    
    try:
        with open(config_path, 'w') as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        logging.error(f"Failed to save {filename}: {str(e)}")
        return False

def convert_to_relative_path(base_dir, full_path):
    """Convert an absolute path to a relative path based on base_dir"""
    try:
        return os.path.relpath(full_path, base_dir)
    except ValueError:
        # If paths are on different drives, return the original path
        return full_path

def convert_to_absolute_path(base_dir, rel_path):
    """Convert a relative path to an absolute path based on base_dir"""
    if os.path.isabs(rel_path):
        return rel_path
    return os.path.normpath(os.path.join(base_dir, rel_path))

def setup_logging(log_name):
    """Set up logging to both file and console"""
    log_dir = ensure_dir_exists(os.path.join(get_user_data_dir(), 'logs'))
    log_file = os.path.join(log_dir, f"{log_name}.log")
    
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return log_file
