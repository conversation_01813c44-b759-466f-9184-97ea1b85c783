# Obsidian Documentation Status Updater

This script searches through a selected folder and its subfolders for PDF files that might be machine manuals. When a manual is found, it updates the corresponding machine note in your Obsidian vault to set `Documentation: true` in the YAML frontmatter.

## Features

- GUI interface for selecting folders to search
- Recursively searches through subfolders for PDF files
- Identifies machine manuals based on filename patterns
- Updates the YAML frontmatter in corresponding machine notes
- Provides a log of all actions taken
- Dry run option to preview changes without modifying files

## Requirements

- Python 3.6 or higher
- Required Python packages (install with `pip install -r requirements.txt`):
  - customtkinter
  - python-frontmatter
  - pyyaml

## Usage

1. Run the script:
   ```
   python apps/update_documentation_status.py
   ```

2. In the GUI:
   - Click "Browse..." to select the folder to search for manuals
   - Click "Browse..." to select your Obsidian Machines folder
   - Check "Search recursively in subfolders" if you want to search in all subfolders
   - Check "Dry run" if you want to preview changes without modifying files
   - Click "Run" to start the process

3. The script will:
   - Search for PDF files that match manual patterns
   - Extract identifiers from manual filenames
   - Find corresponding machine notes in your Obsidian vault
   - Update the YAML frontmatter in those notes to set `Documentation: true`
   - Provide a log of all actions taken

## How It Works

The script specifically looks for PDF files in folders named "merged" with filenames matching these patterns:
- `<serial>.pdf` (e.g., "241035.pdf")
- `<serial> manual.pdf` (e.g., "241035 manual.pdf")
- `<serial> Manual.PDF` (e.g., "241035 Manual.PDF")

It extracts the serial number from the filename and searches for a corresponding machine note named `<serial>.md` (e.g., "241035.md") in your Obsidian vault's Machines folder.

When a match is found, the script updates the YAML frontmatter in the machine note to set `Documentation: true`. If the note already has `Documentation: true`, it is skipped.

## Customization

You can customize the script by modifying the following variables:

- `manual_patterns`: List of regex patterns to match manual filenames
- `machines_folder`: Default folder to search for machine notes

## Troubleshooting

If the script is not finding manuals or updating notes as expected:

1. Check the log for error messages
2. Verify that your manual filenames follow the expected pattern
3. Ensure that your machine notes have YAML frontmatter with a `Documentation` field
4. Try running with the "Dry run" option to preview changes without modifying files

## License

This script is provided as-is with no warranty. Use at your own risk.
