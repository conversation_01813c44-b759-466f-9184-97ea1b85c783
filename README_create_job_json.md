# Job Data Creator Tool

The Job Data Creator Tool is a specialized utility for creating standardized JSON files containing project information. This tool provides a structured interface for entering job details and generates JSON files with consistent formatting and naming conventions for project tracking and documentation purposes.

## Features

- **Structured Data Entry**: Organized form interface for project information
- **Model Integration**: Dropdown selection from configured engineering models
- **Category Management**: Equipment category selection with model filtering
- **Automatic Validation**: Required field validation and data consistency checks
- **Standardized Output**: Consistent JSON format for all project files
- **Timestamp Generation**: Automatic creation timestamp for tracking
- **File Naming Convention**: Standardized filename format using GSS Parent # and Serial Number
- **Dark Mode Interface**: Modern GUI using CustomTkinter

## Installation

### Option 1: Run the Python Script

1. Ensure you have Python 3.8 or higher installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the script:
   ```
   python apps/create_job_json.py
   ```

### Option 2: Use via Engineering Tools Launcher

1. Run the launcher:
   ```
   python apps_launcher.py
   ```
2. Click "Launch" next to "Job Data Creator" in the tools list

## Usage

### Data Entry Process

1. **Launch the application**
2. **Enter Project Information**:
   - GSS Parent # (required)
   - Serial number (required)
   - Customer name
   - Location
   - Title/Description
   - Sales order #

3. **Select Equipment Details**:
   - Choose equipment category from dropdown
   - Select specific model from filtered list

4. **Choose Save Location**:
   - Click "Browse" to select job folder
   - Verify the destination directory

5. **Create JSON File**:
   - Click "Create JSON" to generate the file
   - File will be saved with standardized naming

### Required Fields

The following fields are mandatory:
- **GSS Parent #**: Project identifier number
- **Serial number**: Unit serial number

All other fields are optional but recommended for complete documentation.

## Output Format

### JSON Structure

The generated JSON file contains:

```json
{
    "gss_parent": "123456",
    "serial_number": "789012",
    "customer": "Customer Name",
    "location": "Installation Location",
    "title": "Project Title",
    "sales_order": "SO123456",
    "model": "Selected Model",
    "category": "Equipment Category",
    "job_folder": "/path/to/job/folder",
    "timestamp": "2024-01-15 14:30:22"
}
```

### File Naming Convention

Files are automatically named using the format:
```
[GSS Parent #] [Serial Number].json
```

Example: `123456 789012.json`

## Model Integration

### Category Selection

The tool integrates with the engineering models system:
- **Dynamic Categories**: Loads available categories from models.json
- **Model Filtering**: Shows only models for selected category
- **Validation**: Ensures selected combinations are valid

### Model Configuration

Models are loaded from the system configuration:
- **models.json**: Contains all available equipment models
- **Category Organization**: Models grouped by equipment type
- **Automatic Updates**: Reflects changes in model configuration

## Use Cases

### Project Documentation

- **Job Tracking**: Create standardized records for all projects
- **Data Consistency**: Ensure uniform information capture
- **Integration**: Provide data for other tools and processes

### Workflow Integration

- **Manual Creation**: Provides data for manual generation tools
- **Publishing**: Supplies information for project publishing
- **Reporting**: Enables automated reporting and analysis

## Requirements

- Python 3.8 or higher
- Required Python packages:
  - customtkinter
  - json (built-in)
  - datetime (built-in)
- Access to models.json configuration file

## Troubleshooting

### Common Issues

1. **Models Not Loading**
   - Verify models.json file exists and is accessible
   - Check file format and syntax
   - Ensure proper permissions for configuration files

2. **Save Location Issues**
   - Confirm write permissions for selected directory
   - Verify directory exists and is accessible
   - Check available disk space

3. **Validation Errors**
   - Ensure required fields (GSS Parent #, Serial Number) are filled
   - Check for special characters that might cause issues
   - Verify data format consistency

### Error Messages

The application provides specific error messages for:
- Missing required fields
- File save failures
- Configuration loading issues
- Invalid data formats

## Technical Details

### Data Validation

- **Required Field Checking**: Validates mandatory information
- **Format Validation**: Ensures data consistency
- **Model Validation**: Confirms valid category/model combinations

### File Operations

- **JSON Serialization**: Uses Python's built-in JSON library
- **File Naming**: Automatic generation based on project data
- **Path Handling**: Cross-platform file path management

### Configuration Integration

- **Model Loading**: Reads from centralized models.json
- **Category Management**: Dynamic category population
- **Error Handling**: Graceful handling of configuration issues

## Integration with Other Tools

### Manual Creator

The JSON files created by this tool can be used by:
- **Manual Creation Tools**: Provides project data for document generation
- **Publishing Tools**: Supplies information for project publishing
- **Reporting Systems**: Enables automated data collection

### Data Flow

1. **Job Data Creator**: Creates standardized JSON files
2. **Other Tools**: Read JSON files for project information
3. **Workflow Automation**: Enables automated processing

## Customization

### Field Modification

To add or modify fields:
1. Update the form interface in the source code
2. Modify the JSON structure generation
3. Update validation logic as needed

### Model Integration

The tool automatically adapts to changes in:
- **models.json**: New categories and models appear automatically
- **Configuration Updates**: No code changes required for model updates

## Best Practices

### Data Entry

- **Consistent Formatting**: Use standardized formats for all entries
- **Complete Information**: Fill all available fields when possible
- **Verification**: Double-check critical information before saving

### File Management

- **Organized Storage**: Save JSON files in appropriate project directories
- **Backup**: Include JSON files in project backup procedures
- **Version Control**: Track changes to project data over time

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.

## Contact

For support or questions about the Job Data Creator Tool:
Jonathan Callahan: <EMAIL>
