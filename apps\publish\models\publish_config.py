"""
Publishing configuration models.

This module contains configuration data structures for the publishing system
including PublishConfig, TitleBlockData, and ModelData.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional
import os


@dataclass
class PublishConfig:
    """Configuration settings for publishing operations."""

    create_manual: bool = None
    fill_series: bool = None
    series_count: int = None
    export_formats: List[str] = None
    export_dxf: bool = None
    update_boms: bool = None
    run_report_generator: bool = None
    run_wire_core_sync: bool = None
    bom_reports: List[str] = None
    additional_reports: List[str] = None

    def __post_init__(self):
        """Initialize default values from configuration or fallbacks."""
        # Try to load configuration
        try:
            from ..config.configuration_manager import ConfigurationManager
            config_manager = ConfigurationManager()
            config = config_manager.load_app_config()
            publish_config = config.get('publishing', {}).get('config', {})
            reports_config = config.get('publishing', {}).get('reports', {})
        except:
            publish_config = {}
            reports_config = {}

        # Set defaults from configuration or fallbacks
        if self.create_manual is None:
            self.create_manual = publish_config.get('create_manual', False)
        if self.fill_series is None:
            self.fill_series = publish_config.get('fill_series', False)
        if self.series_count is None:
            self.series_count = publish_config.get('series_count', 1)
        if self.export_formats is None:
            self.export_formats = publish_config.get('export_formats', ["PDF"])
        if self.export_dxf is None:
            self.export_dxf = publish_config.get('export_dxf', True)
        if self.update_boms is None:
            self.update_boms = publish_config.get('update_boms', True)
        if self.run_report_generator is None:
            self.run_report_generator = publish_config.get('run_report_generator', False)
        if self.run_wire_core_sync is None:
            self.run_wire_core_sync = publish_config.get('run_wire_core_sync', True)
        if self.bom_reports is None:
            self.bom_reports = reports_config.get('default_bom_reports', ["PHOENIX BOM", "Simplified BOM"])
        if self.additional_reports is None:
            self.additional_reports = reports_config.get('additional_reports', ["Field Cable List", "Field Cable List without Cores"])

        # Ensure valid export formats
        export_config = config.get('publishing', {}).get('export', {}) if 'config' in locals() else {}
        valid_formats = export_config.get('valid_formats', ["PDF", "DXF"])
        self.export_formats = [fmt.upper() for fmt in self.export_formats if fmt.upper() in valid_formats]

        if not self.export_formats:
            default_formats = export_config.get('default_formats', ["PDF"])
            self.export_formats = [fmt.upper() for fmt in default_formats]
            
    def validate(self) -> 'ValidationResult':
        """
        Validate the publishing configuration.
        
        Returns:
            ValidationResult indicating if configuration is valid
        """
        from .project_data import ValidationResult
        
        errors = []
        warnings = []
        
        # Validate series count
        if self.series_count < 1:
            errors.append("Series count must be at least 1")
        elif self.series_count > 100:
            warnings.append("Large series count may take significant time")
        elif self.series_count > 50:
            warnings.append("Series count over 50 may take considerable time")
            
        # Validate export formats
        if not self.export_formats:
            errors.append("At least one export format must be specified")
        
        valid_formats = ["PDF", "DXF"]
        invalid_formats = [fmt for fmt in self.export_formats if fmt not in valid_formats]
        if invalid_formats:
            errors.append(f"Invalid export formats: {', '.join(invalid_formats)}")
            
        # Logical validation
        if self.fill_series and self.series_count == 1:
            warnings.append("Fill series is enabled but series count is 1")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary representation."""
        return {
            "create_manual": self.create_manual,
            "fill_series": self.fill_series,
            "series_count": self.series_count,
            "export_formats": self.export_formats,
            "export_dxf": self.export_dxf,
            "update_boms": self.update_boms,
            "run_report_generator": self.run_report_generator,
            "run_wire_core_sync": self.run_wire_core_sync,
            "bom_reports": self.bom_reports,
            "additional_reports": self.additional_reports
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'PublishConfig':
        """Create from dictionary representation."""
        return cls(
            create_manual=data.get("create_manual", False),
            fill_series=data.get("fill_series", False),
            series_count=data.get("series_count", 1),
            export_formats=data.get("export_formats", ["PDF"]),
            export_dxf=data.get("export_dxf", True),
            update_boms=data.get("update_boms", True),
            run_report_generator=data.get("run_report_generator", False),
            run_wire_core_sync=data.get("run_wire_core_sync", True),
            bom_reports=data.get("bom_reports", ["PHOENIX BOM", "Simplified BOM"]),
            additional_reports=data.get("additional_reports", ["Field Cable List", "Field Cable List without Cores"])
        )


@dataclass
class ModelData:
    """Configuration data for a specific model."""
    
    template_path: str = ""
    drawings_path: str = ""
    asme_flag: bool = False
    controls_parent: str = ""
    
    def __post_init__(self):
        """Perform post-initialization validation and cleanup."""
        self.template_path = self.template_path.strip()
        self.drawings_path = self.drawings_path.strip()
        self.controls_parent = self.controls_parent.strip()
    
    def validate(self) -> 'ValidationResult':
        """
        Validate the model configuration.
        
        Returns:
            ValidationResult indicating if configuration is valid
        """
        from .project_data import ValidationResult
        
        errors = []
        warnings = []
        
        # Validate template path
        if not self.template_path:
            errors.append("Template path is required")
        elif not os.path.exists(self.template_path):
            errors.append(f"Template path does not exist: {self.template_path}")
        elif not self.template_path.lower().endswith(('.dotx', '.docx', '.dot', '.doc')):
            warnings.append("Template path does not appear to be a Word document")
            
        # Validate drawings path
        if not self.drawings_path:
            warnings.append("Drawings path is not specified")
        elif not os.path.exists(self.drawings_path):
            warnings.append(f"Drawings path does not exist: {self.drawings_path}")
            
        # Validate controls parent
        if not self.controls_parent:
            errors.append("Controls parent number is required")
        elif not self.controls_parent.isdigit():
            warnings.append("Controls parent should typically be numeric")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary representation."""
        return {
            "template_path": self.template_path,
            "drawings_path": self.drawings_path,
            "asme_flag": self.asme_flag,
            "controls_parent": self.controls_parent
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ModelData':
        """Create from dictionary representation."""
        return cls(
            template_path=data.get("template_path", ""),
            drawings_path=data.get("drawings_path", ""),
            asme_flag=data.get("asme_flag", False),
            controls_parent=data.get("controls_parent", "")
        )
    
    def has_manual_template(self) -> bool:
        """Check if model has a valid manual template."""
        return bool(self.template_path and os.path.exists(self.template_path))
    
    def has_drawings(self) -> bool:
        """Check if model has a valid drawings path."""
        return bool(self.drawings_path and os.path.exists(self.drawings_path))


@dataclass
class ModelsConfiguration:
    """Complete models configuration loaded from models.json."""
    
    models: Dict[str, Dict[str, ModelData]] = field(default_factory=dict)
    
    def __post_init__(self):
        """Ensure all model data is properly typed."""
        # Convert any dict entries to ModelData objects
        for category, category_models in self.models.items():
            for model_name, model_data in category_models.items():
                if isinstance(model_data, dict):
                    self.models[category][model_name] = ModelData.from_dict(model_data)
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ModelsConfiguration':
        """Create from dictionary (loaded from JSON)."""
        models = {}
        for category, category_models in data.items():
            models[category] = {}
            for model_name, model_data in category_models.items():
                if isinstance(model_data, dict):
                    models[category][model_name] = ModelData.from_dict(model_data)
                else:
                    models[category][model_name] = model_data
        
        return cls(models=models)
    
    def get_model(self, model_name: str) -> Optional[ModelData]:
        """
        Get model data by name across all categories.
        
        Args:
            model_name: Name of the model to find
            
        Returns:
            ModelData if found, None otherwise
        """
        for category_models in self.models.values():
            if model_name in category_models:
                return category_models[model_name]
        return None
    
    def get_models_for_gss(self, gss_parent: str) -> List[str]:
        """
        Get all model names that match the specified GSS parent number.
        
        Args:
            gss_parent: GSS parent number to match
            
        Returns:
            List of model names that match the GSS parent
        """
        matching_models = []
        for category_models in self.models.values():
            for model_name, model_data in category_models.items():
                if model_data.controls_parent == gss_parent:
                    matching_models.append(model_name)
        return sorted(matching_models)
    
    def get_all_models(self) -> List[str]:
        """Get all model names across all categories."""
        all_models = []
        for category_models in self.models.values():
            all_models.extend(category_models.keys())
        return sorted(all_models)
    
    def get_categories(self) -> List[str]:
        """Get all category names."""
        return sorted(self.models.keys())
    
    def validate_all(self) -> 'ValidationResult':
        """
        Validate all model configurations.
        
        Returns:
            ValidationResult with all validation issues
        """
        from .project_data import ValidationResult
        
        all_errors = []
        all_warnings = []
        
        for category, category_models in self.models.items():
            for model_name, model_data in category_models.items():
                result = model_data.validate()
                if result.errors:
                    all_errors.extend([f"{category}/{model_name}: {error}" for error in result.errors])
                if result.warnings:
                    all_warnings.extend([f"{category}/{model_name}: {warning}" for warning in result.warnings])
        
        return ValidationResult(
            is_valid=len(all_errors) == 0,
            errors=all_errors,
            warnings=all_warnings
        )