"""
Test cases for E3 integration layer.

This module contains unit tests for the E3Client and ReportGeneratorClient classes.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the apps directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'apps'))

from publish.integrations.e3_client import (
    E3Client, E3ConnectionError, E3OperationError, 
    E3ValidationError, E3TimeoutError
)
from publish.integrations.report_generator import (
    ReportGeneratorClient, ReportGeneratorError, 
    ReportGeneratorValidationError, ReportGeneratorTimeoutError
)
from publish.models.project_data import ProjectData, TitleBlockData


class TestE3Client(unittest.TestCase):
    """Test cases for E3Client class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = E3Client()
    
    def test_init(self):
        """Test E3Client initialization."""
        self.assertIsNone(self.client.app)
        self.assertIsNone(self.client.job)
        self.assertFalse(self.client._connected)
    
    def test_is_connected_false_initially(self):
        """Test is_connected returns False initially."""
        self.assertFalse(self.client.is_connected())
    
    def test_connection_error_when_not_connected(self):
        """Test that operations raise E3ConnectionError when not connected."""
        with self.assertRaises(E3ConnectionError):
            self.client.read_title_block_data()
        
        with self.assertRaises(E3ConnectionError):
            self.client.update_attributes(ProjectData())
    
    def test_validation_error_for_invalid_project_data(self):
        """Test validation errors for invalid project data."""
        # Mock connection
        self.client._connected = True
        self.client.app = Mock()
        self.client.job = Mock()
        
        # Test with None project data
        with self.assertRaises(E3ValidationError):
            self.client.update_attributes(None)
        
        # Test with empty GSS parent
        project_data = ProjectData(gss_parent="", serial_number="001")
        with self.assertRaises(E3ValidationError):
            self.client.update_attributes(project_data)
        
        # Test with empty serial number
        project_data = ProjectData(gss_parent="TEST-001", serial_number="")
        with self.assertRaises(E3ValidationError):
            self.client.update_attributes(project_data)


class TestReportGeneratorClient(unittest.TestCase):
    """Test cases for ReportGeneratorClient class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = ReportGeneratorClient()
    
    def test_init(self):
        """Test ReportGeneratorClient initialization."""
        self.assertIsNotNone(self.client.report_generator_path)
        self.assertIsNotNone(self.client.config_path)
    
    def test_validation_errors(self):
        """Test validation errors for invalid inputs."""
        with self.assertRaises(ReportGeneratorValidationError):
            self.client.run_bom_report("", "ExcelExport", 1234)
        
        with self.assertRaises(ReportGeneratorValidationError):
            self.client.run_bom_report("Test Report", "", 1234)
        
        with self.assertRaises(ReportGeneratorValidationError):
            self.client.run_bom_report("Test Report", "ExcelExport", 0)
    
    def test_is_available(self):
        """Test is_available method."""
        # This will likely return False in test environment
        result = self.client.is_available()
        self.assertIsInstance(result, bool)
    
    def test_export_gss_bom_validation(self):
        """Test GSS BOM export validation."""
        with self.assertRaises(ReportGeneratorValidationError):
            self.client.export_gss_bom(1234, "", "001")
        
        with self.assertRaises(ReportGeneratorValidationError):
            self.client.export_gss_bom(1234, "TEST-001", "")


class TestDataModelIntegration(unittest.TestCase):
    """Test integration between data models and integration layer."""
    
    def test_title_block_to_project_data_conversion(self):
        """Test conversion from TitleBlockData to ProjectData."""
        title_block = TitleBlockData(
            document_number="TEST-001",
            model="Test Model",
            description="Test Description",
            customer="Test Customer",
            location="Test Location",
            sales_order="SO-001",
            serial_number="001"
        )
        
        project_data = title_block.to_project_data(
            folder_path="/test/path",
            model_override="Override Model"
        )
        
        self.assertEqual(project_data.gss_parent, "TEST-001")
        self.assertEqual(project_data.serial_number, "001")
        self.assertEqual(project_data.customer, "Test Customer")
        self.assertEqual(project_data.location, "Test Location")
        self.assertEqual(project_data.title, "Test Description")
        self.assertEqual(project_data.sales_order, "SO-001")
        self.assertEqual(project_data.model, "Override Model")  # Should use override
        self.assertEqual(project_data.folder_path, "/test/path")
    
    def test_project_data_validation(self):
        """Test ProjectData validation."""
        project_data = ProjectData(
            gss_parent="TEST-001",
            serial_number="001",
            customer="Test Customer",
            model="Test Model",
            folder_path="/test/path"
        )
        
        validation_result = project_data.validate()
        # Note: This may fail due to path validation, but structure should be correct
        self.assertIsNotNone(validation_result)
        self.assertIsInstance(validation_result.errors, list)
        self.assertIsInstance(validation_result.warnings, list)


if __name__ == '__main__':
    unittest.main()