"""
Tests for the __init__.py files.

This module contains unit tests for the __init__.py files in the lib and applications packages.
"""

import os
import sys
import unittest

# Add parent directory to path to allow importing from lib and applications
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestInit(unittest.TestCase):
    """Test case for the __init__.py files."""
    
    def test_lib_init(self):
        """Test the lib.__init__ module."""
        import lib
        
        # Check that the version is defined
        self.assertTrue(hasattr(lib, "__version__"))
        self.assertIsInstance(lib.__version__, str)
    
    def test_applications_init(self):
        """Test the applications.__init__ module."""
        import applications
        
        # Check that the module can be imported
        self.assertTrue(True)

if __name__ == "__main__":
    unittest.main()
