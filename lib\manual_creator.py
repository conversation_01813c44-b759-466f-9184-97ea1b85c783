import os
import shutil
import logging
import tempfile
import zipfile
import json
import sys
import time
import win32com.client as win32
from docx import Document
from docx2pdf import convert
from PyPDF2 import PdfMerger
from docx.shared import Inches
from PIL import Image

# Import utility functions
try:
    from utils import get_resource_path, get_config_path
except ImportError:
    try:
        from lib.utils import get_resource_path, get_config_path
    except ImportError:
        # Fallback implementation if utils module is not available
        def get_app_dir():
            """Get the application directory, works for both development and PyInstaller"""
            try:
                # PyInstaller creates a temp folder and stores path in _MEIPASS
                base_path = sys._MEIPASS
            except Exception:
                # We're running in a normal Python environment
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            return base_path

        def get_resource_path(relative_path):
            """Get absolute path to resource, works for dev and for PyInstaller"""
            app_dir = get_app_dir()
            return os.path.join(app_dir, relative_path)

        def get_config_path(filename):
            """Get path to a configuration file in the resources/config directory"""
            return get_resource_path(os.path.join('resources', 'config', filename))

# For backward compatibility
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    # Check if it's a config file that should be in resources/config
    if relative_path.endswith('.json'):
        return get_config_path(os.path.basename(relative_path))
    return get_resource_path(relative_path)

# Configure logging to both file and console
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("publish_debug.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
app = win32.Dispatch("CT.Application")

# Load the MODELS dictionary from the JSON file using get_config_path
json_file_path = get_config_path("models.json")
logging.info(f"Attempting to load models from: {json_file_path}")
app.PutInfo(0, f"Attempting to load models from: {json_file_path}")

try:
    with open(json_file_path, 'r') as json_file:
        MODELS = json.load(json_file)
    logging.info(f"Successfully loaded models. Categories: {list(MODELS.keys())}")
    app.PutInfo(0, f"Successfully loaded models. Categories: {list(MODELS.keys())}")
except Exception as e:
    logging.error(f"Failed to load models.json: {str(e)}")
    app.PutInfo(0, f"Failed to load models.json: {str(e)}")
    MODELS = {}
app = None

def convert_dotx_to_docx(template_path):
    """
    Copies the .dotx file to a temporary location, modifies the internal content type,
    and returns the path to the fixed .docx file.
    """
    temp_dir = tempfile.gettempdir()
    base_name = os.path.basename(template_path).replace('.dotx', '')
    temp_docx = os.path.join(temp_dir, f"{base_name}.docx")
    shutil.copy(template_path, temp_docx)

    # Read the original [Content_Types].xml from the .docx file
    with zipfile.ZipFile(temp_docx, 'r') as zin:
        content_types = zin.read('[Content_Types].xml')

    # Replace the template content type with the document content type.
    updated_content_types = content_types.replace(
        b"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml",
        b"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"
    )

    # Create a new temporary file with the fixed content.
    temp_docx_fixed = os.path.join(temp_dir, f"{base_name}_fixed.docx")
    with zipfile.ZipFile(temp_docx, 'r') as zin:
        with zipfile.ZipFile(temp_docx_fixed, 'w') as zout:
            for item in zin.infolist():
                if item.filename == '[Content_Types].xml':
                    zout.writestr(item, updated_content_types)
                else:
                    zout.writestr(item, zin.read(item.filename))
    return temp_docx_fixed

def open_template(template_path):
    """
    Opens a Word template file. If it's a .dotx file, convert it to .docx on the fly.
    """
    if template_path.lower().endswith('.dotx'):
        fixed_docx = convert_dotx_to_docx(template_path)
        return Document(fixed_docx)
    else:
        return Document(template_path)

# New: Fallback conversion function using Word COM automation
def convert_docx_to_pdf_fallback(docx_path, pdf_path):
    import pythoncom
    import time

    wdFormatPDF = 17
    word = None

    try:
        # Initialize COM
        pythoncom.CoInitialize()

        # Use win32com.client instead of comtypes for better compatibility
        try:
            # Try to connect to an existing instance first
            word = win32.GetActiveObject('Word.Application')
            logging.info("Connected to existing Word instance")
        except:
            # If no instance exists, create a new one
            word = win32.Dispatch('Word.Application')
            logging.info("Created new Word instance")

        # Ensure Word is hidden and configured properly
        word.Visible = False
        word.DisplayAlerts = False

        # Add small delay to ensure Word is ready
        time.sleep(1)

        # Full path conversion to handle any path issues
        docx_path = os.path.abspath(docx_path)
        pdf_path = os.path.abspath(pdf_path)

        logging.info(f"Converting {docx_path} to {pdf_path}")

        try:
            # Open the document
            doc = word.Documents.Open(docx_path)
            logging.info("Document opened successfully")

            # Save as PDF
            doc.SaveAs(pdf_path, FileFormat=wdFormatPDF)
            logging.info("Document saved as PDF successfully")

            # Close the document
            doc.Close(False)
            logging.info("Document closed successfully")

        except Exception as e:
            logging.error(f"Error during document processing: {str(e)}")
            raise

    except Exception as e:
        logging.error(f"Word automation error: {str(e)}")
        raise

    finally:
        if word is not None:
            try:
                # Don't quit Word if we connected to an existing instance
                # Only quit if we created a new instance
                word.Quit()
                logging.info("Word application quit successfully")
            except Exception as e:
                logging.warning(f"Error quitting Word: {e}")

        # Cleanup COM
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def convert_docx_to_pdf_with_retry(docx_path, pdf_path, max_retries=3, delay_between_retries=2):
    """Wrapper function that retries the conversion multiple times if it fails"""
    for attempt in range(max_retries):
        try:
            convert_docx_to_pdf_fallback(docx_path, pdf_path)
            return  # Success, exit function
        except Exception as e:
            if attempt == max_retries - 1:  # Last attempt
                raise  # Re-raise the last exception
            logging.warning(f"Attempt {attempt + 1} failed, retrying in {delay_between_retries} seconds...")
            time.sleep(delay_between_retries)

class ManualCreator:
    def __init__(self, job_folder, category, model, company, location, serial, model_data, document_number):
        """
        Initialize a ManualCreator instance with project-specific information.

        Args:
            job_folder (str): Path to the job folder where files will be saved
            category (str): Product category (e.g., 'Boiler', 'Heater')
            model (str): Model number or identifier
            company (str): Customer company name
            location (str): Installation location
            serial (str): Unit serial number
            model_data (tuple|list): Contains (template_path, drawings_path, asme_flag)
            document_number (str): Sheet document reference number

        Raises:
            ValueError: If model_data doesn't contain the required 3 elements
        """
        self.job_folder = job_folder
        self.category = category
        self.model = model
        self.company = company
        self.location = location
        self.serial = serial
        self.model_data = model_data
        self.sheet_document_number = document_number

        # Initialize model-specific attributes from model_data
        if isinstance(model_data, (list, tuple)) and len(model_data) >= 3:
            self.template_path = model_data[0]
            self.drawings_path = model_data[1]
            self.asme_flag = model_data[2]
        else:
            raise ValueError(f"Invalid model_data format for model {model}. Expected tuple/list with at least 3 elements.")

    def determine_category(self, model: str) -> str:
        """
        Determine the category based on the model number.
        Returns the category name or None if not found.
        """
        for category, models_dict in MODELS.items():
            if model in models_dict.keys():  # Changed from models to models_dict.keys()
                return category
        return None

    def create_manual(self):
        """
        Handle the creation of the manual.
        This method opens the Word template, replaces placeholders,
        converts the document to PDF, copies drawing PDFs, and merges PDFs.
        """
        try:
            # Use the class attributes directly
            template_path = self.template_path
            drawings_folder = self.drawings_path
            asme_flag = self.asme_flag

            logging.info(f"Creating manual with template: {template_path}")
            logging.info(f"Drawing folder path: {drawings_folder}")
            logging.info(f"ASME flag: {asme_flag}")

            # Copy drawings first
            drawing_files = []
            if os.path.exists(drawings_folder):
                logging.info(f"Copying drawings from: {drawings_folder}")
                for file in os.listdir(drawings_folder):
                    if file.lower().endswith('.pdf'):
                        src = os.path.join(drawings_folder, file)
                        dst = os.path.join(self.job_folder, file)
                        logging.info(f"Copying {src} to {dst}")
                        shutil.copy2(src, dst)
                        drawing_files.append(dst)
                        logging.info(f"Copied drawing: {file}")
            else:
                logging.error(f"Drawings folder not found: {drawings_folder}")
                raise FileNotFoundError(f"Drawings folder not found: {drawings_folder}")

            # Open the Word template
            try:
                doc = open_template(template_path)
            except Exception as e:
                logging.error(f"Failed to open template: {e}")
                return

            # Define placeholders and their replacements
            replacements = {
                "{{company}}": self.company,
                "{{location}}": self.location,
                "{{serial}}": self.serial,
                "{{model}}": self.model.upper(),
                "{{controls_parent}}": self.sheet_document_number
            }

            logging.debug(f"Replacements: {replacements}")

            # Replace placeholders in the document
            self.replace_placeholders(doc, replacements)
            replace_first_image(doc, drawings_folder)  # <-- New code to replace first image

            # Save the modified document
            manual_docx_path = os.path.join(self.job_folder, f"{self.serial}_manual.docx")
            if os.path.exists(manual_docx_path):
                try:
                    os.remove(manual_docx_path)
                except PermissionError:
                    logging.warning(f"File locked: {manual_docx_path}. Generating alternative filename.")
                    base, ext = os.path.splitext(manual_docx_path)
                    manual_docx_path = f"{base}_new{ext}"
            doc.save(manual_docx_path)

            # New: Convert the Word document to PDF with fallback
            manual_pdf_path = os.path.join(self.job_folder, f"{self.serial}_manual.pdf")

            # Remove existing PDF if it exists
            if os.path.exists(manual_pdf_path):
                try:
                    os.remove(manual_pdf_path)
                    logging.info(f"Removed existing PDF: {manual_pdf_path}")
                except Exception as e:
                    logging.warning(f"Could not remove existing PDF: {e}")

            try:
                logging.info(f"Converting DOCX to PDF using docx2pdf: {manual_docx_path} -> {manual_pdf_path}")
                convert(manual_docx_path, manual_pdf_path)
                logging.info("docx2pdf conversion successful")

                # Verify the PDF was created
                if os.path.exists(manual_pdf_path):
                    logging.info(f"PDF created successfully: {manual_pdf_path}")
                else:
                    raise Exception("PDF file was not created by docx2pdf")

            except Exception as e:
                logging.error(f"Failed to convert docx to PDF using docx2pdf: {e}. Attempting fallback conversion.")
                try:
                    logging.info("Starting Word COM automation fallback conversion")
                    convert_docx_to_pdf_fallback(manual_docx_path, manual_pdf_path)

                    # Verify the PDF was created
                    if os.path.exists(manual_pdf_path):
                        logging.info(f"Fallback conversion successful: {manual_pdf_path}")
                    else:
                        raise Exception("PDF file was not created by fallback conversion")

                except Exception as e2:
                    logging.error(f"Fallback conversion failed: {e2}")
                    logging.error("Manual creation will continue without PDF conversion")
                    # Don't return here - continue with the rest of the process

            # Look for the published schematics PDF in the job folder
            schematics_pdf = None
            # Match the exact format from Publish 3.py's export_to_pdf method
            published_pdf = os.path.join(self.job_folder, f"{self.sheet_document_number} {self.serial}.pdf")  # Changed this line
            if os.path.exists(published_pdf):
                schematics_pdf = published_pdf
                logging.info(f"Found published schematics: {published_pdf}")
            else:
                # Try alternative format as fallback
                alt_published_pdf = os.path.join(self.job_folder, f"{self.serial}.pdf")
                if os.path.exists(alt_published_pdf):
                    schematics_pdf = alt_published_pdf
                    logging.info(f"Found published schematics (alternative format): {alt_published_pdf}")
                else:
                    logging.warning(f"No published schematics found at: {published_pdf} or {alt_published_pdf}")

            # Copy mechanical drawings from the drawings folder to the job folder
            drawing_files = []
            try:
                if not os.path.exists(drawings_folder):
                    raise FileNotFoundError(f"Drawings folder not found: {drawings_folder}")
                for file in os.listdir(drawings_folder):
                    if file.lower().endswith('.pdf'):
                        src = os.path.join(drawings_folder, file)
                        dst = os.path.join(self.job_folder, file)
                        shutil.copy2(src, dst)
                        drawing_files.append(dst)
                        logging.info(f"Copied drawing: {file}")
            except Exception as e:
                logging.error(f"Failed to copy drawings: {e}")
                return

            # Build the list of PDFs to merge in the required order
            # Order: manual -> published schematics -> u1a (if ASME) -> mechanical drawings
            merge_list = [manual_pdf_path]

            # Add published schematics right after the manual
            if schematics_pdf:
                merge_list.append(schematics_pdf)

            # Handle mechanical drawings
            u1a_pdf = None
            other_drawings = []

            for pdf in drawing_files:
                fname = os.path.basename(pdf).lower()
                if fname == "u1a.pdf":
                    u1a_pdf = pdf
                else:
                    other_drawings.append(pdf)

            # Add U1A if it exists and ASME flag is set
            if asme_flag and u1a_pdf:
                merge_list.append(u1a_pdf)

            # Add remaining mechanical drawings
            merge_list.extend(other_drawings)

            if len(merge_list) == 1:
                logging.warning("No additional files found to merge with manual. Only manual PDF will be available.")
                # Don't return - we still have the manual PDF
                if os.path.exists(manual_pdf_path):
                    logging.info(f"Manual PDF created successfully: {manual_pdf_path}")
                else:
                    logging.error("Manual PDF was not created successfully")
                return

            # Create the merged subfolder
            merged_folder = os.path.join(self.job_folder, "merged")
            os.makedirs(merged_folder, exist_ok=True)
            merged_pdf_path = os.path.join(merged_folder, f"{self.serial} Manual.pdf")

            # Merge the PDFs preserving internal links using pikepdf
            import pikepdf
            try:
                merged_pdf = pikepdf.Pdf.new()
                for pdf_path in merge_list:
                    src_pdf = pikepdf.Pdf.open(pdf_path)
                    merged_pdf.pages.extend(src_pdf.pages)
                    src_pdf.close()
                merged_pdf.save(merged_pdf_path)
                logging.info(f"Successfully created merged PDF: {merged_pdf_path}")
                #self.open_pdf(merged_pdf_path)  # Open the merged PDF after creation
            except Exception as e:
                logging.error(f"Failed to merge PDFs using pikepdf: {e}")
                return

            # Log summary of created files
            created_files = []
            if os.path.exists(manual_docx_path):
                created_files.append(manual_docx_path)
            if os.path.exists(manual_pdf_path):
                created_files.append(manual_pdf_path)
            if os.path.exists(merged_pdf_path):
                created_files.append(merged_pdf_path)

            logging.info(f"Manual creation completed. Created {len(created_files)} files:")
            for file_path in created_files:
                logging.info(f"  - {file_path}")

        except Exception as e:
            logging.error(f"Error in create_manual: {str(e)}")

            # Still log any files that were created before the error
            created_files = []
            if hasattr(self, 'job_folder') and self.job_folder:
                manual_docx_path = os.path.join(self.job_folder, f"{self.serial}_manual.docx")
                manual_pdf_path = os.path.join(self.job_folder, f"{self.serial}_manual.pdf")

                if os.path.exists(manual_docx_path):
                    created_files.append(manual_docx_path)
                if os.path.exists(manual_pdf_path):
                    created_files.append(manual_pdf_path)

                if created_files:
                    logging.info(f"Partial manual creation - {len(created_files)} files created before error:")
                    for file_path in created_files:
                        logging.info(f"  - {file_path}")

            raise

    def replace_placeholders(self, document, replacements):
        """
        Replace all occurrences of the given placeholders in the document.

        Args:
            document (Document): The python-docx Document object.
            replacements (dict): Dictionary with placeholders as keys and replacement text as values.
        """
        # Replace placeholders in paragraphs.
        for paragraph in document.paragraphs:
            for placeholder, replacement in replacements.items():
                if placeholder in paragraph.text:
                    # Iterate through runs to replace text.
                    for run in paragraph.runs:
                        if placeholder in run.text:
                            run.text = run.text.replace(placeholder, replacement)
        # Replace placeholders in tables.
        for table in document.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for placeholder, replacement in replacements.items():
                            if placeholder in paragraph.text:
                                for run in paragraph.runs:
                                    if placeholder in run.text:
                                        run.text = run.text.replace(placeholder, replacement)

    def open_pdf(self, pdf_path):
        """
        Open the PDF file using the system default application.
        """
        try:
            os.startfile(pdf_path)
        except Exception as e:
            logging.error(f"Failed to open PDF: {e}")

    def publish(self) -> None:
        """
        Publish the project and optionally create the manual.
        """
        if not self.folder_selected:
            logging.error("No folder selected for project export.")
            return

        try:
            app = win32.Dispatch("CT.Application")
            # Update title block data from UI
            self.sheet_document_number = self.entries["GSS Parent #"].get()
            self.sheet_model = self.entries["Model"].get()
            self.sheet_description = self.entries["Title"].get()
            self.sheet_customer = self.entries["Customer"].get()
            self.sheet_location = self.entries["Location"].get()
            self.sheet_sales_order = self.entries["Sales order #"].get()
            self.sheet_serial_number = self.entries["Serial number"].get()

            # Save path for the schematic
            save_path = f"{self.folder_selected}\\{self.sheet_document_number} {self.sheet_serial_number}.e3s"

            # Write GSS parent number and update sheet title block
            self.write_gss_parent(self.sheet_document_number, self.sheet_serial_number, app)
            self.write_sheet_title_block_data(app)

            # Run report generator
            self.run_reportgenerator(app)

            # Save the project
            job = app.CreateJobObject()
            job.SaveAs(save_path)
            logging.info(f"Project saved to {save_path}")

            # Export to PDF
            self.export_to_pdf(app, job)

            # If checkbox is checked, create the manual
            if self.create_manual_var.get():
                model = self.sheet_model.strip()  # Remove any whitespace
                category = self.determine_category(model)

                if category:
                    logging.info(f"Creating manual for model {model} in category {category}")
                    try:
                        model_info = MODELS[category][model]
                        manual_creator = ManualCreator(
                            self.folder_selected,
                            category,
                            model,
                            self.sheet_customer,
                            self.sheet_location,
                            self.sheet_serial_number,
                            model_info,
                            self.sheet_document_number
                        )
                        manual_creator.create_manual()
                        logging.info(f"Manual created for {model}")
                    except Exception as e:
                        logging.error(f"Failed to create manual: {str(e)}")
                else:
                    logging.error(f"Could not find category for model {model}. Available categories: {list(MODELS.keys())}")
                    logging.error(f"Model value: '{model}'")

        except Exception as exc:
            logging.error(f"An error occurred during publish: {exc}")
            logging.exception("Detailed error information:")  # This will print the full stack trace
        finally:
            job = None
            app = None

def replace_first_image(document, drawings_folder):
    """
    Replace the first image in the document with the image from the drawings folder that contains the word 'render',
    maintaining the original width and adjusting height to preserve aspect ratio.
    """
    # Find a candidate image file from the drawings folder
    candidate = None

    logging.info(f"Scanning files in drawings folder: {drawings_folder}")
    for file in os.listdir(drawings_folder):
        logging.info(f"Checking file: {file}")
        if "render" in file.lower() and any(file.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg']):
            candidate = os.path.join(drawings_folder, file)
            logging.info(f"Found candidate render image: {candidate}")
            break

    if not candidate:
        logging.warning("No render image found in drawings folder.")
        return

    # Find all images in the document
    image_parts = []
    for rel in document.part.rels.values():
        if rel.reltype == 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/image':
            image_parts.append(rel.target_part)

    if not image_parts:
        logging.warning("No images found in document.")
        return

    logging.info(f"Found {len(image_parts)} images in document")

    # Find the first image in the document
    first_image_found = False
    target_width = None

    # First, try to find the image and its dimensions
    for paragraph in document.paragraphs:
        if first_image_found:
            break

        for run in paragraph.runs:
            drawing_elements = run._element.xpath('.//w:drawing')
            if drawing_elements:
                logging.info("Found image in document")
                first_image_found = True

                # Try to get the original width
                try:
                    inline = drawing_elements[0].find('.//wp:inline', run._element.nsmap)
                    if inline is not None:
                        extent = inline.find('.//wp:extent', run._element.nsmap)
                        if extent is not None:
                            cx = extent.get('cx')
                            if cx:
                                target_width = int(cx)  # Width in EMUs
                                logging.info(f"Original image width: {target_width} EMUs ({target_width/914400} inches)")
                except Exception as e:
                    logging.error(f"Error getting image dimensions: {e}")
                break

    if not first_image_found:
        logging.warning("Could not find any images in the document.")
        return

    # Now replace the first image
    first_image_replaced = False
    for paragraph in document.paragraphs:
        if first_image_replaced:
            break

        for run in paragraph.runs:
            drawing_elements = run._element.xpath('.//w:drawing')
            if drawing_elements:
                try:
                    # Clear the run
                    run.clear()

                    # Add the new image with the target width if available
                    if target_width:
                        width_inches = target_width / 914400  # Convert EMUs to inches
                        run.add_picture(candidate, width=Inches(width_inches))
                        logging.info(f"Replaced image with fixed width: {width_inches} inches")
                    else:
                        # If we couldn't get the original width, use a default width
                        run.add_picture(candidate, width=Inches(6.0))  # Default to 6 inches width
                        logging.info("Replaced image with default width of 6 inches")

                    first_image_replaced = True
                    break
                except Exception as e:
                    logging.error(f"Error replacing image: {e}")
                    # Try a different approach as fallback
                    try:
                        run.clear()
                        run.add_picture(candidate)
                        logging.info("Replaced image using fallback method")
                        first_image_replaced = True
                        break
                    except Exception as e2:
                        logging.error(f"Fallback image replacement also failed: {e2}")

    if first_image_replaced:
        logging.info("Successfully replaced the first image in the document")
    else:
        logging.warning("Failed to replace any images in the document")
