"""
Unit tests for publish configuration models.
"""

import unittest
import tempfile
import os
from unittest.mock import patch

# Add the parent directory to the path to import from apps
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.models.publish_config import PublishConfig, ModelData, ModelsConfiguration


class TestPublishConfig(unittest.TestCase):
    """Test cases for PublishConfig class."""
    
    def test_publish_config_creation(self):
        """Test PublishConfig creation with default values."""
        config = PublishConfig()
        self.assertFalse(config.create_manual)
        self.assertFalse(config.fill_series)
        self.assertEqual(config.series_count, 1)
        self.assertEqual(config.export_formats, ["PDF"])
        self.assertTrue(config.export_dxf)
        self.assertTrue(config.update_boms)
        self.assertFalse(config.run_report_generator)
    
    def test_publish_config_with_values(self):
        """Test PublishConfig creation with specific values."""
        config = PublishConfig(
            create_manual=True,
            fill_series=True,
            series_count=5,
            export_formats=["PDF", "DXF"],
            export_dxf=False
        )
        self.assertTrue(config.create_manual)
        self.assertTrue(config.fill_series)
        self.assertEqual(config.series_count, 5)
        self.assertEqual(config.export_formats, ["PDF", "DXF"])
        self.assertFalse(config.export_dxf)
    
    def test_publish_config_post_init_format_validation(self):
        """Test that __post_init__ validates and cleans export formats."""
        config = PublishConfig(export_formats=["pdf", "dxf", "invalid"])
        self.assertEqual(config.export_formats, ["PDF", "DXF"])
        
        config = PublishConfig(export_formats=[])
        self.assertEqual(config.export_formats, ["PDF"])
    
    def test_validate_valid_config(self):
        """Test validation with valid configuration."""
        config = PublishConfig(series_count=5, export_formats=["PDF"])
        result = config.validate()
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
    
    def test_validate_invalid_series_count(self):
        """Test validation with invalid series count."""
        config = PublishConfig(series_count=0)
        result = config.validate()
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
        self.assertIn("at least 1", result.errors[0])
    
    def test_validate_large_series_count_warning(self):
        """Test validation with large series count generates warning."""
        config = PublishConfig(series_count=101)
        result = config.validate()
        self.assertTrue(result.is_valid)  # Should still be valid
        self.assertGreater(len(result.warnings), 0)
        self.assertIn("significant time", result.warnings[0])
    
    def test_validate_empty_export_formats(self):
        """Test validation with empty export formats."""
        config = PublishConfig()
        config.export_formats = []  # Bypass __post_init__
        result = config.validate()
        self.assertFalse(result.is_valid)
        self.assertIn("At least one export format", result.errors[0])
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        config = PublishConfig(create_manual=True, series_count=3)
        config_dict = config.to_dict()
        self.assertIsInstance(config_dict, dict)
        self.assertTrue(config_dict["create_manual"])
        self.assertEqual(config_dict["series_count"], 3)
    
    def test_from_dict(self):
        """Test creation from dictionary."""
        data = {
            "create_manual": True,
            "fill_series": True,
            "series_count": 10,
            "export_formats": ["PDF", "DXF"]
        }
        config = PublishConfig.from_dict(data)
        self.assertTrue(config.create_manual)
        self.assertTrue(config.fill_series)
        self.assertEqual(config.series_count, 10)
        self.assertEqual(config.export_formats, ["PDF", "DXF"])


class TestModelData(unittest.TestCase):
    """Test cases for ModelData class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_template = None
        self.temp_dir = None
    
    def tearDown(self):
        """Clean up test fixtures."""
        if self.temp_template and os.path.exists(self.temp_template):
            os.unlink(self.temp_template)
        if self.temp_dir and os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)
    
    def test_model_data_creation(self):
        """Test ModelData creation."""
        model = ModelData(
            template_path="C:\\test\\template.dotx",
            drawings_path="C:\\test\\drawings",
            asme_flag=True,
            controls_parent="12345"
        )
        self.assertEqual(model.template_path, "C:\\test\\template.dotx")
        self.assertEqual(model.drawings_path, "C:\\test\\drawings")
        self.assertTrue(model.asme_flag)
        self.assertEqual(model.controls_parent, "12345")
    
    def test_model_data_post_init_strips_whitespace(self):
        """Test that __post_init__ strips whitespace."""
        model = ModelData(
            template_path="  C:\\test\\template.dotx  ",
            drawings_path="  C:\\test\\drawings  ",
            controls_parent="  12345  "
        )
        self.assertEqual(model.template_path, "C:\\test\\template.dotx")
        self.assertEqual(model.drawings_path, "C:\\test\\drawings")
        self.assertEqual(model.controls_parent, "12345")
    
    def test_validate_valid_model(self):
        """Test validation with valid model data."""
        # Create temporary files for testing
        with tempfile.NamedTemporaryFile(suffix='.dotx', delete=False) as temp_file:
            self.temp_template = temp_file.name
        
        self.temp_dir = tempfile.mkdtemp()
        
        model = ModelData(
            template_path=self.temp_template,
            drawings_path=self.temp_dir,
            controls_parent="12345"
        )
        result = model.validate()
        self.assertTrue(result.is_valid)
    
    def test_validate_missing_template(self):
        """Test validation with missing template path."""
        model = ModelData(controls_parent="12345")
        result = model.validate()
        self.assertFalse(result.is_valid)
        self.assertIn("Template path is required", result.errors[0])
    
    def test_validate_nonexistent_template(self):
        """Test validation with nonexistent template file."""
        model = ModelData(
            template_path="C:\\nonexistent\\template.dotx",
            controls_parent="12345"
        )
        result = model.validate()
        self.assertFalse(result.is_valid)
        self.assertIn("does not exist", result.errors[0])
    
    def test_validate_missing_controls_parent(self):
        """Test validation with missing controls parent."""
        with tempfile.NamedTemporaryFile(suffix='.dotx', delete=False) as temp_file:
            self.temp_template = temp_file.name
        
        model = ModelData(template_path=self.temp_template)
        result = model.validate()
        self.assertFalse(result.is_valid)
        self.assertIn("Controls parent number is required", result.errors[0])
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        model = ModelData(
            template_path="C:\\test\\template.dotx",
            asme_flag=True,
            controls_parent="12345"
        )
        model_dict = model.to_dict()
        self.assertIsInstance(model_dict, dict)
        self.assertEqual(model_dict["template_path"], "C:\\test\\template.dotx")
        self.assertTrue(model_dict["asme_flag"])
        self.assertEqual(model_dict["controls_parent"], "12345")
    
    def test_from_dict(self):
        """Test creation from dictionary."""
        data = {
            "template_path": "C:\\test\\template.dotx",
            "drawings_path": "C:\\test\\drawings",
            "asme_flag": True,
            "controls_parent": "12345"
        }
        model = ModelData.from_dict(data)
        self.assertEqual(model.template_path, "C:\\test\\template.dotx")
        self.assertEqual(model.drawings_path, "C:\\test\\drawings")
        self.assertTrue(model.asme_flag)
        self.assertEqual(model.controls_parent, "12345")
    
    def test_has_manual_template(self):
        """Test manual template detection."""
        with tempfile.NamedTemporaryFile(suffix='.dotx', delete=False) as temp_file:
            self.temp_template = temp_file.name
        
        model = ModelData(template_path=self.temp_template)
        self.assertTrue(model.has_manual_template())
        
        model = ModelData(template_path="C:\\nonexistent\\template.dotx")
        self.assertFalse(model.has_manual_template())
    
    def test_has_drawings(self):
        """Test drawings path detection."""
        self.temp_dir = tempfile.mkdtemp()
        
        model = ModelData(drawings_path=self.temp_dir)
        self.assertTrue(model.has_drawings())
        
        model = ModelData(drawings_path="C:\\nonexistent\\drawings")
        self.assertFalse(model.has_drawings())


class TestModelsConfiguration(unittest.TestCase):
    """Test cases for ModelsConfiguration class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_models_data = {
            "Chiller": {
                "P408": {
                    "template_path": "C:\\templates\\chiller.dotx",
                    "drawings_path": "C:\\drawings\\P408",
                    "asme_flag": False,
                    "controls_parent": "503390"
                },
                "P410": {
                    "template_path": "C:\\templates\\chiller.dotx",
                    "drawings_path": "C:\\drawings\\P410",
                    "asme_flag": False,
                    "controls_parent": "503390"
                }
            },
            "Boiler": {
                "B100": {
                    "template_path": "C:\\templates\\boiler.dotx",
                    "drawings_path": "C:\\drawings\\B100",
                    "asme_flag": True,
                    "controls_parent": "600123"
                }
            }
        }
    
    def test_models_configuration_creation(self):
        """Test ModelsConfiguration creation."""
        config = ModelsConfiguration()
        self.assertIsInstance(config.models, dict)
        self.assertEqual(len(config.models), 0)
    
    def test_from_dict(self):
        """Test creation from dictionary."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        self.assertEqual(len(config.models), 2)
        self.assertIn("Chiller", config.models)
        self.assertIn("Boiler", config.models)
        
        # Check that model data was converted to ModelData objects
        p408_model = config.models["Chiller"]["P408"]
        self.assertIsInstance(p408_model, ModelData)
        self.assertEqual(p408_model.controls_parent, "503390")
    
    def test_get_model(self):
        """Test getting model by name."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        
        model = config.get_model("P408")
        self.assertIsNotNone(model)
        self.assertEqual(model.controls_parent, "503390")
        
        model = config.get_model("NonExistent")
        self.assertIsNone(model)
    
    def test_get_models_for_gss(self):
        """Test getting models by GSS parent number."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        
        models = config.get_models_for_gss("503390")
        self.assertEqual(len(models), 2)
        self.assertIn("P408", models)
        self.assertIn("P410", models)
        
        models = config.get_models_for_gss("600123")
        self.assertEqual(len(models), 1)
        self.assertIn("B100", models)
        
        models = config.get_models_for_gss("999999")
        self.assertEqual(len(models), 0)
    
    def test_get_all_models(self):
        """Test getting all model names."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        
        all_models = config.get_all_models()
        self.assertEqual(len(all_models), 3)
        self.assertIn("P408", all_models)
        self.assertIn("P410", all_models)
        self.assertIn("B100", all_models)
    
    def test_get_categories(self):
        """Test getting all category names."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        
        categories = config.get_categories()
        self.assertEqual(len(categories), 2)
        self.assertIn("Boiler", categories)
        self.assertIn("Chiller", categories)
    
    def test_validate_all(self):
        """Test validation of all models."""
        config = ModelsConfiguration.from_dict(self.sample_models_data)
        
        result = config.validate_all()
        # Since the test data has non-existent paths, there should be errors
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)


if __name__ == '__main__':
    unittest.main()