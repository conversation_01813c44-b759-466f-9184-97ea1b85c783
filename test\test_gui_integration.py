"""
Integration tests for GUI components.

This module contains basic integration tests to verify the GUI components
can be imported and instantiated without errors.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestGUIIntegration(unittest.TestCase):
    """Integration tests for GUI components."""
    
    def test_imports(self):
        """Test that all GUI modules can be imported."""
        try:
            # Test importing main window
            from apps.publish.gui.main_window import MainWindow
            self.assertTrue(True, "MainWindow imported successfully")
            
            # Test importing widgets
            from apps.publish.gui.widgets import ModelDropdown, SeriesControls, FormField
            self.assertTrue(True, "Widgets imported successfully")
            
            # Test importing dialogs
            from apps.publish.gui.dialogs import ErrorDialog, ConfirmationDialog
            self.assertTrue(True, "Dialogs imported successfully")
            
        except ImportError as e:
            # Expected to fail due to customtkinter dependency
            self.assertIn("customtkinter", str(e).lower())
            
    def test_class_definitions(self):
        """Test that classes are properly defined."""
        try:
            from apps.publish.gui.main_window import MainWindow
            from apps.publish.gui.widgets import ModelDropdown, SeriesControls, FormField
            from apps.publish.gui.dialogs import ErrorDialog, ConfirmationDialog
            
            # Check that classes exist and have expected methods
            self.assertTrue(hasattr(MainWindow, 'setup_ui'))
            self.assertTrue(hasattr(MainWindow, 'on_publish_clicked'))
            self.assertTrue(hasattr(ModelDropdown, 'update_models'))
            self.assertTrue(hasattr(SeriesControls, 'get_create_manual'))
            self.assertTrue(hasattr(ErrorDialog, 'show'))
            self.assertTrue(hasattr(ConfirmationDialog, 'show'))
            
        except ImportError:
            # Skip if customtkinter not available
            self.skipTest("CustomTkinter not available")
            
    def test_module_structure(self):
        """Test that the GUI module structure is correct."""
        # Check that GUI package exists
        gui_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'apps', 'publish', 'gui'
        )
        self.assertTrue(os.path.exists(gui_path))
        
        # Check that required files exist
        required_files = [
            '__init__.py',
            'main_window.py',
            'widgets.py',
            'dialogs.py'
        ]
        
        for filename in required_files:
            file_path = os.path.join(gui_path, filename)
            self.assertTrue(os.path.exists(file_path), f"{filename} should exist")
            
    def test_requirements_compliance(self):
        """Test that the implementation meets the requirements."""
        try:
            from apps.publish.gui.main_window import MainWindow
            from apps.publish.gui.widgets import ModelDropdown, SeriesControls
            from apps.publish.gui.dialogs import ErrorDialog, ConfirmationDialog
            
            # Requirement 2.1: GUI logic separated from business logic
            # MainWindow should accept service container for dependency injection
            import inspect
            main_window_init = inspect.signature(MainWindow.__init__)
            params = list(main_window_init.parameters.keys())
            self.assertIn('container', params)
            
            # Verify that MainWindow gets services from container (better design)
            # This shows proper separation of concerns
            
            # Requirement 2.2: Widgets should be decoupled from business logic
            # Widgets should not directly import business services
            
            # Check ModelDropdown source
            model_dropdown_source = inspect.getsource(ModelDropdown)
            self.assertNotIn('PublishService', model_dropdown_source)
            self.assertNotIn('ModelService', model_dropdown_source)
            
            # Check SeriesControls source
            series_controls_source = inspect.getsource(SeriesControls)
            self.assertNotIn('PublishService', series_controls_source)
            
            # Requirement 4.3: Reusable components
            # Widgets should be reusable
            self.assertTrue(callable(ModelDropdown))
            self.assertTrue(callable(SeriesControls))
            
            # Requirement 3.4: Consistent user experience
            # Dialogs should provide consistent interface
            self.assertTrue(hasattr(ErrorDialog, 'show'))
            self.assertTrue(hasattr(ConfirmationDialog, 'show'))
            
        except ImportError:
            self.skipTest("CustomTkinter not available")


if __name__ == '__main__':
    unittest.main()