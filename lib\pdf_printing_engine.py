"""
PDF Printing Engine Module

This module provides functions for printing PDF files, including:
- Multiple printing methods (Windows API, SumatraPDF, Adobe Reader, etc.)
- Printer profile management
- Printer settings configuration
"""

import os
import sys
import logging
import subprocess
import tempfile
import time
from typing import Dict, List, Tuple, Optional, Any
import win32print
import win32api
import win32con
from PyPDF2 import PdfReader

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER

# Constants for printer settings
ORIENTATION_PORTRAIT = 1
ORIENTATION_LANDSCAPE = 2

DUPLEX_SIMPLEX = 1  # Single-sided
DUPLEX_VERTICAL = 2  # Double-sided, flip on long edge
DUPLEX_HORIZONTAL = 3  # Double-sided, flip on short edge

COLOR_MONOCHROME = 1
COLOR_COLOR = 2

# Paper sizes (in tenths of a millimeter)
PAPER_LETTER = 1  # Letter 8.5 x 11 in
PAPER_TABLOID = 3  # Tabloid 11 x 17 in

class PrinterProfile:
    """Class representing a printer profile with specific settings."""

    def __init__(self, name: str, printer_name: str, paper_size: int, orientation: int,
                 duplex: int, color: int):
        """
        Initialize a printer profile.

        Args:
            name: The name of the profile
            printer_name: The name of the printer
            paper_size: The paper size (PAPER_LETTER, PAPER_TABLOID, etc.)
            orientation: The orientation (ORIENTATION_PORTRAIT, ORIENTATION_LANDSCAPE)
            duplex: The duplex mode (DUPLEX_SIMPLEX, DUPLEX_VERTICAL, DUPLEX_HORIZONTAL)
            color: The color mode (COLOR_MONOCHROME, COLOR_COLOR)
        """
        self.name = name
        self.printer_name = printer_name
        self.paper_size = paper_size
        self.orientation = orientation
        self.duplex = duplex
        self.color = color

    def to_dict(self) -> Dict[str, Any]:
        """Convert the profile to a dictionary for serialization."""
        return {
            "name": self.name,
            "printer_name": self.printer_name,
            "paper_size": self.paper_size,
            "orientation": self.orientation,
            "duplex": self.duplex,
            "color": self.color
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PrinterProfile':
        """Create a profile from a dictionary."""
        return cls(
            name=data["name"],
            printer_name=data["printer_name"],
            paper_size=data["paper_size"],
            orientation=data["orientation"],
            duplex=data["duplex"],
            color=data["color"]
        )

def get_installed_printers() -> List[str]:
    """
    Get a list of installed printers.

    Returns:
        List[str]: A list of printer names
    """
    printers = []
    for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS):
        printers.append(printer[2])
    return printers

def get_default_printer() -> str:
    """
    Get the name of the default printer.

    Returns:
        str: The name of the default printer
    """
    return win32print.GetDefaultPrinter()

def create_default_profiles(printer_name: str = None) -> Dict[str, PrinterProfile]:
    """
    Create default printer profiles for different page types.

    Args:
        printer_name: The name of the printer to use (defaults to the default printer)

    Returns:
        Dict[str, PrinterProfile]: A dictionary of printer profiles
    """
    if printer_name is None:
        printer_name = get_default_printer()

    # Log the default printer
    logging.info(f"Creating default profiles with printer: {printer_name}")

    # Create profiles with distinct settings for each page type
    profiles = {
        PAGE_TYPE_LETTER: PrinterProfile(
            name="Letter Profile",
            printer_name=printer_name,
            paper_size=PAPER_LETTER,          # Letter size
            orientation=ORIENTATION_PORTRAIT,  # Portrait orientation
            duplex=DUPLEX_VERTICAL,           # Double-sided (long edge)
            color=COLOR_COLOR                 # Color
        ),
        PAGE_TYPE_TABLOID: PrinterProfile(
            name="Tabloid Profile",
            printer_name=printer_name,
            paper_size=PAPER_TABLOID,          # Tabloid size
            orientation=ORIENTATION_LANDSCAPE,  # Landscape orientation
            duplex=DUPLEX_SIMPLEX,             # Single-sided
            color=COLOR_COLOR                  # Color
        ),
        PAGE_TYPE_OTHER: PrinterProfile(
            name="Other Profile",
            printer_name=printer_name,
            paper_size=PAPER_LETTER,           # Letter size
            orientation=ORIENTATION_PORTRAIT,   # Portrait orientation
            duplex=DUPLEX_SIMPLEX,             # Single-sided
            color=COLOR_COLOR                  # Color
        )
    }

    # Log the created profiles
    for page_type, profile in profiles.items():
        logging.info(f"Default profile for {page_type}:")
        logging.info(f"  - Paper size: {profile.paper_size} (1=Letter, 3=Tabloid)")
        logging.info(f"  - Orientation: {profile.orientation} (1=Portrait, 2=Landscape)")
        logging.info(f"  - Duplex: {profile.duplex} (1=Simplex, 2=Duplex)")
        logging.info(f"  - Color: {profile.color} (1=Monochrome, 2=Color)")

    return profiles

def print_pdf_with_windows_api(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using the Windows API.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # This is a placeholder for Windows API printing
        # In a real implementation, this would use the Windows API to print the PDF
        # with the specified settings
        logging.info(f"Printing {pdf_path} with Windows API using profile {profile.name}")

        # For now, we'll use ShellExecute as a fallback
        win32api.ShellExecute(
            0,
            "print",
            pdf_path,
            f'/d:"{profile.printer_name}"',
            ".",
            0
        )

        # Wait for the print job to be processed
        time.sleep(2)

        return True
    except Exception as e:
        logging.error(f"Error printing with Windows API: {e}")
        return False

def print_pdf_with_sumatra(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using SumatraPDF.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from lib.pdf_printing_utils import ensure_sumatra_pdf

        # Ensure SumatraPDF is installed
        sumatra_path = ensure_sumatra_pdf()
        if not sumatra_path:
            logging.warning("SumatraPDF not found and could not be installed")
            return False

        # Map paper size to SumatraPDF paper size name
        paper_size_name = "letter"
        if profile.paper_size == PAPER_LETTER:
            paper_size_name = "letter"
        elif profile.paper_size == PAPER_TABLOID:
            paper_size_name = "tabloid"

        # Map orientation to SumatraPDF orientation name
        orientation_name = "portrait"
        if profile.orientation == ORIENTATION_PORTRAIT:
            orientation_name = "portrait"
        elif profile.orientation == ORIENTATION_LANDSCAPE:
            orientation_name = "landscape"

        # Build the print settings string
        print_settings = f"paper={paper_size_name},orientation={orientation_name},duplex={profile.duplex},color={profile.color}"
        logging.info(f"SumatraPDF print settings: {print_settings}")

        # Build the command
        cmd = [
            sumatra_path,
            "-print-to", profile.printer_name,
            "-print-settings", print_settings,
            pdf_path
        ]

        # Log the full command
        logging.info(f"SumatraPDF command: {' '.join(cmd)}")

        # Execute the command
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        # Log the output
        if result.stdout:
            logging.info(f"SumatraPDF output: {result.stdout}")
        if result.stderr:
            logging.warning(f"SumatraPDF error: {result.stderr}")

        # Wait for the print job to be processed
        time.sleep(2)

        return True
    except Exception as e:
        logging.error(f"Error printing with SumatraPDF: {e}")
        return False

def print_pdf_with_adobe(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using Adobe Reader.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if Adobe Reader is installed
        adobe_path = r"C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe"
        if not os.path.exists(adobe_path):
            adobe_path = r"C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe"
            if not os.path.exists(adobe_path):
                logging.warning("Adobe Reader not found")
                return False

        # Build the command
        cmd = [
            adobe_path,
            "/t", pdf_path, profile.printer_name
        ]

        # Execute the command
        subprocess.run(cmd, check=True)

        # Wait for the print job to be processed
        time.sleep(2)

        return True
    except Exception as e:
        logging.error(f"Error printing with Adobe Reader: {e}")
        return False

def print_pdf_with_ghostscript(pdf_path: str, profile: PrinterProfile) -> bool:
    """
    Print a PDF file using Ghostscript.

    Args:
        pdf_path: The path to the PDF file
        profile: The printer profile to use

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if Ghostscript is installed
        gs_path = r"C:\Program Files\gs\gs9.56.1\bin\gswin64c.exe"
        if not os.path.exists(gs_path):
            gs_path = r"C:\Program Files (x86)\gs\gs9.56.1\bin\gswin32c.exe"
            if not os.path.exists(gs_path):
                logging.warning("Ghostscript not found")
                return False

        # Build the command
        cmd = [
            gs_path,
            "-dNOPAUSE",
            "-dBATCH",
            "-dPrinted",
            f"-sDEVICE=mswinpr2",
            f"-sOutputFile=%printer%{profile.printer_name}",
            pdf_path
        ]

        # Execute the command
        subprocess.run(cmd, check=True)

        # Wait for the print job to be processed
        time.sleep(2)

        return True
    except Exception as e:
        logging.error(f"Error printing with Ghostscript: {e}")
        return False

def print_pdf_with_default_viewer(pdf_path: str) -> bool:
    """
    Print a PDF file using the default PDF viewer.

    Args:
        pdf_path: The path to the PDF file

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        win32api.ShellExecute(
            0,
            "print",
            pdf_path,
            None,
            ".",
            0
        )

        # Wait for the print job to be processed
        time.sleep(2)

        return True
    except Exception as e:
        logging.error(f"Error printing with default viewer: {e}")
        return False

def print_pdf_section(section: PDFSection, profiles: Dict[str, PrinterProfile]) -> bool:
    """
    Print a PDF section using the appropriate profile.

    Args:
        section: The PDF section to print
        profiles: A dictionary of printer profiles

    Returns:
        bool: True if successful, False otherwise
    """
    if not section.temp_file_path or not os.path.exists(section.temp_file_path):
        logging.error(f"Temporary file for section {section} not found")
        return False

    # Get the appropriate profile for this section
    profile = profiles.get(section.page_type)
    if not profile:
        logging.error(f"No profile found for page type {section.page_type}")
        return False

    # Log the profile settings being used
    logging.info(f"Printing section {section} with profile settings:")
    logging.info(f"  - Printer: {profile.printer_name}")
    logging.info(f"  - Paper size: {profile.paper_size} (1=Letter, 3=Tabloid)")
    logging.info(f"  - Orientation: {profile.orientation} (1=Portrait, 2=Landscape)")
    logging.info(f"  - Duplex: {profile.duplex} (1=Simplex, 2=Duplex)")
    logging.info(f"  - Color: {profile.color} (1=Monochrome, 2=Color)")

    # Import the direct Windows API printing method
    try:
        from lib.pdf_printing_win32 import print_pdf_with_direct_api
        has_direct_api = True
    except ImportError:
        has_direct_api = False
        logging.warning("Direct Windows API printing not available")

    # Import the PDFtoPrinter method
    try:
        from lib.pdf_printing_external import print_pdf_with_pdftopdf
        has_pdftopdf = True
    except ImportError:
        has_pdftopdf = False
        logging.warning("PDFtoPrinter not available")

    # Try each printing method in order of preference
    methods = []

    # Add PDFtoPrinter method if available
    if has_pdftopdf:
        methods.append(("PDFtoPrinter", lambda: print_pdf_with_pdftopdf(section.temp_file_path, profile)))

    # Add Direct Windows API method if available
    if has_direct_api:
        methods.append(("Direct Windows API", lambda: print_pdf_with_direct_api(section.temp_file_path, profile)))

    # Add other methods
    methods.extend([
        ("SumatraPDF", lambda: print_pdf_with_sumatra(section.temp_file_path, profile)),
        ("Windows API", lambda: print_pdf_with_windows_api(section.temp_file_path, profile)),
        ("Adobe Reader", lambda: print_pdf_with_adobe(section.temp_file_path, profile)),
        ("Ghostscript", lambda: print_pdf_with_ghostscript(section.temp_file_path, profile)),
        ("Default Viewer", lambda: print_pdf_with_default_viewer(section.temp_file_path))
    ])

    for method_name, method_func in methods:
        logging.info(f"Attempting to print with {method_name}")
        if method_func():
            logging.info(f"Successfully printed section {section} with {method_name}")
            return True

    logging.error(f"All printing methods failed for section {section}")
    return False

def print_pdf_sections(sections: List[PDFSection], profiles: Dict[str, PrinterProfile]) -> bool:
    """
    Print all PDF sections using the appropriate profiles.

    Args:
        sections: A list of PDF sections to print
        profiles: A dictionary of printer profiles

    Returns:
        bool: True if all sections were printed successfully, False otherwise
    """
    # Try the VBScript printing approach first
    try:
        from lib.pdf_printing_vbs import print_section_with_vbscript

        # Get the base printer name (use the first profile's printer name)
        base_printer = next(iter(profiles.values())).printer_name
        logging.info(f"Using base printer: {base_printer}")

        # Print each section with VBScript
        success = True
        for section in sections:
            logging.info(f"Printing section {section} with VBScript")
            if not print_section_with_vbscript(section, base_printer):
                success = False

        return success
    except Exception as e:
        logging.error(f"Error using VBScript printing: {e}")
        logging.info("Falling back to standard printing method")

        # Fall back to the standard approach
        success = True
        for section in sections:
            if not print_pdf_section(section, profiles):
                success = False

        return success
