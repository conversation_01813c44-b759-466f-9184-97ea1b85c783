"""
Services Module - Business Logic Layer.

This module contains all service classes that implement the core business logic
for the publishing system. Services orchestrate operations between different
components and provide a clean interface for the GUI layer.

The services layer follows the Service Layer pattern, where each service class
encapsulates related business operations and maintains no state between calls.
All services are designed to be easily testable through dependency injection.

Available Services:
    - **PublishService**: Core publishing workflow orchestration
    - **ModelService**: Model data management and lookup operations
    - **ExportService**: PDF and DXF export operations
    - **ManualService**: Manual creation and processing

Design Principles:
    - Single Responsibility: Each service has a focused purpose
    - Dependency Injection: Services receive dependencies through constructor
    - Stateless Operations: Services don't maintain state between calls
    - Error Handling: Comprehensive error handling with custom exceptions
    - Logging: Detailed logging for debugging and monitoring

Usage Example:
    ```python
    from apps.publish.container import ServiceContainer
    
    # Get services through dependency injection
    container = ServiceContainer()
    publish_service = container.get_service(PublishService)
    model_service = container.get_service(ModelService)
    
    # Use services
    models = model_service.get_models_for_gss("GSS123")
    result = publish_service.publish_project(project_data, config)
    ```

Service Dependencies:
    Services may depend on:
    - Integration clients (E3Client, ReportGeneratorClient)
    - Other services (composition pattern)
    - Configuration managers
    - Utility classes

Error Handling:
    All services use the custom exception hierarchy:
    - ServiceError: General service operation failures
    - ValidationError: Input validation failures
    - IntegrationError: External system communication failures

Testing:
    Services are designed for easy unit testing:
    - Constructor injection allows mocking dependencies
    - Stateless design simplifies test setup
    - Clear interfaces enable behavior verification

Author: E3 Automation Team
"""

from .publish_service import PublishService, PublishConfig, PublishResult
from .model_service import ModelService
from .export_service import ExportService
from .manual_service import ManualService

__all__ = [
    'PublishService',
    'PublishConfig', 
    'PublishResult',
    'ModelService',
    'ExportService',
    'ManualService',
]