"""
Config Module - Configuration and Logging Management.

This module provides comprehensive configuration management and logging setup
for the publishing system. It handles loading, validation, and management of
application settings, as well as centralized logging configuration.

The configuration system supports:
- Multiple configuration sources (files, environment variables, defaults)
- Configuration validation and error reporting
- Hot-reloading of configuration changes
- Environment-specific configurations
- Secure handling of sensitive configuration data

Available Components:
    - **ConfigurationManager**: Central configuration management
    - **LoggingConfig**: Centralized logging setup and management
    - **Configuration Exceptions**: Specific error types for configuration issues

Design Principles:
    - Centralized Management: Single source of truth for configuration
    - Validation: Comprehensive validation with clear error messages
    - Security: Secure handling of sensitive configuration data
    - Flexibility: Support for multiple configuration sources and formats
    - Performance: Efficient loading and caching of configuration data
    - Monitoring: Logging of configuration changes and issues

Usage Example:
    ```python
    from apps.publish.config import ConfigurationManager, setup_publish_logging
    
    # Setup logging first
    setup_publish_logging()
    
    # Load configuration
    config_manager = ConfigurationManager()
    app_config = config_manager.get_app_config()
    models_config = config_manager.get_models_config()
    
    # Access configuration values
    output_path = app_config.get('output_base_path', '/default/path')
    timeout = app_config.get('timeout_seconds', 300)
    ```

Configuration Sources:
    1. **Default Configuration**: Built-in defaults for all settings
    2. **Configuration Files**: JSON/YAML configuration files
    3. **Environment Variables**: Override settings via environment
    4. **Command Line Arguments**: Runtime configuration overrides
    5. **User Preferences**: Persistent user-specific settings

Configuration Structure:
    ```json
    {
        "application": {
            "name": "E3 Publishing System",
            "version": "3.0.0",
            "timeout_seconds": 300
        },
        "paths": {
            "output_base_path": "/default/output",
            "templates_path": "/templates",
            "logs_path": "/logs"
        },
        "e3_integration": {
            "connection_timeout": 30,
            "retry_attempts": 3,
            "auto_connect": true
        },
        "export": {
            "default_formats": ["pdf", "dxf"],
            "pdf_quality": "high",
            "dxf_version": "2018"
        }
    }
    ```

Logging Configuration:
    - Structured logging with JSON format
    - Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    - File rotation and archiving
    - Console and file output
    - Performance monitoring
    - Error tracking and alerting

Configuration Validation:
    - Schema validation for configuration files
    - Type checking and format validation
    - Required field validation
    - Range and constraint validation
    - Cross-field dependency validation

Security Features:
    - Secure storage of sensitive configuration
    - Environment variable masking in logs
    - Configuration file permission validation
    - Encryption of sensitive configuration data
    - Audit logging of configuration changes

Error Handling:
    Configuration-specific exceptions:
    - ConfigurationError: General configuration issues
    - ConfigurationValidationError: Validation failures
    - ConfigurationLoadError: Loading/parsing failures

Hot-Reloading:
    - File system monitoring for configuration changes
    - Automatic reloading of changed configurations
    - Validation of new configurations before applying
    - Rollback capability for invalid configurations
    - Event notifications for configuration changes

Environment Support:
    - Development, testing, and production environments
    - Environment-specific configuration overrides
    - Feature flags and toggles
    - A/B testing configuration support
    - Deployment-specific settings

Performance:
    - Lazy loading of configuration sections
    - Caching of frequently accessed values
    - Efficient configuration lookup
    - Minimal memory footprint
    - Fast startup times

Author: E3 Automation Team
"""

from .configuration_manager import ConfigurationManager
from .exceptions import (
    ConfigurationError, 
    ConfigurationValidationError, 
    ConfigurationLoadError
)
from .logging_config import (
    PublishLoggingConfig,
    setup_publish_logging,
    get_publish_logger,
    log_operation_start,
    log_operation_success,
    log_operation_error
)

__all__ = [
    # Configuration management
    'ConfigurationManager',
    'ConfigurationError',
    'ConfigurationValidationError',
    'ConfigurationLoadError',
    
    # Logging configuration
    'PublishLoggingConfig',
    'setup_publish_logging',
    'get_publish_logger',
    'log_operation_start',
    'log_operation_success',
    'log_operation_error'
]