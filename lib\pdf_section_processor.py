"""
PDF Section Processor Module

This module provides functions for processing PDF files, including:
- Detecting page sizes
- Grouping pages by type
- Extracting sections to temporary files
"""

import os
import logging
import tempfile
from typing import Dict, List, Tuple, Optional, Any
import fitz  # PyMuPDF
from PyPDF2 import PdfReader, PdfWriter

# Constants for page sizes (in points)
# 1 inch = 72 points
LETTER_WIDTH = 612  # 8.5 inches
LETTER_HEIGHT = 792  # 11 inches
TABLOID_WIDTH = 792  # 11 inches
TABLOID_HEIGHT = 1224  # 17 inches

# Tolerance for page size detection (in points)
SIZE_TOLERANCE = 5

# Page types
PAGE_TYPE_LETTER = "Letter"
PAGE_TYPE_TABLOID = "Tabloid"
PAGE_TYPE_OTHER = "Other"

class PDFSection:
    """Class representing a section of a PDF with pages of the same type."""
    
    def __init__(self, page_type: str, start_page: int, end_page: int = None):
        """
        Initialize a PDF section.
        
        Args:
            page_type: The type of pages in this section (Letter, Tabloid, Other)
            start_page: The index of the first page in this section
            end_page: The index of the last page in this section (defaults to start_page)
        """
        self.page_type = page_type
        self.start_page = start_page
        self.end_page = end_page if end_page is not None else start_page
        self.temp_file_path = None
    
    def __str__(self) -> str:
        """Return a string representation of the section."""
        page_range = f"{self.start_page + 1}"
        if self.start_page != self.end_page:
            page_range += f"-{self.end_page + 1}"
        return f"{self.page_type} ({page_range})"
    
    def get_page_count(self) -> int:
        """Return the number of pages in this section."""
        return self.end_page - self.start_page + 1

def detect_page_type(page_width: float, page_height: float) -> str:
    """
    Detect the type of a page based on its dimensions.
    
    Args:
        page_width: The width of the page in points
        page_height: The height of the page in points
        
    Returns:
        str: The page type (Letter, Tabloid, or Other)
    """
    # Check if the page is Letter size (in either orientation)
    if (abs(page_width - LETTER_WIDTH) <= SIZE_TOLERANCE and 
        abs(page_height - LETTER_HEIGHT) <= SIZE_TOLERANCE) or \
       (abs(page_width - LETTER_HEIGHT) <= SIZE_TOLERANCE and 
        abs(page_height - LETTER_WIDTH) <= SIZE_TOLERANCE):
        return PAGE_TYPE_LETTER
    
    # Check if the page is Tabloid size (in either orientation)
    if (abs(page_width - TABLOID_WIDTH) <= SIZE_TOLERANCE and 
        abs(page_height - TABLOID_HEIGHT) <= SIZE_TOLERANCE) or \
       (abs(page_width - TABLOID_HEIGHT) <= SIZE_TOLERANCE and 
        abs(page_height - TABLOID_WIDTH) <= SIZE_TOLERANCE):
        return PAGE_TYPE_TABLOID
    
    # If not Letter or Tabloid, return Other
    return PAGE_TYPE_OTHER

def get_page_dimensions(pdf_path: str) -> List[Tuple[float, float]]:
    """
    Get the dimensions of all pages in a PDF file.
    
    Args:
        pdf_path: The path to the PDF file
        
    Returns:
        List[Tuple[float, float]]: A list of (width, height) tuples for each page
    """
    try:
        doc = fitz.open(pdf_path)
        dimensions = []
        
        for page in doc:
            rect = page.rect
            dimensions.append((rect.width, rect.height))
        
        doc.close()
        return dimensions
    except Exception as e:
        logging.error(f"Error getting page dimensions: {e}")
        raise

def identify_page_types(pdf_path: str) -> List[str]:
    """
    Identify the type of each page in a PDF file.
    
    Args:
        pdf_path: The path to the PDF file
        
    Returns:
        List[str]: A list of page types for each page
    """
    dimensions = get_page_dimensions(pdf_path)
    return [detect_page_type(width, height) for width, height in dimensions]

def group_pages_into_sections(page_types: List[str]) -> List[PDFSection]:
    """
    Group consecutive pages of the same type into sections.
    
    Args:
        page_types: A list of page types for each page
        
    Returns:
        List[PDFSection]: A list of PDF sections
    """
    if not page_types:
        return []
    
    sections = []
    current_section = PDFSection(page_types[0], 0)
    
    for i in range(1, len(page_types)):
        if page_types[i] == current_section.page_type:
            # Same type as current section, extend the section
            current_section.end_page = i
        else:
            # Different type, start a new section
            sections.append(current_section)
            current_section = PDFSection(page_types[i], i)
    
    # Add the last section
    sections.append(current_section)
    
    return sections

def extract_section_to_temp_file(pdf_path: str, section: PDFSection) -> str:
    """
    Extract a section of a PDF to a temporary file.
    
    Args:
        pdf_path: The path to the PDF file
        section: The PDF section to extract
        
    Returns:
        str: The path to the temporary file
    """
    try:
        # Create a temporary file
        fd, temp_path = tempfile.mkstemp(suffix='.pdf')
        os.close(fd)
        
        # Extract the section
        reader = PdfReader(pdf_path)
        writer = PdfWriter()
        
        for i in range(section.start_page, section.end_page + 1):
            writer.add_page(reader.pages[i])
        
        with open(temp_path, 'wb') as f:
            writer.write(f)
        
        # Store the temp file path in the section
        section.temp_file_path = temp_path
        
        return temp_path
    except Exception as e:
        logging.error(f"Error extracting section: {e}")
        raise

def process_pdf(pdf_path: str) -> List[PDFSection]:
    """
    Process a PDF file, identifying page types and grouping them into sections.
    
    Args:
        pdf_path: The path to the PDF file
        
    Returns:
        List[PDFSection]: A list of PDF sections
    """
    try:
        # Identify page types
        page_types = identify_page_types(pdf_path)
        
        # Group pages into sections
        sections = group_pages_into_sections(page_types)
        
        # Extract each section to a temporary file
        for section in sections:
            extract_section_to_temp_file(pdf_path, section)
        
        return sections
    except Exception as e:
        logging.error(f"Error processing PDF: {e}")
        raise

def cleanup_temp_files(sections: List[PDFSection]) -> None:
    """
    Clean up temporary files created for PDF sections.
    
    Args:
        sections: A list of PDF sections
    """
    for section in sections:
        if section.temp_file_path and os.path.exists(section.temp_file_path):
            try:
                os.remove(section.temp_file_path)
            except Exception as e:
                logging.warning(f"Error removing temporary file {section.temp_file_path}: {e}")
