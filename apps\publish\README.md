# Publish Package Documentation

## Overview

The `publish` package is a refactored, modular publishing system for E3 Series CAD projects. It provides a clean separation of concerns between GUI, business logic, data models, and external integrations.

## Architecture

The package follows a layered architecture pattern:

```
┌─────────────────────────────────────────┐
│              Presentation Layer          │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │   GUI Module    │ │  CLI Interface  │ │
│  │  (gui/)         │ │   (optional)    │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             Business Layer              │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ Publish Service │ │ Model Service   │ │
│  │  (services/)    │ │  (services/)    │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              Data Layer                 │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ E3 Integration  │ │ File Operations │ │
│  │ (integrations/) │ │   (utils/)      │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
```

## Package Structure

```
apps/publish/
├── __init__.py                 # Package initialization
├── container.py                # Dependency injection container
├── exceptions.py               # Custom exception hierarchy
├── error_handler.py           # Centralized error handling
├── config/                    # Configuration management
│   ├── __init__.py
│   ├── configuration_manager.py
│   └── logging_config.py
├── gui/                       # User interface components
│   ├── __init__.py
│   ├── main_window.py         # Main application window
│   ├── widgets.py             # Custom UI widgets
│   └── dialogs.py             # Dialog components
├── services/                  # Business logic services
│   ├── __init__.py
│   ├── publish_service.py     # Core publishing orchestration
│   ├── model_service.py       # Model management
│   ├── export_service.py      # PDF/DXF export operations
│   └── manual_service.py      # Manual creation
├── models/                    # Data models and validation
│   ├── __init__.py
│   ├── project_data.py        # Project data structures
│   ├── publish_config.py      # Configuration models
│   └── validation.py          # Data validation utilities
├── integrations/              # External system integrations
│   ├── __init__.py
│   ├── e3_client.py          # E3 Series COM API wrapper
│   └── report_generator.py    # Report generator integration
└── utils/                     # Utility functions
    ├── __init__.py
    ├── file_operations.py     # File and folder operations
    └── series_generator.py    # Series number generation
```

## Key Components

### Services Layer

The services layer contains the core business logic:

- **PublishService**: Orchestrates the complete publishing workflow
- **ModelService**: Manages model data and lookup operations
- **ExportService**: Handles PDF and DXF export operations
- **ManualService**: Manages manual creation processes

### Models Layer

The models layer defines data structures and validation:

- **ProjectData**: Core project information with validation
- **TitleBlockData**: E3 title block data structure
- **PublishConfig**: Publishing configuration options
- **ValidationResult**: Data validation results

### Integrations Layer

The integrations layer handles external system communication:

- **E3Client**: E3 Series COM API wrapper with resource management
- **ReportGeneratorClient**: Integration with report generation tools

### GUI Layer

The GUI layer provides the user interface:

- **MainWindow**: Primary application window
- **Custom Widgets**: Reusable UI components
- **Dialogs**: Error and confirmation dialogs

## Usage Examples

### Basic Publishing

```python
from apps.publish.container import ServiceContainer
from apps.publish.models.project_data import ProjectData
from apps.publish.services.publish_service import PublishConfig

# Initialize service container
container = ServiceContainer()
publish_service = container.get_service(PublishService)

# Create project data
project_data = ProjectData(
    gss_parent="GSS123",
    serial_number="001",
    customer="Example Corp",
    model="Model-A"
)

# Create publish configuration
config = PublishConfig()
config.output_base_path = "/path/to/output"
config.export_formats = ['pdf', 'dxf']

# Publish project
result = publish_service.publish_project(project_data, config)

if result.success:
    print(f"Published successfully to {result.output_path}")
else:
    print(f"Publishing failed: {result.errors}")
```

### Series Publishing

```python
# Publish a series of 5 projects
results = publish_service.publish_series(project_data, config, 5)

successful_count = sum(1 for r in results if r.success)
print(f"Published {successful_count}/{len(results)} projects successfully")
```

### Using E3 Integration

```python
from apps.publish.integrations.e3_client import E3Client

# Use E3 client with context manager for proper cleanup
with E3Client() as e3:
    title_block_data = e3.read_title_block_data()
    if title_block_data:
        project_data = title_block_data.to_project_data()
        # Continue with publishing...
```

## Error Handling

The package uses a comprehensive exception hierarchy:

- **PublishError**: Base exception for all publishing operations
- **ValidationError**: Data validation failures
- **E3ConnectionError**: E3 Series integration issues
- **ExportError**: Export operation failures
- **ConfigurationError**: Configuration issues
- **FileOperationError**: File system operation failures

## Configuration

Configuration is managed through the `ConfigurationManager`:

```python
from apps.publish.config.configuration_manager import ConfigurationManager

config_manager = ConfigurationManager()
app_config = config_manager.get_app_config()
models_config = config_manager.get_models_config()
```

## Testing

The package includes comprehensive unit and integration tests:

```bash
# Run all tests
python -m pytest test/

# Run specific test modules
python -m pytest test/test_publish_service.py
python -m pytest test/test_project_data.py
```

## Logging

Centralized logging is configured through `logging_config.py`:

```python
from apps.publish.config.logging_config import setup_logging

# Setup logging for the application
setup_logging()

# Use logging in modules
import logging
logger = logging.getLogger(__name__)
logger.info("Publishing started")
```

## Development Guidelines

### Adding New Features

1. **Services**: Add business logic to appropriate service classes
2. **Models**: Define data structures in the models package
3. **Validation**: Add validation logic to validation.py
4. **Tests**: Create comprehensive unit tests
5. **Documentation**: Update docstrings and documentation

### Code Quality Standards

- Follow PEP 8 style guidelines
- Use type hints for all public methods
- Write comprehensive docstrings
- Maintain test coverage above 80%
- Use dependency injection for testability

### Error Handling Best Practices

- Use appropriate exception types from the hierarchy
- Include context information in exceptions
- Log errors with appropriate levels
- Provide user-friendly error messages in the GUI

## Dependencies

### Required Packages

- `customtkinter`: Modern GUI framework
- `e3series`: E3 Series COM API (if available)

### Optional Packages

- `pytest`: For running tests
- `mypy`: For type checking
- `black`: For code formatting

## Migration from Legacy Code

The package replaces the monolithic `publish_3.py` script with a modular architecture. Key migration benefits:

- **Separation of Concerns**: Clear boundaries between GUI, business logic, and data
- **Testability**: Easy unit testing with dependency injection
- **Maintainability**: Modular structure for easier maintenance
- **Extensibility**: Clean interfaces for adding new features
- **Error Handling**: Comprehensive error management and logging

## Support and Troubleshooting

### Common Issues

1. **E3 Connection Failures**: Ensure E3 Series is running and accessible
2. **Export Errors**: Check file permissions and output paths
3. **Configuration Issues**: Verify configuration files are valid JSON
4. **Import Errors**: Ensure all dependencies are installed

### Debug Mode

Enable debug logging for troubleshooting:

```python
import logging
logging.getLogger('apps.publish').setLevel(logging.DEBUG)
```

### Performance Considerations

- Use context managers for resource cleanup
- Implement lazy loading where appropriate
- Cache frequently accessed configuration data
- Monitor memory usage with large series publishing