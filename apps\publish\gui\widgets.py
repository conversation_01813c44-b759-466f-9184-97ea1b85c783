"""
Custom widgets for the publish application.

This module contains reusable UI components that can be used across
different parts of the application. All widgets are decoupled from
business logic and can be used independently.
"""

import customtkinter as ctk
from typing import List, Optional, Callable, Tuple, Dict, Any
import logging
import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
from datetime import datetime

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from .communication import LogMessage, ProgressUpdate
from .log_handler import LogLevelFilter


class E3InstanceSelector(ctk.CTkFrame):
    """Custom frame for selecting E3 Series instances."""

    def __init__(self, parent, on_instance_changed: Optional[Callable] = None, **kwargs):
        """
        Initialize the E3 instance selector.

        Args:
            parent: Parent widget
            on_instance_changed: Callback function when instance selection changes
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)
        self.on_instance_changed = on_instance_changed
        self.instances = []
        self.selected_instance = None

        # Create controls
        self._create_controls()

        # Load initial instances
        self.refresh_instances()

    def _create_controls(self):
        """Create the instance selector widgets."""
        # Title label
        title_label = ctk.CTkLabel(self, text="E3 Series Instance:", font=ctk.CTkFont(weight="bold"))
        title_label.pack(side="left", padx=5)

        # Instance dropdown
        self.instance_dropdown = ctk.CTkComboBox(
            self,
            values=["No instances found"],
            command=self._on_instance_selected,
            width=300
        )
        self.instance_dropdown.pack(side="left", padx=5, fill="x", expand=True)

        # Refresh button
        self.refresh_button = ctk.CTkButton(
            self,
            text="Refresh",
            command=self.refresh_instances,
            width=80
        )
        self.refresh_button.pack(side="right", padx=5)

    def _on_instance_selected(self, selection: str):
        """Handle instance selection change."""
        try:
            if not self.instances or selection == "No instances found":
                self.selected_instance = None
                self.logger.debug("No valid instance selected")
                return

            # Find the selected instance
            for instance in self.instances:
                if self._format_instance_display(instance) == selection:
                    # Check if this is actually a different instance
                    if (self.selected_instance and
                        self.selected_instance['pid'] == instance['pid']):
                        self.logger.debug(f"Same instance selected (PID {instance['pid']}), skipping callback")
                        return

                    self.selected_instance = instance
                    self.logger.info(f"Selected E3 instance: PID {instance['pid']}, Project: {instance.get('project_path', 'Unknown')}")

                    # Notify callback
                    if self.on_instance_changed:
                        self.logger.debug("Calling instance changed callback")
                        try:
                            self.on_instance_changed(instance)
                        except Exception as callback_error:
                            self.logger.error(f"Error in instance changed callback: {callback_error}")
                    else:
                        self.logger.debug("No instance changed callback registered")
                    break
            else:
                self.logger.warning(f"Could not find instance for selection: {selection}")

        except Exception as e:
            self.logger.error(f"Error handling instance selection: {e}")

    def refresh_instances(self):
        """Refresh the list of available E3 instances."""
        try:
            self.logger.debug("Refreshing E3 instances...")

            # Import the connection manager
            from lib.e3_connection_manager import E3ConnectionManager

            # Get running instances
            manager = E3ConnectionManager(self.logger)
            raw_instances = manager.get_running_e3_instances()

            # Convert to dict format for easier handling
            self.instances = []
            for instance in raw_instances:
                instance_dict = {
                    'pid': instance.pid,
                    'name': instance.name,
                    'project_path': instance.project_path or "Unknown Project"
                }
                self.instances.append(instance_dict)

            # Update dropdown
            if self.instances:
                display_values = [self._format_instance_display(inst) for inst in self.instances]
                self.instance_dropdown.configure(values=display_values)

                # Auto-select first instance if none selected
                if not self.selected_instance and self.instances:
                    self.instance_dropdown.set(display_values[0])
                    # Delay the callback slightly to ensure parent window is ready
                    if hasattr(self, 'master') and hasattr(self.master, 'after'):
                        self.master.after(100, lambda: self._on_instance_selected(display_values[0]))
                    else:
                        self._on_instance_selected(display_values[0])

                self.logger.info(f"Found {len(self.instances)} E3 instances")
            else:
                self.instance_dropdown.configure(values=["No instances found"])
                self.instance_dropdown.set("No instances found")
                self.selected_instance = None
                self.logger.warning("No E3 instances found")

        except Exception as e:
            self.logger.error(f"Error refreshing E3 instances: {e}")
            self.instance_dropdown.configure(values=["Error loading instances"])
            self.instance_dropdown.set("Error loading instances")
            self.selected_instance = None

    def _format_instance_display(self, instance: Dict[str, Any]) -> str:
        """Format instance information for display."""
        project_name = os.path.basename(instance.get('project_path', 'Unknown'))
        if project_name == 'Unknown':
            project_name = instance.get('project_path', 'Unknown Project')
        return f"PID {instance['pid']} - {project_name}"

    def get_selected_instance(self) -> Optional[Dict[str, Any]]:
        """Get the currently selected instance."""
        return self.selected_instance

    def get_selected_pid(self) -> Optional[int]:
        """Get the PID of the currently selected instance."""
        return self.selected_instance['pid'] if self.selected_instance else None


class ModelDropdown(ctk.CTkOptionMenu):
    """Custom dropdown for model selection."""

    def __init__(self, parent, values: List[str] = None,
                 command: Optional[Callable] = None, **kwargs):
        """
        Initialize the model dropdown.

        Args:
            parent: Parent widget
            values: Initial list of values
            command: Callback function for selection changes
            **kwargs: Additional arguments for CTkOptionMenu
        """
        self.logger = logging.getLogger(__name__)

        # Set default values if none provided
        if values is None:
            values = ["Select GSS Parent #"]

        # Store values internally for easier access
        self._current_values = values.copy()

        # Initialize with default values
        super().__init__(parent, values=values, command=command, **kwargs)

        # Store original command for chaining
        self._user_command = command

        # Set default selection
        if values:
            self.set(values[0])
            
    def update_models(self, models: List[str]):
        """
        Update the available models in the dropdown.

        Args:
            models: List of model names to display
        """
        self.logger.debug(f"Updating model dropdown with {len(models)} models")

        if models:
            # Store values internally
            self._current_values = models.copy()
            # Configure with new model list
            self.configure(values=models)
            # Set first model as default
            self.set(models[0])
            self.logger.debug(f"Set default model to: {models[0]}")
        else:
            # No models available
            no_models_text = "No matching models"
            self._current_values = [no_models_text]
            self.configure(values=[no_models_text])
            self.set(no_models_text)
            self.logger.debug("No models available for current GSS parent")
            
    def get_selected_model(self) -> str:
        """
        Get the currently selected model.
        
        Returns:
            Currently selected model name
        """
        current_value = self.get()
        
        # Return empty string for placeholder values
        if current_value in ["Select GSS Parent #", "No matching models"]:
            return ""
            
        return current_value
        
    def has_valid_selection(self) -> bool:
        """
        Check if a valid model is selected.
        
        Returns:
            True if a valid model is selected
        """
        return bool(self.get_selected_model())
        
    def clear_selection(self):
        """Clear the current selection."""
        default_text = "Select GSS Parent #"
        self._current_values = [default_text]
        self.configure(values=[default_text])
        self.set(default_text)

    def has_value(self, value: str) -> bool:
        """Check if a value exists in the dropdown."""
        return value in self._current_values

    def get_values(self) -> List[str]:
        """Get the current values in the dropdown."""
        return self._current_values.copy()


class SeriesControls(ctk.CTkFrame):
    """Controls for series publishing functionality."""
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the series controls.
        
        Args:
            parent: Parent widget
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)
        
        # Control variables
        self.create_manual_var = ctk.BooleanVar(value=False)
        self.fill_series_var = ctk.BooleanVar(value=False)
        
        # UI components
        self.manual_checkbox: Optional[ctk.CTkCheckBox] = None
        self.series_checkbox: Optional[ctk.CTkCheckBox] = None
        self.series_count_entry: Optional[ctk.CTkEntry] = None
        
        self.setup_controls()
        
    def setup_controls(self):
        """Set up the series control widgets."""
        # Manual creation checkbox
        manual_frame = ctk.CTkFrame(self)
        manual_frame.pack(fill="x", padx=5, pady=2)
        
        self.manual_checkbox = ctk.CTkCheckBox(
            manual_frame,
            text="Create Manual",
            variable=self.create_manual_var,
            command=self._on_manual_checkbox_changed
        )
        self.manual_checkbox.pack(side="left", padx=5)
        
        # Series publishing controls
        series_frame = ctk.CTkFrame(self)
        series_frame.pack(fill="x", padx=5, pady=2)
        
        self.series_checkbox = ctk.CTkCheckBox(
            series_frame,
            text="Fill Series",
            variable=self.fill_series_var,
            command=self._on_series_checkbox_changed
        )
        self.series_checkbox.pack(side="left", padx=5)
        
        # Series count entry
        count_label = ctk.CTkLabel(series_frame, text="Count:", width=50)
        count_label.pack(side="left", padx=(20, 5))
        
        self.series_count_entry = ctk.CTkEntry(
            series_frame, 
            width=80, 
            placeholder_text="1"
        )
        self.series_count_entry.pack(side="left", padx=5)
        self.series_count_entry.insert(0, "1")  # Default value
        
        # Initially disable count entry
        self.series_count_entry.configure(state="disabled")
        
    def _on_manual_checkbox_changed(self):
        """Handle manual creation checkbox changes."""
        is_checked = self.create_manual_var.get()
        self.logger.debug(f"Manual creation checkbox changed: {is_checked}")
        
    def _on_series_checkbox_changed(self):
        """Handle series checkbox changes."""
        is_checked = self.fill_series_var.get()
        self.logger.debug(f"Series checkbox changed: {is_checked}")
        
        # Enable/disable count entry based on checkbox state
        if is_checked:
            self.series_count_entry.configure(state="normal")
            # Focus on the entry and select all text for easy editing
            self.series_count_entry.focus()
            self.series_count_entry.select_range(0, 'end')
        else:
            self.series_count_entry.configure(state="disabled")
            
    def get_create_manual(self) -> bool:
        """
        Get the create manual setting.
        
        Returns:
            True if manual creation is enabled
        """
        return self.create_manual_var.get()
        
    def set_create_manual(self, value: bool):
        """
        Set the create manual setting.
        
        Args:
            value: Whether to enable manual creation
        """
        self.create_manual_var.set(value)
        
    def is_series_enabled(self) -> bool:
        """
        Check if series publishing is enabled.
        
        Returns:
            True if series publishing is enabled
        """
        return self.fill_series_var.get()
        
    def set_series_enabled(self, value: bool):
        """
        Set the series publishing setting.
        
        Args:
            value: Whether to enable series publishing
        """
        self.fill_series_var.set(value)
        self._on_series_checkbox_changed()  # Update UI state
        
    def get_series_count(self) -> int:
        """
        Get the series count value.
        
        Returns:
            Number of projects in series (minimum 1)
        """
        try:
            count_text = self.series_count_entry.get().strip()
            if not count_text:
                return 1
                
            count = int(count_text)
            return max(1, count)  # Ensure minimum of 1
        except (ValueError, AttributeError):
            self.logger.warning("Invalid series count value, defaulting to 1")
            return 1
            
    def set_series_count(self, count: int):
        """
        Set the series count value.
        
        Args:
            count: Number of projects in series
        """
        if count < 1:
            count = 1
            
        self.series_count_entry.delete(0, 'end')
        self.series_count_entry.insert(0, str(count))
        
    def validate_series_count(self) -> tuple[bool, str]:
        """
        Validate the series count input.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.is_series_enabled():
            return True, ""
            
        try:
            count_text = self.series_count_entry.get().strip()
            if not count_text:
                return False, "Series count cannot be empty"
                
            count = int(count_text)
            if count < 1:
                return False, "Series count must be at least 1"
            if count > 1000:
                return False, "Series count cannot exceed 1000"
                
            return True, ""
        except ValueError:
            return False, "Series count must be a valid number"
            
    def get_configuration(self) -> dict:
        """
        Get the current configuration as a dictionary.
        
        Returns:
            Dictionary containing current settings
        """
        return {
            "create_manual": self.get_create_manual(),
            "series_enabled": self.is_series_enabled(),
            "series_count": self.get_series_count()
        }
        
    def set_configuration(self, config: dict):
        """
        Set configuration from a dictionary.
        
        Args:
            config: Dictionary containing settings
        """
        if "create_manual" in config:
            self.set_create_manual(config["create_manual"])
            
        if "series_enabled" in config:
            self.set_series_enabled(config["series_enabled"])
            
        if "series_count" in config:
            self.set_series_count(config["series_count"])


class FormField(ctk.CTkFrame):
    """Reusable form field widget with label and entry."""
    
    def __init__(self, parent, label_text: str, entry_width: int = 200,
                 label_width: int = 120, **kwargs):
        """
        Initialize the form field.
        
        Args:
            parent: Parent widget
            label_text: Text for the label
            entry_width: Width of the entry widget
            label_width: Width of the label widget
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        
        self.label_text = label_text
        
        # Create label
        self.label = ctk.CTkLabel(
            self, 
            text=label_text + ":", 
            width=label_width
        )
        self.label.pack(side="left", padx=5)
        
        # Create entry
        self.entry = ctk.CTkEntry(self, width=entry_width)
        self.entry.pack(side="right", fill="x", expand=True, padx=5)
        
    def get(self) -> str:
        """Get the entry value."""
        return self.entry.get()
        
    def set(self, value: str):
        """Set the entry value."""
        self.entry.delete(0, 'end')
        self.entry.insert(0, value)
        
    def clear(self):
        """Clear the entry value."""
        self.entry.delete(0, 'end')
        
    def bind(self, event: str, callback):
        """Bind an event to the entry widget."""
        self.entry.bind(event, callback)
        
    def configure_entry(self, **kwargs):
        """Configure the entry widget."""
        self.entry.configure(**kwargs)
        
    def configure_label(self, **kwargs):
        """Configure the label widget."""
        self.label.configure(**kwargs)


class ConsoleLogWidget(ctk.CTkFrame):
    """
    Console log widget for displaying real-time application logs.

    Features:
    - Scrollable text display with auto-scroll
    - Log level filtering
    - Search functionality
    - Copy/clear functionality
    - Configurable appearance
    """

    def __init__(self, parent, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize the console log widget.

        Args:
            parent: Parent widget
            config: Configuration dictionary for the widget
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)

        # Configuration
        self.config = config or {}
        self._setup_config()

        # State
        self._log_messages: List[LogMessage] = []
        self._filtered_messages: List[LogMessage] = []
        self._log_filter = LogLevelFilter(self.min_log_level)
        self._auto_scroll = self.config.get('auto_scroll', True)
        self._max_lines = self.config.get('max_lines', 1000)
        self._update_lock = threading.Lock()

        # Create UI
        self._create_controls()
        self._create_text_widget()

        # Start update timer
        self._schedule_update()

    def _setup_config(self):
        """Set up configuration with defaults."""
        defaults = {
            'height': 200,
            'auto_scroll': True,
            'max_lines': 1000,
            'log_level': 'INFO',
            'show_timestamps': True,
            'font_family': 'Consolas',
            'font_size': 10,
            'show_by_default': True,
            'collapsible': True
        }

        for key, default_value in defaults.items():
            if key not in self.config:
                self.config[key] = default_value

        self.min_log_level = self.config['log_level']

    def _create_controls(self):
        """Create control widgets."""
        # Control frame
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=5, pady=(5, 0))

        # Title label
        title_label = ctk.CTkLabel(
            control_frame,
            text="Console Log",
            font=ctk.CTkFont(weight="bold")
        )
        title_label.pack(side="left", padx=5)

        # Log level filter
        level_label = ctk.CTkLabel(control_frame, text="Level:")
        level_label.pack(side="left", padx=(20, 5))

        self.level_var = tk.StringVar(value=self.min_log_level)
        self.level_dropdown = ctk.CTkOptionMenu(
            control_frame,
            values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            variable=self.level_var,
            command=self._on_level_changed,
            width=100
        )
        self.level_dropdown.pack(side="left", padx=5)

        # Auto-scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=self._auto_scroll)
        auto_scroll_cb = ctk.CTkCheckBox(
            control_frame,
            text="Auto-scroll",
            variable=self.auto_scroll_var,
            command=self._on_auto_scroll_changed
        )
        auto_scroll_cb.pack(side="left", padx=(20, 5))

        # Clear button
        clear_btn = ctk.CTkButton(
            control_frame,
            text="Clear",
            command=self.clear_logs,
            width=60
        )
        clear_btn.pack(side="right", padx=5)

        # Copy button
        copy_btn = ctk.CTkButton(
            control_frame,
            text="Copy",
            command=self.copy_selected,
            width=60
        )
        copy_btn.pack(side="right", padx=5)

    def _create_text_widget(self):
        """Create the text display widget."""
        # Text frame with scrollbar
        text_frame = ctk.CTkFrame(self)
        text_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Configure text widget
        font_family = self.config.get('font_family', 'Consolas')
        font_size = self.config.get('font_size', 10)

        self.text_widget = ctk.CTkTextbox(
            text_frame,
            height=self.config.get('height', 200),
            font=ctk.CTkFont(family=font_family, size=font_size),
            wrap="word"
        )
        self.text_widget.pack(fill="both", expand=True)

        # Configure text tags for different log levels
        self._configure_text_tags()

    def _configure_text_tags(self):
        """Configure text tags for different log levels."""
        # Note: CustomTkinter's CTkTextbox doesn't support tags like tkinter.Text
        # This is a placeholder for potential future enhancement
        pass

    def add_log_message(self, log_message: LogMessage):
        """
        Add a log message to the display.

        Args:
            log_message: LogMessage to add
        """
        with self._update_lock:
            self._log_messages.append(log_message)

            # Limit message history
            if len(self._log_messages) > self._max_lines:
                self._log_messages = self._log_messages[-self._max_lines:]

            # Update filtered messages
            self._update_filtered_messages()

    def add_log_messages(self, log_messages: List[LogMessage]):
        """
        Add multiple log messages to the display.

        Args:
            log_messages: List of LogMessage objects to add
        """
        with self._update_lock:
            self._log_messages.extend(log_messages)

            # Limit message history
            if len(self._log_messages) > self._max_lines:
                self._log_messages = self._log_messages[-self._max_lines:]

            # Update filtered messages
            self._update_filtered_messages()

    def clear_logs(self):
        """Clear all log messages."""
        with self._update_lock:
            self._log_messages.clear()
            self._filtered_messages.clear()
            self.text_widget.delete("1.0", "end")

    def copy_selected(self):
        """Copy selected text to clipboard."""
        try:
            selected_text = self.text_widget.get("sel.first", "sel.last")
            if selected_text:
                self.clipboard_clear()
                self.clipboard_append(selected_text)
            else:
                # If no selection, copy all visible text
                all_text = self.text_widget.get("1.0", "end-1c")
                if all_text:
                    self.clipboard_clear()
                    self.clipboard_append(all_text)
        except tk.TclError:
            # No selection
            all_text = self.text_widget.get("1.0", "end-1c")
            if all_text:
                self.clipboard_clear()
                self.clipboard_append(all_text)

    def set_log_level(self, level: str):
        """
        Set the minimum log level to display.

        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.min_log_level = level.upper()
        self._log_filter.set_min_level(self.min_log_level)
        self.level_var.set(self.min_log_level)
        self._update_filtered_messages()
        self._update_display()

    def set_auto_scroll(self, auto_scroll: bool):
        """
        Set auto-scroll behavior.

        Args:
            auto_scroll: Whether to auto-scroll to latest messages
        """
        self._auto_scroll = auto_scroll
        self.auto_scroll_var.set(auto_scroll)

    def _on_level_changed(self, level: str):
        """Handle log level change."""
        self.set_log_level(level)

    def _on_auto_scroll_changed(self):
        """Handle auto-scroll checkbox change."""
        self.set_auto_scroll(self.auto_scroll_var.get())

    def _update_filtered_messages(self):
        """Update the filtered messages based on current filter."""
        self._filtered_messages = self._log_filter.filter_messages(self._log_messages)

    def _update_display(self):
        """Update the text display with current filtered messages."""
        try:
            # Clear current content
            self.text_widget.delete("1.0", "end")

            # Add filtered messages
            for message in self._filtered_messages:
                if self.config.get('show_timestamps', True):
                    display_text = message.formatted_message
                else:
                    display_text = f"{message.level:8} {message.module}: {message.message}"

                self.text_widget.insert("end", display_text + "\n")

            # Auto-scroll to bottom if enabled
            if self._auto_scroll:
                self.text_widget.see("end")

        except Exception as e:
            self.logger.error(f"Error updating console display: {e}")

    def _schedule_update(self):
        """Schedule the next display update."""
        update_interval = 100  # ms
        self.after(update_interval, self._periodic_update)

    def _periodic_update(self):
        """Periodic update method called by timer."""
        try:
            # Only update if we have new messages and widget is visible
            if self._filtered_messages and self.winfo_viewable():
                # Batch updates for better performance
                if len(self._filtered_messages) > 100:
                    # For large message counts, only show recent messages
                    recent_messages = self._filtered_messages[-100:]
                    self._display_messages(recent_messages)
                else:
                    self._update_display()
        except Exception as e:
            self.logger.error(f"Error in periodic update: {e}")
        finally:
            # Schedule next update with adaptive interval
            update_interval = self._get_adaptive_update_interval()
            self.after(update_interval, self._periodic_update)

    def _get_adaptive_update_interval(self) -> int:
        """Get adaptive update interval based on message volume."""
        message_count = len(self._filtered_messages)
        if message_count > 500:
            return 200  # Slower updates for high volume
        elif message_count > 100:
            return 150
        else:
            return 100  # Default interval

    def _display_messages(self, messages: List[LogMessage]):
        """Display a specific set of messages (optimized version)."""
        try:
            # Clear current content
            self.text_widget.delete("1.0", "end")

            # Batch insert for better performance
            text_content = []
            for message in messages:
                if self.config.get('show_timestamps', True):
                    display_text = message.formatted_message
                else:
                    display_text = f"{message.level:8} {message.module}: {message.message}"
                text_content.append(display_text)

            # Single insert operation
            self.text_widget.insert("1.0", "\n".join(text_content))

            # Auto-scroll to bottom if enabled
            if self._auto_scroll:
                self.text_widget.see("end")

        except Exception as e:
            self.logger.error(f"Error displaying messages: {e}")


class ProgressBarWidget(ctk.CTkFrame):
    """
    Progress bar widget for displaying operation progress.

    Features:
    - Determinate and indeterminate progress modes
    - Status text display (current operation, ETA)
    - Cancel button with proper event handling
    - Configurable appearance and behavior
    """

    def __init__(self, parent, config: Optional[Dict[str, Any]] = None,
                 on_cancel: Optional[Callable] = None, **kwargs):
        """
        Initialize the progress bar widget.

        Args:
            parent: Parent widget
            config: Configuration dictionary for the widget
            on_cancel: Callback function when cancel button is clicked
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)

        # Configuration
        self.config = config or {}
        self._setup_config()

        # Callbacks
        self.on_cancel = on_cancel

        # State
        self._current_progress: Optional[ProgressUpdate] = None
        self._is_visible = False
        self._is_indeterminate = False

        # Create UI
        self._create_widgets()

        # Initially hidden
        self.hide()

    def _setup_config(self):
        """Set up configuration with defaults."""
        defaults = {
            'height': 30,
            'show_eta': True,
            'show_percentage': True,
            'allow_cancel': True,
            'update_interval_ms': 100
        }

        for key, default_value in defaults.items():
            if key not in self.config:
                self.config[key] = default_value

    def _create_widgets(self):
        """Create the progress bar widgets."""
        # Configure frame height
        self.configure(height=self.config.get('height', 30))

        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(self)
        self.progress_bar.pack(side="left", fill="x", expand=True, padx=(5, 10), pady=5)
        self.progress_bar.set(0)

        # Status label
        self.status_label = ctk.CTkLabel(
            self,
            text="Ready",
            width=200,
            anchor="w"
        )
        self.status_label.pack(side="left", padx=5, pady=5)

        # Cancel button
        if self.config.get('allow_cancel', True):
            self.cancel_button = ctk.CTkButton(
                self,
                text="Cancel",
                command=self._on_cancel_clicked,
                width=80,
                state="disabled"
            )
            self.cancel_button.pack(side="right", padx=5, pady=5)
        else:
            self.cancel_button = None

    def show(self):
        """Show the progress bar widget."""
        if not self._is_visible:
            self.pack(fill="x", padx=5, pady=(0, 5))
            self._is_visible = True

    def hide(self):
        """Hide the progress bar widget."""
        if self._is_visible:
            self.pack_forget()
            self._is_visible = False

    def update_progress(self, progress: ProgressUpdate):
        """
        Update the progress display.

        Args:
            progress: ProgressUpdate object with current progress information
        """
        self._current_progress = progress

        # Show the widget if not visible
        if not self._is_visible:
            self.show()

        # Update progress bar
        if progress.total > 0:
            # Determinate mode
            self._is_indeterminate = False
            progress_value = progress.current / progress.total
            self.progress_bar.set(progress_value)
        else:
            # Indeterminate mode
            if not self._is_indeterminate:
                self._is_indeterminate = True
                self._start_indeterminate_animation()

        # Update status text
        self._update_status_text(progress)

        # Update cancel button state
        if self.cancel_button:
            if progress.can_cancel:
                self.cancel_button.configure(state="normal")
            else:
                self.cancel_button.configure(state="disabled")

    def _update_status_text(self, progress: ProgressUpdate):
        """Update the status text display."""
        status_parts = []

        # Add step name
        if progress.step_name:
            status_parts.append(progress.step_name)

        # Add percentage if enabled and available
        if self.config.get('show_percentage', True) and progress.total > 0:
            status_parts.append(f"({progress.percentage:.1f}%)")

        # Add ETA if enabled and available
        if self.config.get('show_eta', True) and progress.eta_seconds:
            eta_text = self._format_eta(progress.eta_seconds)
            status_parts.append(f"ETA: {eta_text}")

        # Add current message
        if progress.message:
            status_parts.append(f"- {progress.message}")

        status_text = " ".join(status_parts) if status_parts else "Processing..."
        self.status_label.configure(text=status_text)

    def _format_eta(self, seconds: float) -> str:
        """
        Format ETA seconds into human-readable string.

        Args:
            seconds: ETA in seconds

        Returns:
            Formatted ETA string
        """
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            remaining_seconds = int(seconds % 60)
            return f"{minutes}m {remaining_seconds}s"
        else:
            hours = int(seconds / 3600)
            remaining_minutes = int((seconds % 3600) / 60)
            return f"{hours}h {remaining_minutes}m"

    def _start_indeterminate_animation(self):
        """Start indeterminate progress animation."""
        # CustomTkinter doesn't have built-in indeterminate mode
        # We'll simulate it by oscillating the progress value
        self._animate_indeterminate()

    def _animate_indeterminate(self):
        """Animate indeterminate progress."""
        if not self._is_indeterminate or not self._is_visible:
            return

        # Simple oscillating animation
        import time
        current_time = time.time()
        # Create a sine wave between 0 and 1
        import math
        progress_value = (math.sin(current_time * 2) + 1) / 2
        self.progress_bar.set(progress_value)

        # Schedule next animation frame
        self.after(50, self._animate_indeterminate)

    def _on_cancel_clicked(self):
        """Handle cancel button click."""
        if self.on_cancel and self._current_progress and self._current_progress.can_cancel:
            try:
                self.on_cancel()
            except Exception as e:
                self.logger.error(f"Error in cancel callback: {e}")

    def set_complete(self, success: bool = True, message: str = "Complete"):
        """
        Set the progress bar to complete state.

        Args:
            success: Whether the operation was successful
            message: Completion message
        """
        if success:
            self.progress_bar.set(1.0)
            self.status_label.configure(text=f"✓ {message}")
        else:
            self.status_label.configure(text=f"✗ {message}")

        # Disable cancel button
        if self.cancel_button:
            self.cancel_button.configure(state="disabled")

        self._is_indeterminate = False

        # Auto-hide after a delay
        self.after(3000, self.hide)  # Hide after 3 seconds

    def set_error(self, message: str = "Error occurred"):
        """
        Set the progress bar to error state.

        Args:
            message: Error message
        """
        self.set_complete(success=False, message=message)

    def reset(self):
        """Reset the progress bar to initial state."""
        self.progress_bar.set(0)
        self.status_label.configure(text="Ready")
        self._is_indeterminate = False
        self._current_progress = None

        if self.cancel_button:
            self.cancel_button.configure(state="disabled")

        self.hide()

    def get_current_progress(self) -> Optional[ProgressUpdate]:
        """Get the current progress information."""
        return self._current_progress

    def is_visible(self) -> bool:
        """Check if the progress bar is currently visible."""
        return self._is_visible