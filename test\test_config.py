"""
Tests for the config module.

This module contains unit tests for the configuration management functions in the lib.config module.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
from unittest.mock import patch

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.config import (
    get_config_path,
    load_config,
    save_config,
    get_config_value,
    update_config,
    load_models,
    save_models,
    CONFIG_FILENAME,
    MODELS_FILENAME,
)

class TestConfig(unittest.TestCase):
    """Test case for the config module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()

        # Create a mock config file
        self.config_data = {"test_key": "test_value"}
        self.config_file = os.path.join(self.temp_dir, CONFIG_FILENAME)
        with open(self.config_file, "w") as f:
            json.dump(self.config_data, f)

        # Create a mock models file
        self.models_data = {
            "Category": {
                "Model": {
                    "template_path": "path/to/template.dotx",
                    "drawings_path": "path/to/drawings",
                    "asme_flag": False,
                    "controls_parent": ""
                }
            }
        }
        self.models_file = os.path.join(self.temp_dir, MODELS_FILENAME)
        with open(self.models_file, "w") as f:
            json.dump(self.models_data, f)

    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

    @patch("lib.config.get_user_data_dir")
    @patch("lib.config.get_app_dir")
    def test_get_config_path(self, mock_get_app_dir, mock_get_user_data_dir):
        """Test get_config_path function."""
        # Set up mocks
        mock_get_user_data_dir.return_value = os.path.join(self.temp_dir, "user")
        mock_get_app_dir.return_value = os.path.join(self.temp_dir, "app")

        # Create directories
        os.makedirs(mock_get_user_data_dir.return_value)
        os.makedirs(os.path.join(mock_get_app_dir.return_value, "resources", "config"))

        # Create a user config file
        user_config = os.path.join(mock_get_user_data_dir.return_value, "user_config.json")
        with open(user_config, "w") as f:
            json.dump({"user": "data"}, f)

        # Create an app config file
        app_config = os.path.join(mock_get_app_dir.return_value, "resources", "config", "app_config.json")
        with open(app_config, "w") as f:
            json.dump({"app": "data"}, f)

        # Test with config file in user directory
        config_path = get_config_path("user_config.json")
        self.assertEqual(config_path, user_config)

        # Test with config file in app directory
        config_path = get_config_path("app_config.json")
        self.assertEqual(config_path, app_config)

        # Test with config file not found
        config_path = get_config_path("nonexistent.json")
        self.assertEqual(config_path, os.path.join(mock_get_app_dir.return_value, "nonexistent.json"))

    @patch("lib.config.get_config_path")
    def test_load_config(self, mock_get_config_path):
        """Test load_config function."""
        # Test with existing config file
        mock_get_config_path.return_value = self.config_file
        config = load_config(CONFIG_FILENAME)
        self.assertEqual(config, self.config_data)

        # Test with non-existent config file
        nonexistent_file = os.path.join(self.temp_dir, "nonexistent.json")
        mock_get_config_path.return_value = nonexistent_file
        with self.assertRaises(FileNotFoundError):
            load_config("nonexistent.json")

        # Test with invalid JSON
        invalid_json = os.path.join(self.temp_dir, "invalid.json")
        with open(invalid_json, "w") as f:
            f.write("invalid json")

        mock_get_config_path.return_value = invalid_json
        with self.assertRaises(json.JSONDecodeError):
            load_config("invalid.json")

    @patch("lib.config.get_user_data_dir")
    @patch("lib.config.ensure_dir_exists")
    def test_save_config(self, mock_ensure_dir_exists, mock_get_user_data_dir):
        """Test save_config function."""
        # Set up mocks
        mock_get_user_data_dir.return_value = self.temp_dir
        mock_ensure_dir_exists.return_value = self.temp_dir

        # Test saving config
        result = save_config({"new": "data"}, "new_config.json")
        self.assertTrue(result)

        # Check that the file was created
        new_config_path = os.path.join(self.temp_dir, "new_config.json")
        self.assertTrue(os.path.exists(new_config_path))

        # Check the content
        with open(new_config_path, "r") as f:
            data = json.load(f)
            self.assertEqual(data, {"new": "data"})

    @patch("lib.config.load_config")
    def test_get_config_value(self, mock_load_config):
        """Test get_config_value function."""
        # Set up mock
        mock_load_config.return_value = {"key1": "value1", "key2": "value2"}

        # Test with existing key
        value = get_config_value("key1")
        self.assertEqual(value, "value1")

        # Test with non-existent key
        value = get_config_value("key3", default="default")
        self.assertEqual(value, "default")

        # Test with exception
        mock_load_config.side_effect = Exception("Test exception")
        value = get_config_value("key1", default="default")
        self.assertEqual(value, "default")

    def test_update_config(self):
        """Test update_config function."""
        # Test with mocked functions
        with patch("lib.config.load_config") as mock_load_config, \
             patch("lib.config.save_config") as mock_save_config:
            # Set up mocks
            mock_load_config.return_value = {"key1": "value1"}
            mock_save_config.return_value = True

            # Test updating existing key
            result = update_config("key1", "new_value")
            self.assertTrue(result)
            mock_save_config.assert_called_with({"key1": "new_value"}, CONFIG_FILENAME)

        # Test adding new key with fresh mocks
        with patch("lib.config.load_config") as mock_load_config, \
             patch("lib.config.save_config") as mock_save_config:
            # Set up mocks
            mock_load_config.return_value = {"key1": "value1"}
            mock_save_config.return_value = True

            # Test adding new key
            result = update_config("key2", "value2")
            self.assertTrue(result)
            mock_save_config.assert_called_with({"key1": "value1", "key2": "value2"}, CONFIG_FILENAME)

        # Test with load_config exception with fresh mocks
        with patch("lib.config.load_config") as mock_load_config, \
             patch("lib.config.save_config") as mock_save_config:
            # Set up mocks
            mock_load_config.side_effect = FileNotFoundError("Test exception")
            mock_save_config.return_value = True

            # Test with exception
            result = update_config("key1", "new_value")
            self.assertTrue(result)
            mock_save_config.assert_called_with({"key1": "new_value"}, CONFIG_FILENAME)

    @patch("lib.config.load_config")
    def test_load_models(self, mock_load_config):
        """Test load_models function."""
        # Set up mock
        mock_load_config.return_value = self.models_data

        # Test loading models
        models = load_models()
        self.assertEqual(models, self.models_data)
        mock_load_config.assert_called_with(MODELS_FILENAME)

    @patch("lib.config.save_config")
    def test_save_models(self, mock_save_config):
        """Test save_models function."""
        # Set up mock
        mock_save_config.return_value = True

        # Test saving models
        result = save_models(self.models_data)
        self.assertTrue(result)
        mock_save_config.assert_called_with(self.models_data, MODELS_FILENAME)

if __name__ == "__main__":
    unittest.main()
