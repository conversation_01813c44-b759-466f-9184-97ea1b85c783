"""
PDF Printing Configuration Module

This module provides functions for managing configuration settings for the PDF Section Printing Application.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from lib.utils import get_user_data_dir, ensure_dir_exists, save_json_config, load_json_config
except ImportError:
    from utils import get_user_data_dir, ensure_dir_exists, save_json_config, load_json_config

from lib.pdf_printing_engine import PrinterProfile, create_default_profiles, get_default_printer

# Configuration file names
PROFILES_FILENAME = "pdf_printer_profiles.json"
SETTINGS_FILENAME = "pdf_printing_settings.json"

def get_config_dir() -> str:
    """
    Get the directory for configuration files.
    
    Returns:
        str: The path to the configuration directory
    """
    config_dir = os.path.join(get_user_data_dir(), "pdf_printing")
    ensure_dir_exists(config_dir)
    return config_dir

def save_profiles(profiles: Dict[str, PrinterProfile]) -> bool:
    """
    Save printer profiles to a configuration file.
    
    Args:
        profiles: A dictionary of printer profiles
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Convert profiles to dictionaries
        profiles_dict = {k: v.to_dict() for k, v in profiles.items()}
        
        # Save to file
        config_path = os.path.join(get_config_dir(), PROFILES_FILENAME)
        with open(config_path, 'w') as f:
            json.dump(profiles_dict, f, indent=4)
        
        logging.info(f"Saved printer profiles to {config_path}")
        return True
    except Exception as e:
        logging.error(f"Error saving printer profiles: {e}")
        return False

def load_profiles() -> Dict[str, PrinterProfile]:
    """
    Load printer profiles from a configuration file.
    
    Returns:
        Dict[str, PrinterProfile]: A dictionary of printer profiles
    """
    try:
        # Load from file
        config_path = os.path.join(get_config_dir(), PROFILES_FILENAME)
        if not os.path.exists(config_path):
            # Create default profiles if the file doesn't exist
            profiles = create_default_profiles()
            save_profiles(profiles)
            return profiles
        
        with open(config_path, 'r') as f:
            profiles_dict = json.load(f)
        
        # Convert dictionaries to profiles
        profiles = {k: PrinterProfile.from_dict(v) for k, v in profiles_dict.items()}
        
        logging.info(f"Loaded printer profiles from {config_path}")
        return profiles
    except Exception as e:
        logging.error(f"Error loading printer profiles: {e}")
        # Return default profiles if loading fails
        return create_default_profiles()

def save_settings(settings: Dict[str, Any]) -> bool:
    """
    Save application settings to a configuration file.
    
    Args:
        settings: A dictionary of application settings
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Save to file
        config_path = os.path.join(get_config_dir(), SETTINGS_FILENAME)
        with open(config_path, 'w') as f:
            json.dump(settings, f, indent=4)
        
        logging.info(f"Saved application settings to {config_path}")
        return True
    except Exception as e:
        logging.error(f"Error saving application settings: {e}")
        return False

def load_settings() -> Dict[str, Any]:
    """
    Load application settings from a configuration file.
    
    Returns:
        Dict[str, Any]: A dictionary of application settings
    """
    try:
        # Load from file
        config_path = os.path.join(get_config_dir(), SETTINGS_FILENAME)
        if not os.path.exists(config_path):
            # Create default settings if the file doesn't exist
            settings = {
                "last_directory": "",
                "default_printer": get_default_printer(),
                "auto_print": False,
                "show_preview": True
            }
            save_settings(settings)
            return settings
        
        with open(config_path, 'r') as f:
            settings = json.load(f)
        
        logging.info(f"Loaded application settings from {config_path}")
        return settings
    except Exception as e:
        logging.error(f"Error loading application settings: {e}")
        # Return default settings if loading fails
        return {
            "last_directory": "",
            "default_printer": get_default_printer(),
            "auto_print": False,
            "show_preview": True
        }
