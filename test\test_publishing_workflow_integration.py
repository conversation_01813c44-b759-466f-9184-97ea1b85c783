"""
Integration tests for the complete publishing workflow.

This module contains integration tests that verify the complete publishing
workflow from end to end, testing component interactions and data flow.
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os
import tempfile
import shutil
import json
from datetime import datetime

# Add the parent directory to the path to import from apps
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.services.publish_service import PublishService, PublishConfig, PublishResult
from apps.publish.services.model_service import ModelService, ModelData
from apps.publish.services.export_service import ExportService, ExportResult
from apps.publish.services.manual_service import ManualService, ManualResult
from apps.publish.integrations.e3_client import E3Client
from apps.publish.integrations.report_generator import ReportGeneratorClient
from apps.publish.models.project_data import ProjectData, TitleBlockData


class TestPublishingWorkflowIntegration(unittest.TestCase):
    """Integration tests for the complete publishing workflow."""
    
    def setUp(self):
        """Set up test fixtures for integration testing."""
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Create mock models configuration
        self.models_config = {
            "Chiller": {
                "P408": {
                    "template_path": os.path.join(self.temp_dir, "template.dotx"),
                    "drawings_path": os.path.join(self.temp_dir, "drawings"),
                    "asme_flag": False,
                    "controls_parent": "503390"
                },
                "P410": {
                    "template_path": os.path.join(self.temp_dir, "template2.dotx"),
                    "drawings_path": os.path.join(self.temp_dir, "drawings2"),
                    "asme_flag": True,
                    "controls_parent": "503390"
                }
            }
        }
        
        # Create mock template files
        for category in self.models_config:
            for model, data in self.models_config[category].items():
                os.makedirs(os.path.dirname(data["template_path"]), exist_ok=True)
                with open(data["template_path"], 'w') as f:
                    f.write("Mock template content")
                
                os.makedirs(data["drawings_path"], exist_ok=True)
        
        # Create test project data
        self.project_data = ProjectData(
            gss_parent="TEST123",
            serial_number="001",
            customer="Test Customer",
            location="Test Location",
            title="Test Project",
            sales_order="SO-12345",
            model="P408",
            folder_path=os.path.join(self.temp_dir, "project")
        )
        
        # Create publish configuration
        self.publish_config = PublishConfig()
        self.publish_config.output_base_path = self.temp_dir
        self.publish_config.create_manual = True
        self.publish_config.export_formats = ["PDF", "DXF"]
        
    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_complete_publishing_workflow_success(self):
        """Test the complete publishing workflow with all components working together."""
        # Create service instances
        mock_e3_client = Mock(spec=E3Client)
        mock_report_generator = Mock(spec=ReportGeneratorClient)
        
        model_service = ModelService(self.models_config)
        export_service = ExportService(mock_e3_client)
        manual_service = ManualService(self.models_config)
        
        publish_service = PublishService(
            e3_client=mock_e3_client,
            export_service=export_service,
            manual_service=manual_service,
            report_generator=mock_report_generator
        )
        
        # Setup mocks for successful workflow
        mock_e3_client.get_process_id.return_value = 12345
        mock_e3_client.export_pdf.return_value = True
        mock_e3_client.export_dxf_dataplates.return_value = ["dataplate1.dxf", "dataplate2.dxf"]
        
        mock_report_generator.update_bom_reports.return_value = {
            "PHOENIX BOM": True,
            "Simplified BOM": True
        }
        mock_report_generator.export_gss_bom.return_value = "gss_bom.xlsx"
        
        # Mock manual creation
        with patch.object(manual_service, 'is_manual_creation_available', return_value=True):
            with patch.object(manual_service, 'create_manual') as mock_create_manual:
                # Setup manual creation mock
                manual_result = ManualResult()
                manual_result.success = True
                manual_result.created_files = [
                    os.path.join(self.temp_dir, "TEST123 001", "001_manual.pdf")
                ]
                mock_create_manual.return_value = manual_result
                
                # Execute the complete workflow
                result = publish_service.publish_project(self.project_data, self.publish_config)
        
        # Verify the workflow completed successfully
        self.assertTrue(result.success)
        self.assertEqual(result.serial_number, "001")
        self.assertGreater(len(result.steps_completed), 0)
        
        # Verify all major steps were completed
        expected_steps = [
            "update_attributes",
            "update_bom_reports", 
            "export_files",
            "create_manual",
            "export_gss_bom",
            "save_job_data"
        ]
        for step in expected_steps:
            self.assertIn(step, result.steps_completed)
        
        # Verify output folder was created
        expected_output_path = os.path.join(self.temp_dir, "TEST123 001")
        self.assertTrue(os.path.exists(expected_output_path))
        
        # Verify job data JSON was created
        json_path = os.path.join(expected_output_path, "TEST123 001.json")
        self.assertTrue(os.path.exists(json_path))
        
        # Verify JSON content
        with open(json_path, 'r') as f:
            job_data = json.load(f)
        
        self.assertEqual(job_data["gss_parent"], "TEST123")
        self.assertEqual(job_data["serial_number"], "001")
        self.assertEqual(job_data["customer"], "Test Customer")
        self.assertIn("timestamp", job_data)
    
    def test_series_publishing_workflow(self):
        """Test series publishing workflow with multiple projects."""
        # Create service instances
        mock_e3_client = Mock(spec=E3Client)
        model_service = ModelService(self.models_config)
        export_service = ExportService(mock_e3_client)
        
        publish_service = PublishService(
            e3_client=mock_e3_client,
            export_service=export_service
        )
        
        # Setup mocks for successful exports
        mock_e3_client.export_pdf.return_value = True
        mock_e3_client.export_dxf_dataplates.return_value = ["dataplate.dxf"]
        
        # Configure for series publishing
        self.publish_config.create_manual = False  # Disable manual for simplicity
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        # Execute series publishing
        results = publish_service.publish_series(self.project_data, self.publish_config, 3)
        
        # Verify series results
        self.assertEqual(len(results), 3)
        
        expected_serials = ["001", "002", "003"]
        for i, result in enumerate(results):
            self.assertTrue(result.success)
            self.assertEqual(result.serial_number, expected_serials[i])
            
            # Verify output folders were created
            expected_folder = os.path.join(self.temp_dir, f"TEST123 {expected_serials[i]}")
            self.assertTrue(os.path.exists(expected_folder))
    
    def test_model_service_integration_with_project_data(self):
        """Test model service integration with project data validation."""
        model_service = ModelService(self.models_config)
        
        # Test getting models for GSS parent
        models = model_service.get_models_for_gss("503390")
        self.assertEqual(len(models), 2)
        self.assertIn("P408", models)
        self.assertIn("P410", models)
        
        # Test getting model data
        model_data = model_service.get_model_data("P408")
        self.assertIsNotNone(model_data)
        self.assertEqual(model_data.controls_parent, "503390")
        self.assertFalse(model_data.asme_flag)
        
        # Test with project data
        self.assertEqual(self.project_data.model, "P408")
        project_model_data = model_service.get_model_data(self.project_data.model)
        self.assertIsNotNone(project_model_data)
        self.assertEqual(project_model_data.controls_parent, "503390")
    
    def test_title_block_to_project_data_workflow(self):
        """Test workflow from title block data to project data."""
        # Create title block data
        title_block = TitleBlockData(
            document_number="DOC-12345",
            model="P410",
            description="Test Description",
            customer="Title Block Customer",
            location="Title Block Location",
            sales_order="TB-SO-123",
            serial_number="TB-001"
        )
        
        # Convert to project data
        project_data = title_block.to_project_data(
            folder_path=os.path.join(self.temp_dir, "title_block_project"),
            model_override="P408"  # Override the model
        )
        
        # Verify conversion
        self.assertEqual(project_data.gss_parent, "DOC-12345")
        self.assertEqual(project_data.serial_number, "TB-001")
        self.assertEqual(project_data.customer, "Title Block Customer")
        self.assertEqual(project_data.model, "P408")  # Should use override
        
        # Validate the converted project data
        validation_result = project_data.validate()
        if not validation_result.is_valid:
            # Create the folder if validation fails due to missing folder
            os.makedirs(project_data.folder_path, exist_ok=True)
            validation_result = project_data.validate()
        
        self.assertTrue(validation_result.is_valid)
    
    def test_error_handling_integration(self):
        """Test error handling across integrated components."""
        from apps.publish.integrations.e3_client import E3ConnectionError
        
        # Create service instances with failing components
        mock_e3_client = Mock(spec=E3Client)
        mock_e3_client.update_attributes.side_effect = E3ConnectionError("E3 connection failed")
        mock_e3_client.export_pdf.return_value = True  # Let PDF succeed
        mock_e3_client.export_dxf_dataplates.return_value = []  # Let DXF succeed
        
        export_service = ExportService(mock_e3_client)
        publish_service = PublishService(e3_client=mock_e3_client, export_service=export_service)
        
        # Configure to continue on errors
        self.publish_config.fail_on_e3_errors = False
        self.publish_config.fail_on_export_errors = False
        self.publish_config.create_manual = False
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        # Execute publishing with errors
        result = publish_service.publish_project(self.project_data, self.publish_config)
        
        # Verify error handling
        self.assertFalse(result.success)
        self.assertGreater(len(result.errors), 0)
        
        # Verify that some operations still completed despite errors
        self.assertIn("save_job_data", result.steps_completed)
        self.assertIn("export_files", result.steps_completed)
        
        # Verify output folder was still created
        expected_output_path = os.path.join(self.temp_dir, "TEST123 001")
        self.assertTrue(os.path.exists(expected_output_path))
    
    def test_configuration_validation_integration(self):
        """Test configuration validation across components."""
        # Test invalid project data
        invalid_project = ProjectData()  # Missing required fields
        validation_result = invalid_project.validate()
        self.assertFalse(validation_result.is_valid)
        self.assertGreater(len(validation_result.errors), 0)
        
        # Test valid project data
        valid_project = ProjectData(
            gss_parent="VALID-123",
            serial_number="001",
            model="P408",
            folder_path=self.temp_dir
        )
        validation_result = valid_project.validate()
        self.assertTrue(validation_result.is_valid)
        
        # Test model data validation
        model_service = ModelService(self.models_config)
        model_data = model_service.get_model_data("P408")
        
        # Model data should be valid since we created the template files
        self.assertIsNotNone(model_data)
        self.assertEqual(model_data.controls_parent, "503390")
    
    def test_data_flow_integration(self):
        """Test data flow between components."""
        # Create services
        model_service = ModelService(self.models_config)
        
        # Test data flow: GSS Parent -> Models -> Model Data
        gss_parent = "503390"
        available_models = model_service.get_models_for_gss(gss_parent)
        self.assertGreater(len(available_models), 0)
        
        # Select first model and get its data
        selected_model = available_models[0]
        model_data = model_service.get_model_data(selected_model)
        self.assertIsNotNone(model_data)
        
        # Verify the data flow is consistent
        self.assertEqual(model_data.controls_parent, gss_parent)
        
        # Test project data creation with model
        project_data = ProjectData(
            gss_parent="NEW-GSS-123",
            serial_number="DATA-001",
            model=selected_model,
            folder_path=os.path.join(self.temp_dir, "data_flow_test")
        )
        
        # Verify model consistency
        project_model_data = model_service.get_model_data(project_data.model)
        self.assertEqual(project_model_data.controls_parent, model_data.controls_parent)
    
    def test_resource_cleanup_integration(self):
        """Test that resources are properly cleaned up across components."""
        # Create service instances
        mock_e3_client = Mock(spec=E3Client)
        export_service = ExportService(mock_e3_client)
        publish_service = PublishService(e3_client=mock_e3_client, export_service=export_service)
        
        # Configure minimal publishing
        self.publish_config.create_manual = False
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        # Setup mocks
        mock_e3_client.export_pdf.return_value = True
        mock_e3_client.export_dxf_dataplates.return_value = []
        
        # Execute publishing
        result = publish_service.publish_project(self.project_data, self.publish_config)
        
        # Verify basic success
        self.assertTrue(result.success)
        
        # Verify that temporary resources would be cleaned up
        # (In a real implementation, this would test COM object cleanup, file handles, etc.)
        self.assertIsNotNone(result.completed_at)
        self.assertIsInstance(result.completed_at, datetime)


class TestComponentInteractionIntegration(unittest.TestCase):
    """Integration tests for component interactions."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_service_dependency_injection(self):
        """Test that services can be properly injected and work together."""
        # Create mock dependencies
        mock_e3_client = Mock(spec=E3Client)
        mock_report_generator = Mock(spec=ReportGeneratorClient)
        
        # Create services with dependencies
        export_service = ExportService(mock_e3_client)
        manual_service = ManualService({})
        
        # Create main service with all dependencies
        publish_service = PublishService(
            e3_client=mock_e3_client,
            export_service=export_service,
            manual_service=manual_service,
            report_generator=mock_report_generator
        )
        
        # Verify dependencies are properly injected
        self.assertEqual(publish_service.e3_client, mock_e3_client)
        self.assertEqual(publish_service.export_service, export_service)
        self.assertEqual(publish_service.manual_service, manual_service)
        self.assertEqual(publish_service.report_generator, mock_report_generator)
        
        # Verify that export service has the same E3 client
        self.assertEqual(export_service.e3_client, mock_e3_client)
    
    def test_error_propagation_between_components(self):
        """Test that errors propagate correctly between components."""
        from apps.publish.services.export_service import ExportError
        from apps.publish.services.publish_service import PublishError
        
        # Create service with failing export service
        mock_e3_client = Mock(spec=E3Client)
        mock_e3_client.export_pdf.side_effect = ExportError("Export failed", "pdf_export")
        mock_e3_client.export_dxf_dataplates.return_value = []  # Let DXF succeed
        
        export_service = ExportService(mock_e3_client)
        publish_service = PublishService(export_service=export_service)
        
        # Create test data
        project_data = ProjectData(
            gss_parent="ERROR-TEST",
            serial_number="001",
            model="TestModel",
            folder_path=self.temp_dir
        )
        
        config = PublishConfig()
        config.output_base_path = self.temp_dir
        config.fail_on_export_errors = False  # Don't fail fast, let it continue
        config.create_manual = False
        config.update_bom_reports = False
        config.export_gss_bom = False
        
        # Test error handling (should not raise exception but should have errors)
        result = publish_service.publish_project(project_data, config)
        
        # Verify error handling
        self.assertFalse(result.success)
        self.assertGreater(len(result.errors), 0)
        
        # Verify error details are preserved
        error_messages = " ".join(result.errors).lower()
        self.assertIn("export", error_messages)
    
    def test_configuration_consistency_across_components(self):
        """Test that configuration is consistently applied across components."""
        # Test service dependency injection consistency
        mock_e3_client = Mock(spec=E3Client)
        mock_report_generator = Mock(spec=ReportGeneratorClient)
        
        # Create services with dependencies
        export_service = ExportService(mock_e3_client)
        manual_service = ManualService({})
        
        # Create main service with all dependencies
        publish_service = PublishService(
            e3_client=mock_e3_client,
            export_service=export_service,
            manual_service=manual_service,
            report_generator=mock_report_generator
        )
        
        # Verify dependencies are properly injected
        self.assertEqual(publish_service.e3_client, mock_e3_client)
        self.assertEqual(publish_service.export_service, export_service)
        self.assertEqual(publish_service.manual_service, manual_service)
        self.assertEqual(publish_service.report_generator, mock_report_generator)
        
        # Verify that export service has the same E3 client
        self.assertEqual(export_service.e3_client, mock_e3_client)
        
        # Test configuration consistency
        config = PublishConfig()
        config.export_formats = ["PDF"]
        config.create_manual = False
        config.update_bom_reports = False
        config.export_gss_bom = False
        
        # Verify configuration properties
        self.assertEqual(config.export_formats, ["PDF"])
        self.assertFalse(config.create_manual)


if __name__ == '__main__':
    unittest.main()