# Engineering Tools - Apps Directory

This directory contains the latest engineering tools and utilities for document management, project publishing, and workflow automation. All tools feature modern dark mode interfaces and are designed for professional engineering workflows.

## Available Tools

### Project Publishing and Management

#### Publish 3 (publish_3.py)
**Purpose**: Complete project publishing solution with advanced batch processing capabilities.

**Features**:
- Read and update title block data from engineering projects
- Export to PDF and DXF formats with automatic organization
- Optional manual creation using model-specific templates
- **Fill Series**: Batch publishing with auto-incrementing serial numbers
- Support for both numeric and alphanumeric serial number patterns
- Dark mode interface with real-time status updates
- JSON job data export for project tracking

**Usage**: Select project data, choose model, optionally enable Fill Series for batch processing, and publish.

#### Project Revision Tool (project_revision_tool.py)
**Purpose**: Create project revisions with automatic backup and version management.

**Features**:
- Automatic detection and incrementing of revision numbers (REV#)
- Timestamped backups in organized revision folders
- Support for both individual files and entire project directories
- Maintains project structure and file relationships
- Real-time status updates during processing

**Usage**: Select project file or directory, review current revision, and create new revision with automatic backup.

### Manual and Document Creation

#### Manual Creator (create_manuals.py)
**Purpose**: Generate technical manuals from Word templates with project-specific information.

**Features**:
- Template-based manual generation using Word automation
- Automatic placeholder replacement with project data
- PDF conversion and drawing integration
- Model-specific template and drawing selection
- Comprehensive error handling and logging

**Usage**: Select equipment category and model, choose job folder, enter project details, and create manual.

#### Job Data Creator (create_job_json.py)
**Purpose**: Create standardized JSON files for project tracking and documentation.

**Features**:
- Structured data entry for project information
- Model and category selection with validation
- Automatic timestamp generation
- Standardized filename format (GSS Parent # + Serial Number)
- Integration with project workflow systems

**Usage**: Enter project details, select model, choose save location, and generate JSON file.

### File Management and Organization

#### Copy Merged Files (copy_merged_files.py)
**Purpose**: Collect and organize files from MERGED folders across project directories.

**Features**:
- Recursive search through directory structures
- Automatic detection of MERGED folders
- Duplicate filename handling with automatic suffixes
- Progress tracking and detailed results display
- Option to open destination folder after completion

**Usage**: Select root directory, search for MERGED folders, review found files, and copy to central manual folder.

#### DXF Emailer (dxf_emailer.py)
**Purpose**: Automated email distribution of DXF files via Microsoft Outlook.

**Features**:
- Automatic DXF file detection in selected directories
- Microsoft Outlook integration for email sending
- Customizable recipient addresses
- Automatic email subject and body generation
- File attachment with size validation

**Usage**: Select folder containing DXF files, verify recipient email, and send automated email with attachments.

#### Nesting File Monitor (nesting_file_monitor.py)
**Purpose**: Monitor directories for new or modified STEP and PDF files and send email notifications.

**Features**:
- Continuous monitoring of specified directories
- Detection of new and modified STEP (.step, .stp) and PDF files
- Automatic email notifications via Microsoft Outlook
- File size validation for email attachment limits
- State persistence between monitoring sessions
- Detailed logging and error handling

**Usage**: Configure monitoring directory and email recipient, run to monitor for file changes and send notifications.

### PDF Processing and Printing

#### PDF Section Printing (print_merged_pdf_sections.py)
**Purpose**: Intelligent printing of PDF documents with automatic page size detection and section grouping.

**Features**:
- Automatic page size detection (Letter, Tabloid, Other)
- Intelligent section grouping of consecutive same-size pages
- Printer-specific settings based on page dimensions
- Temporary file extraction for section printing
- Progress tracking and detailed status reporting

**Usage**: Select directory containing merged PDFs and initiate intelligent section-based printing.

#### Deep PDF Merge (deep_pdf_merge.py)
**Purpose**: Advanced PDF merging capabilities for complex document assembly.

**Features**:
- Sophisticated PDF merging algorithms
- Handling of complex document structures
- Preservation of document metadata and formatting
- Error handling for corrupted or problematic PDFs
- Batch processing capabilities

**Usage**: Select PDF files or directories for advanced merging operations.

### Calculation and Analysis Tools

#### Sum Motor FLA (sum_motor_fla.py)
**Purpose**: Calculate total Full Load Amperage (FLA) for all motors in engineering projects.

**Features**:
- Automatic motor detection in project files
- FLA calculation and summation
- Attribute updating in project files
- Support for multiple motor types and configurations
- Detailed calculation logging and reporting

**Usage**: Select project file, analyze motors, and update with calculated total FLA.

### Configuration and System Management

#### Configuration Tool (config_app.py)
**Purpose**: Configure application settings, paths, and system preferences.

**Features**:
- Template directory path configuration
- Drawing directory path setup
- Monitoring folder configuration
- Email notification settings
- Automatic models.json and config.json updates
- Path validation and verification

**Usage**: Launch tool, configure paths and settings, and save configuration for all applications.

#### Add Model (add_model.py)
**Purpose**: Add new engineering models to the system with template and drawing configurations.

**Features**:
- Add models to existing equipment categories
- Create new equipment categories
- Configure template and drawing paths for each model
- Set ASME certification flags
- Configure controls parent IDs
- Automatic models.json updates

**Usage**: Select or create category, enter model details, configure paths, and save to system.

#### Documentation Status Updater (update_documentation_status.py)
**Purpose**: Update Obsidian vault documentation status for machine manuals.

**Features**:
- Recursive PDF file search in selected directories
- Machine manual identification based on filename patterns
- YAML frontmatter updates in Obsidian notes
- Documentation status tracking (Documentation: true/false)
- Dry run option for preview without changes
- Detailed action logging

**Usage**: Select folder to search, configure Obsidian vault path, and update documentation status.

### E3 Automation Scripts

#### Device Designation Automation (set_device_designations.py)
**Purpose**: Automatically update device designations in E3.series projects based on symbol positioning.

**Features**:
- Generates designations using format: `{device letter code}{sheet}{grid}`
- Finds topmost leftmost symbol position for each device
- Automatic device letter code detection from attributes or device names
- Conflict resolution with letter suffixes (A, B, C, etc.)
- Comprehensive logging and error handling
- Test script for connection verification

**Usage**: Ensure E3.series is running with project open, then run the script. Use the test script first to verify connection and device data.

**Related Files**:
- `apps/set_device_designations.py` - Main automation script
- `apps/test_device_designation.py` - Connection and data test script
- `README_device_designation.md` - Detailed documentation

#### Wire Numbering Automation (set_wire_numbers.py)
**Purpose**: Automatically assign wire numbers in E3.series projects based on connection positioning.

**Features**:
- Calculates wire numbers based on lowest page and grid position per signal
- Groups signals by base wire number with letter suffixes for conflicts
- Updates all net segments belonging to each signal
- Position-based sorting (left-to-right, top-to-bottom)
- FixWireName attribute support - skips connections with this attribute set
- Comprehensive connection and pin analysis

**Usage**: Ensure E3.series is running with project open, then run the script.

**Related Files**:
- `apps/set_wire_numbers.py` - Main wire numbering script
- `apps/test_fix_wire_name.py` - Test script for FixWireName functionality
- `README_wire_numbering.md` - Detailed documentation

## How to Use

### Using the Engineering Tools Launcher (Recommended)

1. Run the launcher from the main directory:
   ```
   python apps_launcher.py
   ```

2. The launcher provides:
   - Categorized view of all available tools
   - Tool descriptions and functionality overview
   - One-click launching of any tool
   - Status tracking and error reporting

3. Click the "Launch" button next to any tool to start it.

### Running Tools Directly

You can also run each tool directly:

```bash
# Project publishing
python apps/publish_3.py

# File management
python apps/copy_merged_files.py
python apps/dxf_emailer.py

# Manual creation
python apps/create_manuals.py
python apps/create_job_json.py

# PDF processing
python apps/print_merged_pdf_sections.py
python apps/deep_pdf_merge.py

# Monitoring and analysis
python apps/nesting_file_monitor.py
python apps/sum_motor_fla.py

# Configuration
python apps/config_app.py
python apps/add_model.py

# Documentation
python apps/update_documentation_status.py

# E3 Automation (requires E3.series running)
python apps/set_device_designations.py
python apps/test_device_designation.py
python apps/set_wire_numbers.py
python apps/test_fix_wire_name.py
```

## Requirements

- Python 3.8 or higher
- Windows operating system
- Microsoft Word (for manual creation features)
- Microsoft Outlook (for email features)
- Required Python packages (see requirements.txt in main directory)

## Adding New Tools

To add a new tool to the apps directory:

1. Create a new Python file in the `apps` directory
2. Add a comprehensive docstring at the top describing the tool's purpose and functionality
3. Include the standard path setup for importing from lib and utils:
   ```python
   import os
   import sys
   sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   ```
4. Follow the established patterns for GUI design using CustomTkinter
5. Implement proper error handling and logging
6. The tool will automatically appear in the Engineering Tools Launcher

## Support and Documentation

For detailed documentation on specific tools, see the individual README files in the main directory:
- README_publish_3.md
- README_sum_motor_fla.md
- README_PDF_Section_Printing.md
- README_documentation_updater.md
- README_device_designation.md
- README_wire_numbering.md

For installation and setup instructions, see INSTALL.md in the main directory.
