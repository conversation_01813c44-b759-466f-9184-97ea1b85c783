#!/usr/bin/env python3
"""
Test script to verify COM threading fix.
"""

import sys
import os

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.abspath(__file__))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_config_loading():
    """Test that the new configuration options load correctly."""
    try:
        from apps.publish.config.configuration_manager import ConfigurationManager
        
        config_manager = ConfigurationManager()
        config = config_manager.load_app_config()
        
        threading_config = config.get('threading', {})
        
        print("=== Configuration Test ===")
        print(f"✓ enable_background_processing: {threading_config.get('enable_background_processing', 'NOT SET')}")
        print(f"✓ fallback_to_sync: {threading_config.get('fallback_to_sync', 'NOT SET')}")
        print(f"✓ disable_for_e3_operations: {threading_config.get('disable_for_e3_operations', 'NOT SET')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_e3_detection():
    """Test E3 operation detection logic."""
    try:
        from apps.publish.services.publish_service import PublishConfig
        
        print("\n=== E3 Detection Test ===")
        
        # Test config with E3 operations
        config_with_e3 = PublishConfig()
        config_with_e3.save_project = True
        config_with_e3.export_formats = ['pdf']
        
        print(f"✓ Config with E3 operations created")
        print(f"  - save_project: {config_with_e3.save_project}")
        print(f"  - export_formats: {config_with_e3.export_formats}")
        
        # Test config without E3 operations
        config_without_e3 = PublishConfig()
        config_without_e3.save_project = False
        config_without_e3.export_formats = []
        
        print(f"✓ Config without E3 operations created")
        print(f"  - save_project: {config_without_e3.save_project}")
        print(f"  - export_formats: {config_without_e3.export_formats}")
        
        return True
        
    except Exception as e:
        print(f"✗ E3 detection test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing COM Threading Fix Implementation")
    print("=" * 50)
    
    success = True
    
    success &= test_config_loading()
    success &= test_e3_detection()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ All tests passed!")
        print("✓ COM threading fix is properly implemented")
        print("✓ E3 operations will now run synchronously to avoid COM errors")
    else:
        print("✗ Some tests failed")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
