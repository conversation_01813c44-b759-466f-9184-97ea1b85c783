"""
PDF Printing UI Module

This module provides the user interface for the PDF Section Printing Application.
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from typing import Dict, List, Tuple, Optional, Any, Callable
import fitz  # PyMuPDF

# Import utility functions
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.pdf_section_processor import PDFSection, PAGE_TYPE_LETTER, PAGE_TYPE_TABLOID, PAGE_TYPE_OTHER
from lib.pdf_printing_engine import PrinterProfile, get_installed_printers, get_default_printer
from lib.pdf_printing_config import load_profiles, save_profiles, load_settings, save_settings
from lib.pdf_printing_logging import StatusLogger

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")

# Apply the red theme
apply_theme("red", "dark")

class PDFPreviewFrame(ctk.CTkFrame):
    """Frame for displaying a PDF preview."""

    def __init__(self, master, **kwargs):
        """Initialize the PDF preview frame."""
        super().__init__(master, **kwargs)

        self.pdf_document = None
        self.current_page = 0
        self.total_pages = 0
        self.page_types = []
        self.sections = []
        self.zoom_factor = 1.0  # Base zoom factor
        self.auto_zoom = True   # Whether to auto-fit the page

        self.create_widgets()

    def create_widgets(self):
        """Create the widgets for the PDF preview frame."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create canvas for PDF preview
        self.canvas_frame = ctk.CTkFrame(self)
        self.canvas_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.canvas_frame.grid_columnconfigure(0, weight=1)
        self.canvas_frame.grid_rowconfigure(0, weight=1)

        self.canvas = tk.Canvas(self.canvas_frame, bg="#2B2B2B", highlightthickness=0)
        self.canvas.grid(row=0, column=0, sticky="nsew")

        # Create navigation controls
        self.controls_frame = ctk.CTkFrame(self)
        self.controls_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")

        self.btn_prev = ctk.CTkButton(
            self.controls_frame,
            text="Previous",
            command=self.prev_page,
            width=100
        )
        self.btn_prev.pack(side="left", padx=5)

        self.page_label = ctk.CTkLabel(
            self.controls_frame,
            text="Page 0 of 0",
            font=("Arial", 12)
        )
        self.page_label.pack(side="left", padx=5)

        self.btn_next = ctk.CTkButton(
            self.controls_frame,
            text="Next",
            command=self.next_page,
            width=100
        )
        self.btn_next.pack(side="left", padx=5)

        # Add zoom controls
        self.zoom_frame = ctk.CTkFrame(self.controls_frame)
        self.zoom_frame.pack(side="right", padx=10)

        self.btn_zoom_out = ctk.CTkButton(
            self.zoom_frame,
            text="-",
            command=self.zoom_out,
            width=30,
            height=30
        )
        self.btn_zoom_out.pack(side="left", padx=2)

        self.zoom_label = ctk.CTkLabel(
            self.zoom_frame,
            text="100%",
            width=50
        )
        self.zoom_label.pack(side="left", padx=2)

        self.btn_zoom_in = ctk.CTkButton(
            self.zoom_frame,
            text="+",
            command=self.zoom_in,
            width=30,
            height=30
        )
        self.btn_zoom_in.pack(side="left", padx=2)

        self.page_type_label = ctk.CTkLabel(
            self.controls_frame,
            text="Type: None",
            font=("Arial", 12, "bold")
        )
        self.page_type_label.pack(side="right", padx=5)

    def load_pdf(self, pdf_path: str, page_types: List[str], sections: List[PDFSection]):
        """
        Load a PDF file for preview.

        Args:
            pdf_path: The path to the PDF file
            page_types: A list of page types for each page
            sections: A list of PDF sections
        """
        try:
            # Close any previously open document
            if self.pdf_document:
                self.pdf_document.close()

            # Open the PDF document
            self.pdf_document = fitz.open(pdf_path)
            self.current_page = 0
            self.total_pages = len(self.pdf_document)
            self.page_types = page_types
            self.sections = sections

            # Update the UI
            self.update_page_label()
            self.render_current_page()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load PDF: {str(e)}")

    def render_current_page(self):
        """Render the current page of the PDF."""
        if not self.pdf_document or self.current_page >= self.total_pages:
            return

        try:
            # Get the current page
            page = self.pdf_document[self.current_page]

            # Get canvas dimensions for scaling
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Calculate zoom factor to fit the page in the canvas
            # with some padding
            page_rect = page.rect

            if self.auto_zoom:
                width_ratio = (canvas_width - 40) / page_rect.width
                height_ratio = (canvas_height - 40) / page_rect.height
                self.zoom_factor = min(width_ratio, height_ratio)

                # Use a minimum zoom level
                self.zoom_factor = max(self.zoom_factor, 0.5)

            # Update zoom label
            self.zoom_label.configure(text=f"{int(self.zoom_factor * 100)}%")

            # Render the page to a pixmap
            mat = fitz.Matrix(self.zoom_factor, self.zoom_factor)
            pix = page.get_pixmap(matrix=mat)

            # Convert the pixmap to a PhotoImage
            img_data = pix.tobytes("ppm")
            img = tk.PhotoImage(data=img_data)

            # Clear the canvas
            self.canvas.delete("all")

            # Calculate the position to center the image
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            img_width = img.width()
            img_height = img.height()

            x = max(0, (canvas_width - img_width) // 2)
            y = max(0, (canvas_height - img_height) // 2)

            # Draw the image on the canvas
            self.canvas.create_image(x, y, anchor="nw", image=img)

            # Add page type indicator in the top-right corner
            if self.current_page < len(self.page_types):
                page_type = self.page_types[self.current_page]
                page_type_color = "#2AA876" if page_type == "Letter" else "#1F538D" if page_type == "Tabloid" else "#B3261E"
                self.canvas.create_rectangle(
                    canvas_width - 120, 10, canvas_width - 10, 40,
                    fill=page_type_color, outline=""
                )
                self.canvas.create_text(
                    canvas_width - 65, 25,
                    text=f"{page_type}",
                    fill="white",
                    font=("Arial", 12, "bold")
                )

            # Keep a reference to the image to prevent garbage collection
            self.canvas.image = img

            # Update the page type label
            if self.current_page < len(self.page_types):
                self.page_type_label.configure(text=f"Type: {self.page_types[self.current_page]}")
            else:
                self.page_type_label.configure(text="Type: Unknown")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to render page: {str(e)}")

    def update_page_label(self):
        """Update the page label with the current page number."""
        self.page_label.configure(text=f"Page {self.current_page + 1} of {self.total_pages}")

    def next_page(self):
        """Go to the next page."""
        if self.pdf_document and self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.update_page_label()
            self.render_current_page()

    def prev_page(self):
        """Go to the previous page."""
        if self.pdf_document and self.current_page > 0:
            self.current_page -= 1
            self.update_page_label()
            self.render_current_page()

    def zoom_in(self):
        """Increase the zoom level."""
        self.auto_zoom = False
        self.zoom_factor *= 1.2
        self.render_current_page()

    def zoom_out(self):
        """Decrease the zoom level."""
        self.auto_zoom = False
        self.zoom_factor /= 1.2
        if self.zoom_factor < 0.1:
            self.zoom_factor = 0.1
        self.render_current_page()

    def close_pdf(self):
        """Close the PDF document."""
        if self.pdf_document:
            self.pdf_document.close()
            self.pdf_document = None
            self.current_page = 0
            self.total_pages = 0
            self.page_types = []
            self.sections = []
            self.zoom_factor = 1.0
            self.auto_zoom = True

            # Clear the canvas
            self.canvas.delete("all")

            # Update the UI
            self.update_page_label()
            self.page_type_label.configure(text="Type: None")
            self.zoom_label.configure(text="100%")

class PrinterProfileFrame(ctk.CTkFrame):
    """Frame for configuring printer profiles."""

    def __init__(self, master, profiles: Dict[str, PrinterProfile], on_save: Callable, **kwargs):
        """
        Initialize the printer profile frame.

        Args:
            master: The parent widget
            profiles: A dictionary of printer profiles
            on_save: A callback function to call when profiles are saved
        """
        super().__init__(master, **kwargs)

        self.profiles = profiles
        self.on_save = on_save
        self.printers = get_installed_printers()

        # Dictionary to store variables for each profile tab
        self.profile_vars = {}

        self.create_widgets()

    def create_widgets(self):
        """Create the widgets for the printer profile frame."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(3, weight=1)

        # Create profile selection
        self.profile_label = ctk.CTkLabel(
            self,
            text="Printer Profiles",
            font=("Arial", 14, "bold")
        )
        self.profile_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")

        # Create profile tabs
        self.profile_tabs = ctk.CTkTabview(self)
        self.profile_tabs.grid(row=1, column=0, padx=10, pady=5, sticky="nsew")

        # Add a tab for each profile
        for page_type, profile in self.profiles.items():
            tab = self.profile_tabs.add(page_type)
            self.create_profile_tab(tab, profile, page_type)

        # Create save button
        # Create button frame
        self.button_frame = ctk.CTkFrame(self)
        self.button_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        self.button_frame.grid_columnconfigure(0, weight=1)
        self.button_frame.grid_columnconfigure(1, weight=1)

        # Create reset button
        self.btn_reset = ctk.CTkButton(
            self.button_frame,
            text="Reset to Defaults",
            command=self.reset_profiles,
            height=30,
            font=("Arial", 12, "bold"),
            fg_color="#B3261E",
            hover_color="#8C1D16"
        )
        self.btn_reset.grid(row=0, column=0, padx=5, pady=0, sticky="ew")

        # Create save button
        self.btn_save = ctk.CTkButton(
            self.button_frame,
            text="Save Profiles",
            command=self.save_profiles,
            height=30,
            font=("Arial", 12, "bold"),
            fg_color="#2AA876",
            hover_color="#22815D"
        )
        self.btn_save.grid(row=0, column=1, padx=5, pady=0, sticky="ew")

    def create_profile_tab(self, tab, profile: PrinterProfile, page_type: str):
        """
        Create a tab for a printer profile.

        Args:
            tab: The tab widget
            profile: The printer profile
            page_type: The page type for this profile
        """
        # Configure grid layout
        tab.grid_columnconfigure(1, weight=1)

        # Create variables for this profile
        vars_dict = {
            "printer_var": tk.StringVar(value=profile.printer_name),
            "paper_size_var": tk.IntVar(value=profile.paper_size),
            "orientation_var": tk.IntVar(value=profile.orientation),
            "duplex_var": tk.IntVar(value=profile.duplex),
            "color_var": tk.IntVar(value=profile.color)
        }

        # Store the variables in the dictionary
        self.profile_vars[page_type] = vars_dict

        # Printer selection
        ctk.CTkLabel(tab, text="Printer:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        printer_dropdown = ctk.CTkOptionMenu(
            tab,
            values=self.printers,
            variable=vars_dict["printer_var"]
        )
        printer_dropdown.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        # Paper size
        ctk.CTkLabel(tab, text="Paper Size:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        paper_size_frame = ctk.CTkFrame(tab)
        paper_size_frame.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        ctk.CTkRadioButton(
            paper_size_frame,
            text="Letter",
            variable=vars_dict["paper_size_var"],
            value=1
        ).pack(side="left", padx=5)

        ctk.CTkRadioButton(
            paper_size_frame,
            text="Tabloid",
            variable=vars_dict["paper_size_var"],
            value=3
        ).pack(side="left", padx=5)

        # Orientation
        ctk.CTkLabel(tab, text="Orientation:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        orientation_frame = ctk.CTkFrame(tab)
        orientation_frame.grid(row=2, column=1, padx=10, pady=5, sticky="ew")

        ctk.CTkRadioButton(
            orientation_frame,
            text="Portrait",
            variable=vars_dict["orientation_var"],
            value=1
        ).pack(side="left", padx=5)

        ctk.CTkRadioButton(
            orientation_frame,
            text="Landscape",
            variable=vars_dict["orientation_var"],
            value=2
        ).pack(side="left", padx=5)

        # Duplex
        ctk.CTkLabel(tab, text="Duplex:").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        duplex_frame = ctk.CTkFrame(tab)
        duplex_frame.grid(row=3, column=1, padx=10, pady=5, sticky="ew")

        ctk.CTkRadioButton(
            duplex_frame,
            text="Single-sided",
            variable=vars_dict["duplex_var"],
            value=1
        ).pack(side="left", padx=5)

        ctk.CTkRadioButton(
            duplex_frame,
            text="Double-sided (Long Edge)",
            variable=vars_dict["duplex_var"],
            value=2
        ).pack(side="left", padx=5)

        # Color
        ctk.CTkLabel(tab, text="Color:").grid(row=4, column=0, padx=10, pady=5, sticky="w")
        color_frame = ctk.CTkFrame(tab)
        color_frame.grid(row=4, column=1, padx=10, pady=5, sticky="ew")

        ctk.CTkRadioButton(
            color_frame,
            text="Monochrome",
            variable=vars_dict["color_var"],
            value=1
        ).pack(side="left", padx=5)

        ctk.CTkRadioButton(
            color_frame,
            text="Color",
            variable=vars_dict["color_var"],
            value=2
        ).pack(side="left", padx=5)

    def reset_profiles(self):
        """Reset the printer profiles to their defaults."""
        try:
            # Ask for confirmation
            if not messagebox.askyesno("Confirm Reset", "Are you sure you want to reset all printer profiles to their defaults?"):
                return

            # Import here to avoid circular imports
            from lib.pdf_printing_engine import create_default_profiles

            # Create default profiles
            default_profiles = create_default_profiles()

            # Update the profiles
            for page_type, profile in default_profiles.items():
                if page_type in self.profiles:
                    self.profiles[page_type] = profile

            # Update the UI
            for page_type, vars_dict in self.profile_vars.items():
                profile = self.profiles.get(page_type)
                if profile and vars_dict:
                    vars_dict["printer_var"].set(profile.printer_name)
                    vars_dict["paper_size_var"].set(profile.paper_size)
                    vars_dict["orientation_var"].set(profile.orientation)
                    vars_dict["duplex_var"].set(profile.duplex)
                    vars_dict["color_var"].set(profile.color)

            # Call the save callback
            self.on_save(self.profiles)

            messagebox.showinfo("Success", "Printer profiles reset to defaults.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to reset printer profiles: {str(e)}")

    def save_profiles(self):
        """Save the printer profiles."""
        try:
            # Update the profiles with the current values
            for page_type, profile in self.profiles.items():
                vars_dict = self.profile_vars.get(page_type)
                if vars_dict:
                    profile.printer_name = vars_dict["printer_var"].get()
                    profile.paper_size = vars_dict["paper_size_var"].get()
                    profile.orientation = vars_dict["orientation_var"].get()
                    profile.duplex = vars_dict["duplex_var"].get()
                    profile.color = vars_dict["color_var"].get()

            # Call the save callback
            self.on_save(self.profiles)

            messagebox.showinfo("Success", "Printer profiles saved successfully.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save printer profiles: {str(e)}")

class PDFSectionPrintingApp(ctk.CTk):
    """Main application window for the PDF Section Printing Application."""

    def __init__(self):
        """Initialize the application window."""
        super().__init__()

        # Set window properties
        self.title("PDF Section Printing")
        self.geometry("1200x800")  # Increased window size
        self.minsize(1000, 700)  # Increased minimum size

        # Initialize variables
        self.current_directory = ""
        self.pdf_files = []
        self.current_pdf = None
        self.page_types = []
        self.sections = []

        # Load configuration
        self.profiles = load_profiles()
        self.settings = load_settings()

        # Initialize status logger
        self.status_logger = StatusLogger(self.update_status)

        # Create widgets
        self.create_widgets()

        # Load the last directory if available
        if self.settings.get("last_directory") and os.path.isdir(self.settings["last_directory"]):
            self.current_directory = self.settings["last_directory"]
            self.lbl_directory.configure(text=self.current_directory)
            self.search_merged_folders()

    def create_widgets(self):
        """Create the widgets for the application window."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=5)  # Increased weight for preview column
        self.grid_rowconfigure(1, weight=1)

        # Create directory selection frame
        self.dir_frame = ctk.CTkFrame(self)
        self.dir_frame.grid(row=0, column=0, columnspan=2, padx=20, pady=10, sticky="ew")

        self.btn_select = ctk.CTkButton(
            self.dir_frame,
            text="Select Directory",
            command=self.select_directory,
            width=120,
            corner_radius=8
        )
        self.btn_select.pack(side="left", padx=5)

        self.lbl_directory = ctk.CTkLabel(
            self.dir_frame,
            text="No directory selected",
            anchor="w"
        )
        self.lbl_directory.pack(side="left", fill="x", expand=True, padx=5)

        self.btn_search = ctk.CTkButton(
            self.dir_frame,
            text="Search for MERGED Folders",
            command=self.search_merged_folders,
            width=180,
            corner_radius=8
        )
        self.btn_search.pack(side="right", padx=5)

        # Create file list frame
        self.file_frame = ctk.CTkFrame(self)
        self.file_frame.grid(row=1, column=0, padx=(20, 10), pady=10, sticky="nsew")

        self.file_label = ctk.CTkLabel(
            self.file_frame,
            text="PDF Files in MERGED Folders",
            font=("Arial", 14, "bold")
        )
        self.file_label.pack(padx=10, pady=10, anchor="w")

        self.file_listbox = tk.Listbox(
            self.file_frame,
            bg="#2B2B2B",
            fg="#DCE4EE",
            selectbackground="#1F538D",
            highlightthickness=0,
            bd=0
        )
        self.file_listbox.pack(padx=10, pady=5, fill="both", expand=True)
        self.file_listbox.bind("<<ListboxSelect>>", self.on_file_select)

        # Create PDF preview frame
        self.preview_frame = PDFPreviewFrame(self)
        self.preview_frame.grid(row=1, column=1, padx=(10, 20), pady=10, sticky="nsew")

        # Create printer profile frame
        self.profile_frame = PrinterProfileFrame(
            self,
            profiles=self.profiles,
            on_save=self.save_profiles
        )
        self.profile_frame.grid(row=2, column=0, padx=(20, 10), pady=10, sticky="nsew")

        # Create print button
        self.btn_print = ctk.CTkButton(
            self,
            text="Print PDF Sections",
            command=self.print_pdf_sections,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#2AA876",
            hover_color="#22815D"
        )
        self.btn_print.grid(row=2, column=1, padx=(10, 20), pady=10, sticky="ew")

        # Create status bar
        self.status_bar = ctk.CTkLabel(
            self,
            text="Ready",
            anchor="w",
            height=25
        )
        self.status_bar.grid(row=3, column=0, columnspan=2, padx=20, pady=5, sticky="ew")

    def select_directory(self):
        """Open a directory selection dialog."""
        directory = filedialog.askdirectory()
        if directory:
            self.current_directory = directory
            self.lbl_directory.configure(text=directory)
            self.settings["last_directory"] = directory
            save_settings(self.settings)
            self.search_merged_folders()

    def search_merged_folders(self):
        """Search for MERGED folders in the selected directory."""
        if not self.current_directory:
            messagebox.showerror("Error", "Please select a directory first")
            return

        self.status_logger.info(f"Searching for MERGED folders in {self.current_directory}")

        # Clear the file list
        self.file_listbox.delete(0, tk.END)
        self.pdf_files = []

        try:
            # Walk through the directory tree
            for root, dirs, files in os.walk(self.current_directory):
                # Check if this is a MERGED folder
                if os.path.basename(root).upper() == "MERGED":
                    # Find PDF files in this folder
                    for file in files:
                        if file.lower().endswith(".pdf"):
                            pdf_path = os.path.join(root, file)
                            self.pdf_files.append(pdf_path)
                            self.file_listbox.insert(tk.END, f"{os.path.basename(root)} - {file}")

            if not self.pdf_files:
                self.status_logger.warning("No PDF files found in MERGED folders")
            else:
                self.status_logger.info(f"Found {len(self.pdf_files)} PDF files in MERGED folders")
        except Exception as e:
            self.status_logger.exception(e, "Error searching for MERGED folders")

    def on_file_select(self, event):
        """Handle file selection in the listbox."""
        selection = self.file_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        if index < len(self.pdf_files):
            pdf_path = self.pdf_files[index]
            self.load_pdf(pdf_path)

    def load_pdf(self, pdf_path):
        """
        Load a PDF file for processing.

        Args:
            pdf_path: The path to the PDF file
        """
        try:
            self.status_logger.info(f"Loading PDF: {pdf_path}")

            # Import here to avoid circular imports
            from lib.pdf_section_processor import identify_page_types, group_pages_into_sections, process_pdf

            # Process the PDF
            self.current_pdf = pdf_path
            self.page_types = identify_page_types(pdf_path)
            self.sections = group_pages_into_sections(self.page_types)

            # Extract sections to temporary files
            for section in self.sections:
                from lib.pdf_section_processor import extract_section_to_temp_file
                extract_section_to_temp_file(pdf_path, section)

            # Update the preview
            self.preview_frame.load_pdf(pdf_path, self.page_types, self.sections)

            # Update the status
            self.status_logger.info(f"PDF loaded: {len(self.page_types)} pages, {len(self.sections)} sections")
        except Exception as e:
            self.status_logger.exception(e, "Error loading PDF")

    def print_pdf_sections(self):
        """Print the sections of the current PDF."""
        if not self.current_pdf or not self.sections:
            messagebox.showerror("Error", "Please select a PDF file first")
            return

        # Create a dialog to select which sections to print
        self.print_dialog = ctk.CTkToplevel(self)
        self.print_dialog.title("Print PDF Sections")
        self.print_dialog.geometry("500x400")
        self.print_dialog.resizable(False, False)
        self.print_dialog.grab_set()  # Make the dialog modal

        # Configure grid layout
        self.print_dialog.grid_columnconfigure(0, weight=1)
        self.print_dialog.grid_rowconfigure(1, weight=1)

        # Create header label
        header_label = ctk.CTkLabel(
            self.print_dialog,
            text="Select sections to print",
            font=("Arial", 16, "bold")
        )
        header_label.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        # Create scrollable frame for sections
        sections_frame = ctk.CTkScrollableFrame(self.print_dialog)
        sections_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        sections_frame.grid_columnconfigure(0, weight=1)

        # Add a checkbox for each section
        self.section_vars = []
        for i, section in enumerate(self.sections):
            section_var = tk.BooleanVar(value=True)
            self.section_vars.append(section_var)

            # Create a frame for this section
            section_frame = ctk.CTkFrame(sections_frame)
            section_frame.grid(row=i, column=0, padx=5, pady=5, sticky="ew")
            section_frame.grid_columnconfigure(1, weight=1)

            # Add checkbox
            section_check = ctk.CTkCheckBox(
                section_frame,
                text="",
                variable=section_var,
                width=20,
                height=20
            )
            section_check.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # Add section info
            section_info = ctk.CTkLabel(
                section_frame,
                text=f"Section {i+1}: {section.page_type} (Pages {section.start_page+1}-{section.end_page+1})",
                font=("Arial", 12),
                anchor="w"
            )
            section_info.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

            # Add print button for this section
            section_print_btn = ctk.CTkButton(
                section_frame,
                text="Print",
                command=lambda s=section, i=i: self.print_single_section(s, i),
                width=80,
                height=25,
                font=("Arial", 12, "bold"),
                fg_color="#C53F3F",  # Red color
                hover_color="#A02222"  # Darker red
            )
            section_print_btn.grid(row=0, column=2, padx=5, pady=5, sticky="e")

        # Create button frame
        button_frame = ctk.CTkFrame(self.print_dialog)
        button_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)

        # Create cancel button
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self.print_dialog.destroy,
            height=30,
            font=("Arial", 12, "bold"),
            fg_color="#B3261E",
            hover_color="#8C1D16"
        )
        cancel_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # Create print all button
        print_all_btn = ctk.CTkButton(
            button_frame,
            text="Print Selected",
            command=self.print_selected_sections,
            height=30,
            font=("Arial", 12, "bold"),
            fg_color="#C53F3F",  # Red color
            hover_color="#A02222"  # Darker red
        )
        print_all_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    def print_selected_sections(self):
        """Print the selected sections of the current PDF."""
        # Get the selected sections
        selected_sections = []
        for i, var in enumerate(self.section_vars):
            if var.get():
                selected_sections.append(self.sections[i])

        if not selected_sections:
            messagebox.showinfo("Info", "No sections selected for printing")
            return

        # Close the dialog
        self.print_dialog.destroy()

        # Start printing in a separate thread
        threading.Thread(target=lambda: self.print_pdf_sections_thread(selected_sections)).start()

    def print_single_section(self, section, index):
        """Print a single section of the current PDF."""
        # Start printing in a separate thread
        threading.Thread(target=lambda: self.print_pdf_sections_thread([section], f"Section {index+1}")).start()

    def print_pdf_sections_thread(self, sections=None, label=None):
        """Print the specified sections of the current PDF in a separate thread."""
        try:
            if sections is None:
                sections = self.sections

            if label:
                self.status_logger.info(f"Starting to print {label}")
            else:
                self.status_logger.info(f"Starting to print {len(sections)} selected sections")

            # Import here to avoid circular imports
            from lib.pdf_printing_engine import print_pdf_section

            # Print each section individually
            success = True
            for section in sections:
                # Get the profile for this section type
                profile = self.profiles.get(section.page_type)
                if not profile:
                    self.status_logger.warning(f"No profile found for section type {section.page_type}")
                    success = False
                    continue

                # Print the section
                self.status_logger.info(f"Printing section {section} with profile settings:")
                self.status_logger.info(f"  - Printer: {profile.printer_name}")
                self.status_logger.info(f"  - Paper size: {profile.paper_size} (1=Letter, 3=Tabloid)")
                self.status_logger.info(f"  - Orientation: {profile.orientation} (1=Portrait, 2=Landscape)")
                self.status_logger.info(f"  - Duplex: {profile.duplex} (1=Simplex, 2=Duplex)")
                self.status_logger.info(f"  - Color: {profile.color} (1=Monochrome, 2=Color)")

                # Use the Windows API method directly
                from lib.pdf_printing_win32 import print_pdf_with_direct_api
                if not print_pdf_with_direct_api(section.temp_file_path, profile):
                    success = False

            # Show a message
            if success:
                if label:
                    self.status_logger.info(f"{label} printed successfully")
                    messagebox.showinfo("Success", f"{label} printed successfully")
                else:
                    self.status_logger.info("All selected sections printed successfully")
                    messagebox.showinfo("Success", "All selected sections printed successfully")
            else:
                self.status_logger.warning("Some sections failed to print")
                messagebox.showwarning("Warning", "Some sections failed to print. Check the log for details.")
        except Exception as e:
            self.status_logger.exception(e, "Error printing PDF sections")
            messagebox.showerror("Error", f"Error printing PDF sections: {str(e)}")

    def save_profiles(self, profiles):
        """
        Save the printer profiles.

        Args:
            profiles: A dictionary of printer profiles
        """
        self.profiles = profiles
        save_profiles(profiles)

    def update_status(self, message):
        """
        Update the status bar with a message.

        Args:
            message: The message to display
        """
        self.status_bar.configure(text=message)

    def on_closing(self):
        """Handle window closing."""
        # Clean up temporary files
        if hasattr(self, 'sections') and self.sections:
            from lib.pdf_section_processor import cleanup_temp_files
            cleanup_temp_files(self.sections)

        # Close the preview
        self.preview_frame.close_pdf()

        # Destroy the window
        self.destroy()
