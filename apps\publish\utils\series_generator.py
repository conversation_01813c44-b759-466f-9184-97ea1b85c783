"""
Series number generation utilities.

This module contains the SeriesGenerator class for parsing and incrementing
serial numbers in both numeric and alphanumeric formats.
"""

import re
import logging
from typing import List, Optional, Tuple
from enum import Enum


class SerialFormat(Enum):
    """Enumeration of supported serial number formats."""
    NUMERIC = "numeric"
    ALPHANUMERIC = "alphanumeric"
    ALPHA_SUFFIX = "alpha_suffix"
    MIXED = "mixed"
    UNKNOWN = "unknown"


class SeriesGeneratorError(Exception):
    """Custom exception for series generation errors."""
    pass


class SeriesGenerator:
    """Utility class for generating series of serial numbers."""
    
    def __init__(self):
        """Initialize series generator."""
        self.logger = logging.getLogger(__name__)
        
    def generate_series(self, base_serial: str, count: int) -> List[str]:
        """
        Generate a series of serial numbers starting from the base serial.
        
        Args:
            base_serial: Starting serial number
            count: Number of serial numbers to generate
            
        Returns:
            List of generated serial numbers
            
        Raises:
            SeriesGeneratorError: If base serial is invalid or count is invalid
        """
        if not base_serial or not base_serial.strip():
            raise SeriesGeneratorError("Base serial number cannot be empty")
            
        if count <= 0:
            raise SeriesGeneratorError("Count must be greater than 0")
            
        if not self.validate_serial_format(base_serial):
            raise SeriesGeneratorError(f"Invalid serial format: {base_serial}")
            
        series = [base_serial]
        current_serial = base_serial
        
        try:
            for i in range(1, count):
                current_serial = self.increment_serial(current_serial)
                if current_serial is None:
                    self.logger.warning(f"Could not increment serial beyond: {series[-1]}")
                    break
                series.append(current_serial)
        except Exception as e:
            raise SeriesGeneratorError(f"Error generating series: {e}") from e
            
        self.logger.info(f"Generated series of {len(series)} serials starting from {base_serial}")
        return series 
       
    def increment_serial(self, serial: str) -> Optional[str]:
        """
        Increment a serial number by one.
        
        Args:
            serial: Serial number to increment
            
        Returns:
            Incremented serial number or None if increment failed
        """
        if not serial:
            return None
            
        serial_format = self.detect_serial_format(serial)
        
        try:
            if serial_format == SerialFormat.NUMERIC:
                return self._increment_numeric(serial)
            elif serial_format in [SerialFormat.ALPHANUMERIC, SerialFormat.ALPHA_SUFFIX, SerialFormat.MIXED]:
                return self._increment_alphanumeric(serial)
            else:
                self.logger.warning(f"Unknown serial format, cannot increment: {serial}")
                return None
        except Exception as e:
            self.logger.error(f"Error incrementing serial {serial}: {e}")
            return None
        
    def _increment_numeric(self, serial: str) -> str:
        """
        Increment a purely numeric serial number.
        
        Args:
            serial: Numeric serial to increment
            
        Returns:
            Incremented serial number
            
        Raises:
            SeriesGeneratorError: If serial is not numeric or overflow occurs
        """
        if not serial.isdigit():
            raise SeriesGeneratorError(f"Serial is not purely numeric: {serial}")
            
        try:
            # Preserve leading zeros
            original_length = len(serial)
            incremented_value = int(serial) + 1
            return str(incremented_value).zfill(original_length)
        except (ValueError, OverflowError) as e:
            raise SeriesGeneratorError(f"Cannot increment numeric serial {serial}: {e}")
    
    def _increment_alphanumeric(self, serial: str) -> Optional[str]:
        """
        Increment an alphanumeric serial number.
        
        Args:
            serial: Alphanumeric serial to increment
            
        Returns:
            Incremented serial or None if increment failed
        """
        # Strategy 1: Find the last numeric part in the string
        match = re.search(r'(\d+)(?=\D*$)', serial)
        
        if match:
            # Extract the numeric part and increment it
            start, end = match.span()
            numeric_part = match.group(1)
            try:
                incremented = str(int(numeric_part) + 1).zfill(len(numeric_part))
                return serial[:start] + incremented + serial[end:]
            except (ValueError, OverflowError):
                self.logger.warning(f"Numeric overflow in serial: {serial}")
                return None
        
        # Strategy 2: Try to increment the last alphabetic character
        return self._increment_alpha_suffix(serial)
    
    def _increment_alpha_suffix(self, serial: str) -> Optional[str]:
        """
        Increment the alphabetic suffix of a serial number.
        
        Args:
            serial: Serial with alphabetic suffix
            
        Returns:
            Incremented serial or None if increment failed
        """
        if not serial or not serial[-1].isalpha():
            return None
            
        last_char = serial[-1]
        prefix = serial[:-1]
        
        # Handle uppercase letters
        if last_char.isupper():
            if last_char == 'Z':
                # Handle Z -> AA case (carry over)
                return self._handle_alpha_carry(prefix, 'A')
            else:
                return prefix + chr(ord(last_char) + 1)
        
        # Handle lowercase letters
        elif last_char.islower():
            if last_char == 'z':
                # Handle z -> aa case (carry over)
                return self._handle_alpha_carry(prefix, 'a')
            else:
                return prefix + chr(ord(last_char) + 1)
        
        return None
    
    def _handle_alpha_carry(self, prefix: str, base_char: str) -> Optional[str]:
        """
        Handle alphabetic carry-over (e.g., Z -> AA).
        
        Args:
            prefix: The prefix before the carried character
            base_char: The base character ('A' or 'a')
            
        Returns:
            Serial with carry-over applied or None if failed
        """
        if not prefix:
            # Simple case: Z -> AA, z -> aa
            return base_char + base_char
        
        # Try to increment the prefix recursively
        incremented_prefix = self._increment_alpha_suffix(prefix)
        if incremented_prefix is not None:
            return incremented_prefix + base_char
        else:
            # If prefix can't be incremented, add new character
            return prefix + base_char + base_char        

    def detect_serial_format(self, serial: str) -> SerialFormat:
        """
        Detect the format of a serial number.
        
        Args:
            serial: Serial number to analyze
            
        Returns:
            SerialFormat enum indicating the detected format
        """
        if not serial:
            return SerialFormat.UNKNOWN
            
        # Check if purely numeric
        if serial.isdigit():
            return SerialFormat.NUMERIC
            
        # Check if purely alphabetic
        if serial.isalpha():
            return SerialFormat.ALPHA_SUFFIX
            
        # Check for alphanumeric patterns
        has_digits = bool(re.search(r'\d', serial))
        has_letters = bool(re.search(r'[a-zA-Z]', serial))
        
        if has_digits and has_letters:
            # Check if it ends with letters (common pattern)
            if re.search(r'[a-zA-Z]+$', serial):
                return SerialFormat.ALPHA_SUFFIX
            else:
                return SerialFormat.MIXED
        elif has_digits:
            return SerialFormat.NUMERIC
        elif has_letters:
            return SerialFormat.ALPHA_SUFFIX
        else:
            return SerialFormat.UNKNOWN
    
    def validate_serial_format(self, serial: str) -> bool:
        """
        Validate that a serial number can be incremented.
        
        Args:
            serial: Serial number to validate
            
        Returns:
            True if serial can be incremented, False otherwise
        """
        if not serial or not serial.strip():
            return False
            
        serial_format = self.detect_serial_format(serial)
        
        if serial_format == SerialFormat.NUMERIC:
            return True
            
        if serial_format == SerialFormat.ALPHA_SUFFIX:
            # Check if the last character can be incremented
            last_char = serial[-1]
            if last_char.isalpha():
                return True
            return False
            
        if serial_format in [SerialFormat.ALPHANUMERIC, SerialFormat.MIXED]:
            # Check if it contains incrementable parts
            if re.search(r'\d', serial):  # Has digits
                return True
            if serial and serial[-1].isalpha():  # Ends with letter
                return True
            return False
            
        return False
    
    def parse_serial_components(self, serial: str) -> Tuple[str, str, str]:
        """
        Parse a serial number into its components.
        
        Args:
            serial: Serial number to parse
            
        Returns:
            Tuple of (prefix, numeric_part, suffix)
        """
        if not serial:
            return ("", "", "")
            
        # Find the last numeric part
        match = re.search(r'(.*)(\d+)(.*)', serial)
        
        if match:
            prefix = match.group(1) or ""
            numeric_part = match.group(2)
            suffix = match.group(3) or ""
            return (prefix, numeric_part, suffix)
        else:
            # No numeric part found
            return (serial, "", "")
    
    def get_next_serial(self, serial: str) -> Optional[str]:
        """
        Get the next serial number in sequence.
        
        This is an alias for increment_serial for better API clarity.
        
        Args:
            serial: Current serial number
            
        Returns:
            Next serial number or None if increment failed
        """
        return self.increment_serial(serial)
    
    def get_serial_range(self, start_serial: str, end_serial: str) -> List[str]:
        """
        Generate a range of serial numbers from start to end (inclusive).
        
        Args:
            start_serial: Starting serial number
            end_serial: Ending serial number
            
        Returns:
            List of serial numbers in the range
            
        Raises:
            SeriesGeneratorError: If range cannot be generated
        """
        if not self.validate_serial_format(start_serial):
            raise SeriesGeneratorError(f"Invalid start serial format: {start_serial}")
            
        if not self.validate_serial_format(end_serial):
            raise SeriesGeneratorError(f"Invalid end serial format: {end_serial}")
        
        # For now, implement a simple approach by generating series
        # and stopping when we reach the end serial
        series = []
        current = start_serial
        max_iterations = 10000  # Safety limit
        
        while current and len(series) < max_iterations:
            series.append(current)
            if current == end_serial:
                break
            current = self.increment_serial(current)
            
        if current != end_serial and len(series) == max_iterations:
            raise SeriesGeneratorError(f"Range too large or end serial not reachable: {start_serial} to {end_serial}")
            
        return series
    
    def is_valid_serial_character(self, char: str) -> bool:
        """
        Check if a character is valid for use in serial numbers.
        
        Args:
            char: Character to check
            
        Returns:
            True if character is valid for serial numbers
        """
        return char.isalnum() or char in ['-', '_']