"""
Custom exception hierarchy for the publish application.

This module defines application-specific exception classes that provide
better error handling and context information throughout the application.
"""

from typing import Optional, Dict, Any


class PublishError(Exception):
    """
    Base exception for all publishing operations.
    
    This is the root exception class that all other publish-related
    exceptions inherit from. It provides common functionality for
    error context and logging.
    """
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the PublishError.
        
        Args:
            message: Human-readable error message
            context: Additional context information about the error
            cause: The underlying exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.cause = cause
    
    def __str__(self) -> str:
        """Return a string representation of the error."""
        base_msg = self.message
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            base_msg += f" (Context: {context_str})"
        if self.cause:
            base_msg += f" (Caused by: {str(self.cause)})"
        return base_msg
    
    def add_context(self, key: str, value: Any) -> 'PublishError':
        """
        Add context information to the error.
        
        Args:
            key: Context key
            value: Context value
            
        Returns:
            Self for method chaining
        """
        self.context[key] = value
        return self


class ValidationError(PublishError):
    """
    Exception raised when data validation fails.
    
    This exception is used when input data doesn't meet the required
    validation criteria, such as missing required fields or invalid formats.
    """
    
    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None, 
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the ValidationError.
        
        Args:
            message: Human-readable error message
            field: The field that failed validation
            value: The invalid value
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.field = field
        self.value = value
        
        # Add field and value to context if provided
        if field is not None:
            self.add_context("field", field)
        if value is not None:
            self.add_context("value", value)


class E3ConnectionError(PublishError):
    """
    Exception raised when E3 Series connection issues occur.
    
    This exception is used for all E3 Series COM API related errors,
    including connection failures, COM object issues, and API call failures.
    """
    
    def __init__(self, message: str, operation: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the E3ConnectionError.
        
        Args:
            message: Human-readable error message
            operation: The E3 operation that failed
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.operation = operation
        
        if operation is not None:
            self.add_context("operation", operation)


class ExportError(PublishError):
    """
    Exception raised when export operations fail.
    
    This exception is used for PDF and DXF export failures,
    including file system issues and export process errors.
    """
    
    def __init__(self, message: str, export_type: Optional[str] = None, output_path: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the ExportError.
        
        Args:
            message: Human-readable error message
            export_type: The type of export that failed (PDF, DXF, etc.)
            output_path: The intended output path
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.export_type = export_type
        self.output_path = output_path
        
        if export_type is not None:
            self.add_context("export_type", export_type)
        if output_path is not None:
            self.add_context("output_path", output_path)


class ConfigurationError(PublishError):
    """
    Exception raised when configuration issues occur.
    
    This exception is used for configuration loading failures,
    invalid configuration values, and missing configuration files.
    """
    
    def __init__(self, message: str, config_file: Optional[str] = None, config_key: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the ConfigurationError.
        
        Args:
            message: Human-readable error message
            config_file: The configuration file that caused the error
            config_key: The specific configuration key that caused the error
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.config_file = config_file
        self.config_key = config_key
        
        if config_file is not None:
            self.add_context("config_file", config_file)
        if config_key is not None:
            self.add_context("config_key", config_key)


class FileOperationError(PublishError):
    """
    Exception raised when file operations fail.
    
    This exception is used for file system operations like creating directories,
    reading/writing files, and file permission issues.
    """
    
    def __init__(self, message: str, file_path: Optional[str] = None, operation: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the FileOperationError.
        
        Args:
            message: Human-readable error message
            file_path: The file path that caused the error
            operation: The file operation that failed
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.file_path = file_path
        self.operation = operation
        
        if file_path is not None:
            self.add_context("file_path", file_path)
        if operation is not None:
            self.add_context("operation", operation)


class ServiceError(PublishError):
    """
    Exception raised when service layer operations fail.
    
    This exception is used for business logic errors in service classes,
    including orchestration failures and service dependency issues.
    """
    
    def __init__(self, message: str, service: Optional[str] = None, operation: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the ServiceError.
        
        Args:
            message: Human-readable error message
            service: The service class that caused the error
            operation: The service operation that failed
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.service = service
        self.operation = operation
        
        if service is not None:
            self.add_context("service", service)
        if operation is not None:
            self.add_context("operation", operation)


class IntegrationError(PublishError):
    """
    Exception raised when external integration operations fail.
    
    This exception is used for failures in external system integrations,
    including report generator and other external tool failures.
    """
    
    def __init__(self, message: str, integration: Optional[str] = None, operation: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the IntegrationError.
        
        Args:
            message: Human-readable error message
            integration: The integration system that caused the error
            operation: The integration operation that failed
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.integration = integration
        self.operation = operation
        
        if integration is not None:
            self.add_context("integration", integration)
        if operation is not None:
            self.add_context("operation", operation)


class GUIError(PublishError):
    """
    Exception raised when GUI operations fail.
    
    This exception is used for user interface related errors,
    including widget creation failures and user interaction issues.
    """
    
    def __init__(self, message: str, widget: Optional[str] = None, operation: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        """
        Initialize the GUIError.
        
        Args:
            message: Human-readable error message
            widget: The GUI widget that caused the error
            operation: The GUI operation that failed
            context: Additional context information
            cause: The underlying exception that caused this error
        """
        super().__init__(message, context, cause)
        self.widget = widget
        self.operation = operation
        
        if widget is not None:
            self.add_context("widget", widget)
        if operation is not None:
            self.add_context("operation", operation)