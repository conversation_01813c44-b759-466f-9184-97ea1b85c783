"""
Dialog components for the publish application.

This module contains dialog classes for error display, confirmation,
and other user interactions.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from typing import Optional, List, Callable
import logging

from ..exceptions import PublishError


class BaseDialog:
    """
    Base class for custom dialogs.

    Features:
    - Modal behavior with proper focus management
    - Automatic centering on parent window
    - User resizable with minimum size constraints
    - Proper resource cleanup
    """
    
    def __init__(self, parent, title: str = "Dialog", width: int = 400, height: int = 200):
        """
        Initialize the base dialog.
        
        Args:
            parent: Parent window
            title: Dialog title
            width: Dialog width
            height: Dialog height
        """
        self.parent = parent
        self.title = title
        self.width = width
        self.height = height
        self.result = None
        self.dialog: Optional[ctk.CTkToplevel] = None
        self.logger = logging.getLogger(__name__)
        
    def _create_dialog(self) -> ctk.CTkToplevel:
        """Create and configure the dialog window."""
        dialog = ctk.CTkToplevel(self.parent)
        dialog.title(self.title)
        dialog.geometry(f"{self.width}x{self.height}")

        # Make dialog modal
        dialog.transient(self.parent)
        dialog.grab_set()

        # Center dialog on parent
        if self.parent:
            self._center_dialog(dialog)

        # Allow resizing with minimum size constraints
        dialog.resizable(True, True)
        dialog.minsize(300, 150)  # Set minimum size to prevent dialog from becoming too small

        return dialog
        
    def _center_dialog(self, dialog):
        """Center the dialog on the parent window."""
        try:
            # Get parent window position and size
            parent_x = self.parent.winfo_x()
            parent_y = self.parent.winfo_y()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # Calculate center position
            x = parent_x + (parent_width - self.width) // 2
            y = parent_y + (parent_height - self.height) // 2
            
            dialog.geometry(f"{self.width}x{self.height}+{x}+{y}")
        except Exception as e:
            self.logger.debug(f"Could not center dialog: {e}")
            
    def _on_close(self):
        """Handle dialog close event."""
        if self.dialog:
            self.dialog.grab_release()
            self.dialog.destroy()
            self.dialog = None


class ErrorDialog(BaseDialog):
    """Dialog for displaying error messages to users."""
    
    def __init__(self, parent, title: str = "Error", message: str = "", 
                 width: int = 500, height: int = 300):
        """
        Initialize the error dialog.
        
        Args:
            parent: Parent window
            title: Dialog title
            message: Error message to display
            width: Dialog width
            height: Dialog height
        """
        super().__init__(parent, title, width, height)
        self.message = message
        
    def show(self):
        """Display the error dialog."""
        try:
            # For simple error messages, use system message box
            if len(self.message) < 200 and '\n' not in self.message:
                messagebox.showerror(self.title, self.message, parent=self.parent)
                return
                
            # For complex messages, create custom dialog
            self._show_custom_dialog()
            
        except Exception as e:
            self.logger.error(f"Failed to show error dialog: {e}")
            # Fallback to print
            print(f"ERROR: {self.title} - {self.message}")
            
    def _show_custom_dialog(self):
        """Show custom error dialog for complex messages."""
        self.dialog = self._create_dialog()
        
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Error icon and title
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))
        
        error_label = ctk.CTkLabel(
            header_frame, 
            text="⚠️ Error", 
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="red"
        )
        error_label.pack(side="left", padx=5)
        
        # Message text
        text_frame = ctk.CTkFrame(main_frame)
        text_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Use textbox for scrollable text
        text_widget = ctk.CTkTextbox(
            text_frame,
            wrap="word",
            font=ctk.CTkFont(size=12)
        )
        text_widget.pack(fill="both", expand=True, padx=5, pady=5)
        text_widget.insert("1.0", self.message)
        text_widget.configure(state="disabled")  # Make read-only
        
        # Button frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x")
        
        ok_button = ctk.CTkButton(
            button_frame,
            text="OK",
            command=self._on_close,
            width=80
        )
        ok_button.pack(side="right", padx=5)
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Wait for dialog to close
        self.dialog.wait_window()


class MessageDialog(BaseDialog):
    """
    Dialog for displaying informational messages with an OK button.

    Features:
    - Automatic size calculation based on message content
    - User resizable at runtime with responsive text wrapping
    - Supports both simple labels and scrollable textboxes
    - Single OK button for acknowledgment
    """

    def __init__(self, parent, title: str = "Information", message: str = "",
                 width: int = None, height: int = None):
        """
        Initialize the message dialog.

        Args:
            parent: Parent window
            title: Dialog title
            message: Message text to display
            width: Dialog width (auto-calculated if None)
            height: Dialog height (auto-calculated if None)
        """
        # Calculate dynamic size if not provided
        if width is None or height is None:
            calc_width, calc_height = self._calculate_dialog_size(message)
            width = width or calc_width
            height = height or calc_height

        super().__init__(parent, title, width, height)
        self.message = message

        # Initialize widget references for resize handling
        self.text_widget = None
        self.message_label = None
        self.text_frame = None

    def _calculate_dialog_size(self, message: str) -> tuple[int, int]:
        """
        Calculate optimal dialog size based on message content.

        Args:
            message: The message text to display

        Returns:
            Tuple of (width, height) in pixels
        """
        # Base dimensions
        min_width = 350
        max_width = 800
        min_height = 200
        max_height = 600

        # Calculate based on text content
        lines = message.split('\n')
        line_count = len(lines)
        max_line_length = max(len(line) for line in lines) if lines else 0

        # Estimate width based on longest line (roughly 8 pixels per character)
        estimated_width = max_line_length * 8 + 100  # Add padding
        width = max(min_width, min(estimated_width, max_width))

        # Estimate height based on line count
        base_height = 120  # Header, buttons, padding
        text_height = line_count * 20  # Roughly 20 pixels per line

        # Add extra height for long messages that will use textbox
        if len(message) > 300:
            text_height = max(text_height, 150)  # Minimum textbox height

        estimated_height = base_height + text_height
        height = max(min_height, min(estimated_height, max_height))

        return width, height

    def show(self) -> None:
        """Show the message dialog."""
        self._show_message_dialog()

    def _show_message_dialog(self) -> None:
        """Show message dialog with OK button."""
        self.dialog = self._create_dialog()

        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Info icon and title
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        info_label = ctk.CTkLabel(
            header_frame,
            text="ℹ️ Information",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="lightblue"
        )
        info_label.pack(side="left", padx=5)

        # Message text
        text_frame = ctk.CTkFrame(main_frame)
        text_frame.pack(fill="both", expand=True, pady=(0, 10))

        if len(self.message) > 300 or len(self.message.split('\n')) > 8:
            # Use textbox for long messages or many lines
            # Calculate appropriate height based on content
            line_count = len(self.message.split('\n'))
            textbox_height = min(max(line_count * 20, 100), 300)

            self.text_widget = ctk.CTkTextbox(
                text_frame,
                wrap="word",
                font=ctk.CTkFont(size=12),
                height=textbox_height
            )
            self.text_widget.pack(fill="both", expand=True, padx=5, pady=5)
            self.text_widget.insert("1.0", self.message)
            self.text_widget.configure(state="disabled")
        else:
            # Use label for shorter messages with dynamic wrapping
            self.message_label = ctk.CTkLabel(
                text_frame,
                text=self.message,
                font=ctk.CTkFont(size=12),
                wraplength=self.width - 60,  # Initial wrap length
                justify="left"
            )
            self.message_label.pack(fill="both", expand=True, padx=5, pady=5)

            # Bind resize event to update wrap length dynamically
            self.dialog.bind("<Configure>", self._on_dialog_resize)

        # Store reference to text frame for resize handling
        self.text_frame = text_frame

        # Button frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        # OK button
        ok_button = ctk.CTkButton(
            button_frame,
            text="OK",
            command=self._on_ok,
            width=100
        )
        ok_button.pack(side="right", padx=5)

        # Wait for dialog to close
        self.dialog.wait_window()

    def _on_ok(self):
        """Handle OK button click."""
        self.dialog.destroy()

    def _on_dialog_resize(self, event):
        """Handle dialog resize events to update text wrapping."""
        # Only handle resize events for the main dialog window, not child widgets
        if event.widget == self.dialog:
            try:
                # Update wrap length for label if it exists
                if hasattr(self, 'message_label') and self.message_label:
                    new_width = event.width
                    # Calculate new wrap length accounting for padding and borders
                    new_wrap_length = max(200, new_width - 60)  # Minimum wrap of 200px
                    self.message_label.configure(wraplength=new_wrap_length)
            except Exception as e:
                # Silently handle any resize errors to avoid disrupting the dialog
                pass


class ConfirmationDialog(BaseDialog):
    """
    Dialog for user confirmation with dynamic sizing and resizable interface.

    Features:
    - Automatic size calculation based on message content
    - User resizable at runtime with responsive text wrapping
    - Supports both simple labels and scrollable textboxes
    - Returns boolean result for user choice
    """

    def __init__(self, parent, title: str = "Confirm", message: str = "",
                 width: int = None, height: int = None):
        """
        Initialize the confirmation dialog.

        Args:
            parent: Parent window
            title: Dialog title
            message: Confirmation message
            width: Dialog width (auto-calculated if None)
            height: Dialog height (auto-calculated if None)
        """
        # Calculate dynamic size if not provided
        if width is None or height is None:
            calc_width, calc_height = self._calculate_dialog_size(message)
            width = width or calc_width
            height = height or calc_height

        super().__init__(parent, title, width, height)
        self.message = message

        # Initialize widget references for resize handling
        self.text_widget = None
        self.message_label = None
        self.text_frame = None

    def _calculate_dialog_size(self, message: str) -> tuple[int, int]:
        """
        Calculate optimal dialog size based on message content.

        Args:
            message: The message text to display

        Returns:
            Tuple of (width, height) in pixels
        """
        # Base dimensions
        min_width = 350
        max_width = 800
        min_height = 200
        max_height = 600

        # Calculate based on text content
        lines = message.split('\n')
        line_count = len(lines)
        max_line_length = max(len(line) for line in lines) if lines else 0

        # Estimate width based on longest line (roughly 8 pixels per character)
        estimated_width = max_line_length * 8 + 100  # Add padding
        width = max(min_width, min(estimated_width, max_width))

        # Estimate height based on line count
        base_height = 120  # Header, buttons, padding
        text_height = line_count * 20  # Roughly 20 pixels per line

        # Add extra height for long messages that will use textbox
        if len(message) > 300:
            text_height = max(text_height, 150)  # Minimum textbox height

        estimated_height = base_height + text_height
        height = max(min_height, min(estimated_height, max_height))

        return width, height
        
    def show(self) -> bool:
        """
        Display the confirmation dialog.
        
        Returns:
            True if user confirmed, False otherwise
        """
        try:
            # For simple messages, use system message box
            if len(self.message) < 200 and '\n' not in self.message:
                return messagebox.askyesno(self.title, self.message, parent=self.parent)
                
            # For complex messages, create custom dialog
            return self._show_custom_dialog()
            
        except Exception as e:
            self.logger.error(f"Failed to show confirmation dialog: {e}")
            return False
            
    def _show_custom_dialog(self) -> bool:
        """Show custom confirmation dialog for complex messages."""
        self.result = False
        self.dialog = self._create_dialog()

        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Question icon and title
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        question_label = ctk.CTkLabel(
            header_frame,
            text="❓ Confirmation",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="orange"
        )
        question_label.pack(side="left", padx=5)

        # Message text
        text_frame = ctk.CTkFrame(main_frame)
        text_frame.pack(fill="both", expand=True, pady=(0, 10))

        if len(self.message) > 300 or len(self.message.split('\n')) > 8:
            # Use textbox for long messages or many lines
            # Calculate appropriate height based on content
            line_count = len(self.message.split('\n'))
            textbox_height = min(max(line_count * 20, 100), 300)

            self.text_widget = ctk.CTkTextbox(
                text_frame,
                wrap="word",
                font=ctk.CTkFont(size=12),
                height=textbox_height
            )
            self.text_widget.pack(fill="both", expand=True, padx=5, pady=5)
            self.text_widget.insert("1.0", self.message)
            self.text_widget.configure(state="disabled")
        else:
            # Use label for shorter messages with dynamic wrapping
            self.message_label = ctk.CTkLabel(
                text_frame,
                text=self.message,
                font=ctk.CTkFont(size=12),
                wraplength=self.width - 60,  # Initial wrap length
                justify="left"
            )
            self.message_label.pack(fill="both", expand=True, padx=5, pady=5)

            # Bind resize event to update wrap length dynamically
            self.dialog.bind("<Configure>", self._on_dialog_resize)

        # Store reference to text frame for resize handling
        self.text_frame = text_frame
        
        # Button frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x")
        
        # No button
        no_button = ctk.CTkButton(
            button_frame,
            text="No",
            command=self._on_no_clicked,
            width=80,
            fg_color="gray"
        )
        no_button.pack(side="right", padx=(5, 0))
        
        # Yes button
        yes_button = ctk.CTkButton(
            button_frame,
            text="Yes",
            command=self._on_yes_clicked,
            width=80
        )
        yes_button.pack(side="right", padx=5)
        
        # Handle window close (treat as No)
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_no_clicked)
        
        # Wait for dialog to close
        self.dialog.wait_window()

        return self.result

    def _on_dialog_resize(self, event):
        """Handle dialog resize events to update text wrapping."""
        # Only handle resize events for the main dialog window, not child widgets
        if event.widget == self.dialog:
            try:
                # Update wrap length for label if it exists
                if hasattr(self, 'message_label') and self.message_label:
                    new_width = event.width
                    # Calculate new wrap length accounting for padding and borders
                    new_wrap_length = max(200, new_width - 60)  # Minimum wrap of 200px
                    self.message_label.configure(wraplength=new_wrap_length)
            except Exception as e:
                # Silently handle any resize errors to avoid disrupting the dialog
                pass
        
    def _on_yes_clicked(self):
        """Handle Yes button click."""
        self.result = True
        self._on_close()
        
    def _on_no_clicked(self):
        """Handle No button click."""
        self.result = False
        self._on_close()


class ProgressDialog(BaseDialog):
    """Dialog for showing progress during long operations."""
    
    def __init__(self, parent, title: str = "Progress", message: str = "Processing...",
                 width: int = 400, height: int = 150):
        """
        Initialize the progress dialog.
        
        Args:
            parent: Parent window
            title: Dialog title
            message: Progress message
            width: Dialog width
            height: Dialog height
        """
        super().__init__(parent, title, width, height)
        self.message = message
        self.progress_bar: Optional[ctk.CTkProgressBar] = None
        self.message_label: Optional[ctk.CTkLabel] = None
        self.cancel_callback = None
        self.cancelled = False
        
    def show(self, cancel_callback=None):
        """
        Display the progress dialog.
        
        Args:
            cancel_callback: Optional callback for cancel button
        """
        self.cancel_callback = cancel_callback
        self.cancelled = False
        
        self.dialog = self._create_dialog()
        
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Message label
        self.message_label = ctk.CTkLabel(
            main_frame,
            text=self.message,
            font=ctk.CTkFont(size=12)
        )
        self.message_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(main_frame)
        self.progress_bar.pack(fill="x", pady=(0, 10))
        self.progress_bar.set(0)
        
        # Cancel button (if callback provided)
        if cancel_callback:
            cancel_button = ctk.CTkButton(
                main_frame,
                text="Cancel",
                command=self._on_cancel_clicked,
                width=80,
                fg_color="gray"
            )
            cancel_button.pack()
            
        # Prevent closing without cancel
        if not cancel_callback:
            self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        else:
            self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel_clicked)
            
    def update_progress(self, value: float, message: str = None):
        """
        Update the progress bar and message.
        
        Args:
            value: Progress value (0.0 to 1.0)
            message: Optional new message
        """
        if self.progress_bar:
            self.progress_bar.set(value)
            
        if message and self.message_label:
            self.message_label.configure(text=message)
            
        # Update the display
        if self.dialog:
            self.dialog.update()
            
    def _on_cancel_clicked(self):
        """Handle cancel button click."""
        self.cancelled = True
        if self.cancel_callback:
            self.cancel_callback()
        self._on_close()
        
    def is_cancelled(self) -> bool:
        """Check if the operation was cancelled."""
        return self.cancelled
        
    def close(self):
        """Close the progress dialog."""
        self._on_close()


class PublishErrorDialog(ErrorDialog):
    """Specialized dialog for displaying PublishError exceptions with context."""
    
    def __init__(self, parent, error: PublishError, title: str = None):
        """
        Initialize the publish error dialog.
        
        Args:
            parent: Parent window
            error: PublishError exception to display
            title: Optional custom title
        """
        self.error = error
        
        # Determine title based on error type
        if title is None:
            title = self._get_error_title(error)
        
        # Build detailed message
        message = self._build_detailed_message(error)
        
        super().__init__(parent, title, message, width=550, height=350)
    
    def _get_error_title(self, error: PublishError) -> str:
        """Get appropriate title for the error type."""
        error_titles = {
            'ValidationError': 'Validation Error',
            'E3ConnectionError': 'E3 Connection Error',
            'ExportError': 'Export Error',
            'ConfigurationError': 'Configuration Error',
            'FileOperationError': 'File Operation Error',
            'ServiceError': 'Service Error',
            'IntegrationError': 'Integration Error',
            'GUIError': 'Interface Error'
        }
        
        error_type = type(error).__name__
        return error_titles.get(error_type, 'Application Error')
    
    def _build_detailed_message(self, error: PublishError) -> str:
        """Build detailed error message with context."""
        message_parts = [error.message]
        
        # Add context information if available
        if error.context:
            message_parts.append("")
            message_parts.append("Additional Information:")
            for key, value in error.context.items():
                message_parts.append(f"• {key}: {value}")
        
        # Add cause information if available
        if error.cause:
            message_parts.append("")
            message_parts.append(f"Underlying cause: {str(error.cause)}")
        
        return "\n".join(message_parts)


class ValidationErrorDialog(ErrorDialog):
    """Specialized dialog for displaying validation errors."""
    
    def __init__(self, parent, errors: List[str], warnings: List[str] = None):
        """
        Initialize the validation error dialog.
        
        Args:
            parent: Parent window
            errors: List of error messages
            warnings: List of warning messages
        """
        self.errors = errors
        self.warnings = warnings or []
        
        # Build message
        message = self._build_message()
        
        super().__init__(parent, "Validation Errors", message, width=500, height=400)
        
    def _build_message(self) -> str:
        """Build the complete validation message."""
        message_parts = []
        
        if self.errors:
            message_parts.append("The following errors must be fixed:")
            for i, error in enumerate(self.errors, 1):
                message_parts.append(f"{i}. {error}")
                
        if self.warnings:
            if message_parts:
                message_parts.append("")  # Empty line
            message_parts.append("Warnings:")
            for i, warning in enumerate(self.warnings, 1):
                message_parts.append(f"{i}. {warning}")
                
        return "\n".join(message_parts)


class ResultsDialog(BaseDialog):
    """Dialog for displaying operation results."""
    
    def __init__(self, parent, title: str, results: List[dict], 
                 width: int = 600, height: int = 400):
        """
        Initialize the results dialog.
        
        Args:
            parent: Parent window
            title: Dialog title
            results: List of result dictionaries
            width: Dialog width
            height: Dialog height
        """
        super().__init__(parent, title, width, height)
        self.results = results
        
    def show(self):
        """Display the results dialog."""
        self.dialog = self._create_dialog()
        
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Results summary
        summary_frame = ctk.CTkFrame(main_frame)
        summary_frame.pack(fill="x", pady=(0, 10))
        
        successful = sum(1 for r in self.results if r.get('success', False))
        total = len(self.results)
        
        summary_text = f"Results: {successful}/{total} successful"
        summary_label = ctk.CTkLabel(
            summary_frame,
            text=summary_text,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        summary_label.pack(pady=5)
        
        # Results list
        results_frame = ctk.CTkFrame(main_frame)
        results_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Use textbox for scrollable results
        results_text = ctk.CTkTextbox(
            results_frame,
            wrap="word",
            font=ctk.CTkFont(size=11)
        )
        results_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Build results text
        results_content = self._build_results_text()
        results_text.insert("1.0", results_content)
        results_text.configure(state="disabled")
        
        # Close button
        close_button = ctk.CTkButton(
            main_frame,
            text="Close",
            command=self._on_close,
            width=80
        )
        close_button.pack()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def _build_results_text(self) -> str:
        """Build the results text content."""
        lines = []
        
        for i, result in enumerate(self.results, 1):
            status = "✓ SUCCESS" if result.get('success', False) else "✗ FAILED"
            serial = result.get('serial_number', f'Item {i}')
            
            lines.append(f"{i}. {serial} - {status}")
            
            # Add error details for failed items
            if not result.get('success', False):
                errors = result.get('errors', [])
                for error in errors[:3]:  # Show first 3 errors
                    lines.append(f"   • {error}")
                if len(errors) > 3:
                    lines.append(f"   • ... and {len(errors) - 3} more errors")
                    
            lines.append("")  # Empty line between results
            
        return "\n".join(lines)