"""
Tests for GUI widgets.

This module contains unit tests for the custom GUI widgets.
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock customtkinter before importing our modules
sys.modules['customtkinter'] = Mock()

from apps.publish.gui.widgets import ModelDropdown, SeriesControls, FormField


class TestModelDropdown(unittest.TestCase):
    """Test cases for ModelDropdown widget."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parent = Mock()
        
    def test_initialization(self):
        """Test ModelDropdown initialization."""
        # Test with default values
        dropdown = ModelDropdown(self.parent)
        self.assertIsNotNone(dropdown)
        
        # Test with custom values
        values = ["Model1", "Model2"]
        dropdown = ModelDropdown(self.parent, values=values)
        self.assertIsNotNone(dropdown)
        
    def test_update_models(self):
        """Test updating models in dropdown."""
        dropdown = ModelDropdown(self.parent)
        
        # Test with models
        models = ["Model1", "Model2", "Model3"]
        dropdown.update_models(models)
        
        # Test with empty models
        dropdown.update_models([])
        
    def test_get_selected_model(self):
        """Test getting selected model."""
        dropdown = ModelDropdown(self.parent)
        
        # Mock the get method
        dropdown.get = Mock(return_value="Model1")
        result = dropdown.get_selected_model()
        self.assertEqual(result, "Model1")
        
        # Test with placeholder values
        dropdown.get = Mock(return_value="Select GSS Parent #")
        result = dropdown.get_selected_model()
        self.assertEqual(result, "")
        
    def test_has_valid_selection(self):
        """Test checking for valid selection."""
        dropdown = ModelDropdown(self.parent)
        
        # Mock valid selection
        dropdown.get = Mock(return_value="Model1")
        self.assertTrue(dropdown.has_valid_selection())
        
        # Mock invalid selection
        dropdown.get = Mock(return_value="Select GSS Parent #")
        self.assertFalse(dropdown.has_valid_selection())


class TestSeriesControls(unittest.TestCase):
    """Test cases for SeriesControls widget."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parent = Mock()
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_initialization(self, mock_ctk):
        """Test SeriesControls initialization."""
        # Mock the CTk components
        mock_ctk.BooleanVar.return_value = Mock()
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkCheckBox.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = Mock()
        
        controls = SeriesControls(self.parent)
        self.assertIsNotNone(controls)
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_get_create_manual(self, mock_ctk):
        """Test getting create manual setting."""
        mock_var = Mock()
        mock_var.get.return_value = True
        mock_ctk.BooleanVar.return_value = mock_var
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkCheckBox.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = Mock()
        
        controls = SeriesControls(self.parent)
        result = controls.get_create_manual()
        self.assertTrue(result)
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_get_series_count(self, mock_ctk):
        """Test getting series count."""
        mock_entry = Mock()
        mock_entry.get.return_value = "5"
        mock_ctk.BooleanVar.return_value = Mock()
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkCheckBox.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = mock_entry
        
        controls = SeriesControls(self.parent)
        controls.series_count_entry = mock_entry
        
        result = controls.get_series_count()
        self.assertEqual(result, 5)
        
        # Test with invalid input
        mock_entry.get.return_value = "invalid"
        result = controls.get_series_count()
        self.assertEqual(result, 1)  # Should default to 1
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_validate_series_count(self, mock_ctk):
        """Test series count validation."""
        mock_entry = Mock()
        mock_var = Mock()
        mock_var.get.return_value = True  # Series enabled
        mock_ctk.BooleanVar.return_value = mock_var
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkCheckBox.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = mock_entry
        
        controls = SeriesControls(self.parent)
        controls.series_count_entry = mock_entry
        controls.fill_series_var = mock_var
        
        # Test valid count
        mock_entry.get.return_value = "5"
        is_valid, message = controls.validate_series_count()
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
        
        # Test invalid count
        mock_entry.get.return_value = "0"
        is_valid, message = controls.validate_series_count()
        self.assertFalse(is_valid)
        self.assertIn("at least 1", message)
        
        # Test non-numeric input
        mock_entry.get.return_value = "abc"
        is_valid, message = controls.validate_series_count()
        self.assertFalse(is_valid)
        self.assertIn("valid number", message)


class TestFormField(unittest.TestCase):
    """Test cases for FormField widget."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parent = Mock()
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_initialization(self, mock_ctk):
        """Test FormField initialization."""
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = Mock()
        
        field = FormField(self.parent, "Test Label")
        self.assertIsNotNone(field)
        self.assertEqual(field.label_text, "Test Label")
        
    @patch('apps.publish.gui.widgets.ctk')
    def test_get_set_methods(self, mock_ctk):
        """Test get and set methods."""
        mock_entry = Mock()
        mock_entry.get.return_value = "test value"
        mock_ctk.CTkFrame.return_value = Mock()
        mock_ctk.CTkLabel.return_value = Mock()
        mock_ctk.CTkEntry.return_value = mock_entry
        
        field = FormField(self.parent, "Test Label")
        field.entry = mock_entry
        
        # Test get
        result = field.get()
        self.assertEqual(result, "test value")
        
        # Test set
        field.set("new value")
        mock_entry.delete.assert_called_with(0, 'end')
        mock_entry.insert.assert_called_with(0, "new value")
        
        # Test clear
        field.clear()
        mock_entry.delete.assert_called_with(0, 'end')


if __name__ == '__main__':
    unittest.main()