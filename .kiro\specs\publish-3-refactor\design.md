# Design Document

## Overview

The refactoring of `publish_3.py` will transform a monolithic 700+ line script into a well-structured, maintainable application following the Model-View-Controller (MVC) architectural pattern. The design emphasizes separation of concerns, testability, and adherence to Python best practices.

The current script combines GUI logic, business operations, data management, and external integrations in a single file. The refactored solution will separate these concerns into distinct modules with clear interfaces and responsibilities.

## Architecture

### High-Level Architecture

The application will follow a layered architecture with the following components:

```
┌─────────────────────────────────────────┐
│              Presentation Layer          │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │   GUI Module    │ │  CLI Interface  │ │
│  │  (publish_gui)  │ │   (optional)    │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             Business Layer              │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ Publish Service │ │ Model Service   │ │
│  │                 │ │                 │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              Data Layer                 │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ E3 Integration  │ │ File Operations │ │
│  │                 │ │                 │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
```

### Module Structure

```
apps/
├── publish_3.py                    # Main entry point (minimal)
├── publish/                        # New package directory
│   ├── __init__.py
│   ├── gui/                        # GUI components
│   │   ├── __init__.py
│   │   ├── main_window.py          # Main application window
│   │   ├── widgets.py              # Custom widgets
│   │   └── dialogs.py              # Dialog components
│   ├── services/                   # Business logic services
│   │   ├── __init__.py
│   │   ├── publish_service.py      # Core publishing logic
│   │   ├── model_service.py        # Model management
│   │   ├── export_service.py       # PDF/DXF export logic
│   │   └── manual_service.py       # Manual creation logic
│   ├── models/                     # Data models
│   │   ├── __init__.py
│   │   ├── project_data.py         # Project data model
│   │   ├── publish_config.py       # Publishing configuration
│   │   └── validation.py           # Data validation
│   ├── integrations/               # External system integrations
│   │   ├── __init__.py
│   │   ├── e3_client.py           # E3 Series COM integration
│   │   └── report_generator.py     # Report generator integration
│   └── utils/                      # Utility functions
│       ├── __init__.py
│       ├── file_operations.py      # File and folder operations
│       └── series_generator.py     # Series number generation
```

## Components and Interfaces

### 1. Main Entry Point (`publish_3.py`)

**Responsibility:** Application initialization and orchestration

```python
class PublishApplication:
    def __init__(self):
        self.config = load_configuration()
        self.setup_logging()
        
    def run(self):
        # Initialize and run GUI
        
    def setup_logging(self):
        # Configure logging
```

### 2. GUI Layer (`gui/`)

#### MainWindow (`gui/main_window.py`)

**Responsibility:** Main application window and user interaction

```python
class MainWindow:
    def __init__(self, publish_service: PublishService):
        self.publish_service = publish_service
        self.setup_ui()
        
    def setup_ui(self):
        # Create form fields, buttons, etc.
        
    def on_publish_clicked(self):
        # Delegate to service layer
```

#### Custom Widgets (`gui/widgets.py`)

**Responsibility:** Reusable UI components

```python
class ModelDropdown(ctk.CTkOptionMenu):
    # Custom dropdown for model selection
    
class SeriesControls(ctk.CTkFrame):
    # Series publishing controls
```

### 3. Service Layer (`services/`)

#### PublishService (`services/publish_service.py`)

**Responsibility:** Core publishing orchestration

```python
class PublishService:
    def __init__(self, e3_client: E3Client, export_service: ExportService):
        self.e3_client = e3_client
        self.export_service = export_service
        
    def publish_project(self, project_data: ProjectData) -> PublishResult:
        # Orchestrate the publishing process
        
    def publish_series(self, project_data: ProjectData, count: int) -> List[PublishResult]:
        # Handle series publishing
```

#### ModelService (`services/model_service.py`)

**Responsibility:** Model data management and lookup

```python
class ModelService:
    def __init__(self, models_config: Dict):
        self.models = models_config
        
    def get_models_for_gss(self, gss_number: str) -> List[str]:
        # Find models matching GSS parent number
        
    def get_model_data(self, model: str) -> ModelData:
        # Get model configuration data
```

#### ExportService (`services/export_service.py`)

**Responsibility:** PDF and DXF export operations

```python
class ExportService:
    def __init__(self, e3_client: E3Client):
        self.e3_client = e3_client
        
    def export_to_pdf(self, project_data: ProjectData, output_path: str) -> bool:
        # Export project to PDF
        
    def export_to_dxf(self, project_data: ProjectData, output_path: str) -> bool:
        # Export dataplates to DXF
```

### 4. Data Models (`models/`)

#### ProjectData (`models/project_data.py`)

**Responsibility:** Project information data structure

```python
@dataclass
class ProjectData:
    gss_parent: str
    serial_number: str
    customer: str
    location: str
    title: str
    sales_order: str
    model: str
    folder_path: str
    
    def validate(self) -> ValidationResult:
        # Validate project data
```

#### PublishConfig (`models/publish_config.py`)

**Responsibility:** Publishing configuration settings

```python
@dataclass
class PublishConfig:
    create_manual: bool
    fill_series: bool
    series_count: int
    export_formats: List[str]
```

### 5. Integration Layer (`integrations/`)

#### E3Client (`integrations/e3_client.py`)

**Responsibility:** E3 Series COM API wrapper

```python
class E3Client:
    def __init__(self):
        self.app = None
        self.job = None
        
    def connect(self) -> bool:
        # Establish E3 connection
        
    def read_title_block_data(self) -> TitleBlockData:
        # Read project title block
        
    def update_attributes(self, project_data: ProjectData):
        # Update project attributes
        
    def __enter__(self):
        # Context manager entry
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Proper resource cleanup
```

### 6. Utility Layer (`utils/`)

#### FileOperations (`utils/file_operations.py`)

**Responsibility:** File and folder management

```python
class FileOperations:
    @staticmethod
    def create_serial_folder(base_path: str, serial: str) -> str:
        # Create folder for serial number
        
    @staticmethod
    def save_job_data_json(project_data: ProjectData, output_path: str) -> bool:
        # Save project data to JSON
```

## Data Models

### Core Data Structures

```python
@dataclass
class TitleBlockData:
    document_number: str
    model: str
    description: str
    customer: str
    location: str
    sales_order: str
    serial_number: str

@dataclass
class ModelData:
    template_path: str
    drawings_path: str
    asme_flag: bool
    controls_parent: str

@dataclass
class PublishResult:
    success: bool
    serial_number: str
    output_path: str
    errors: List[str]
    warnings: List[str]
```

## Error Handling

### Centralized Error Management

```python
class PublishError(Exception):
    """Base exception for publishing operations"""
    pass

class E3ConnectionError(PublishError):
    """E3 Series connection issues"""
    pass

class ExportError(PublishError):
    """Export operation failures"""
    pass

class ValidationError(PublishError):
    """Data validation failures"""
    pass
```

### Error Handling Strategy

1. **Service Layer**: Catch and wrap exceptions with context
2. **GUI Layer**: Display user-friendly error messages
3. **Logging**: Comprehensive error logging with stack traces
4. **Recovery**: Graceful degradation where possible

## Testing Strategy

### Unit Testing Structure

```
test/
├── test_services/
│   ├── test_publish_service.py
│   ├── test_model_service.py
│   └── test_export_service.py
├── test_models/
│   ├── test_project_data.py
│   └── test_validation.py
├── test_integrations/
│   └── test_e3_client.py
└── test_utils/
    └── test_file_operations.py
```

### Testing Approach

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Mock Objects**: Mock external dependencies (E3 COM, file system)
4. **Test Data**: Use fixtures for consistent test data
5. **Coverage**: Aim for >80% code coverage

### Example Test Structure

```python
class TestPublishService:
    def setup_method(self):
        self.mock_e3_client = Mock(spec=E3Client)
        self.mock_export_service = Mock(spec=ExportService)
        self.service = PublishService(self.mock_e3_client, self.mock_export_service)
        
    def test_publish_project_success(self):
        # Test successful publishing
        
    def test_publish_project_validation_error(self):
        # Test validation error handling
```

## Configuration Management

### Configuration Files

1. **Application Config** (`config.json`): General application settings
2. **Models Config** (`models.json`): Model definitions and paths
3. **Logging Config**: Logging configuration
4. **Theme Config**: UI theme settings

### Configuration Loading

```python
class ConfigurationManager:
    def __init__(self):
        self.app_config = self.load_app_config()
        self.models_config = self.load_models_config()
        
    def load_app_config(self) -> Dict:
        # Load application configuration
        
    def reload_config(self):
        # Reload configuration at runtime
```

## Dependency Injection

### Service Container

```python
class ServiceContainer:
    def __init__(self):
        self._services = {}
        self._configure_services()
        
    def _configure_services(self):
        # Configure service dependencies
        
    def get_service(self, service_type: Type[T]) -> T:
        # Retrieve service instance
```

## Migration Strategy

### Phase 1: Extract Core Services
- Create service layer classes
- Move business logic from GUI class
- Maintain existing GUI structure

### Phase 2: Refactor Data Models
- Create data model classes
- Replace direct attribute access
- Add validation logic

### Phase 3: Improve Integration Layer
- Wrap E3 COM API
- Add proper resource management
- Implement error handling

### Phase 4: Enhance GUI
- Separate GUI from business logic
- Create reusable components
- Improve user experience

### Phase 5: Add Testing
- Create unit tests for services
- Add integration tests
- Set up continuous testing

## Performance Considerations

1. **Lazy Loading**: Load models configuration only when needed
2. **Connection Pooling**: Reuse E3 connections where possible
3. **Async Operations**: Consider async for long-running operations
4. **Memory Management**: Proper cleanup of COM objects
5. **Caching**: Cache frequently accessed data

## Security Considerations

1. **Path Validation**: Validate all file paths to prevent directory traversal
2. **Input Sanitization**: Sanitize user inputs
3. **Error Information**: Avoid exposing sensitive information in error messages
4. **File Permissions**: Ensure proper file access permissions