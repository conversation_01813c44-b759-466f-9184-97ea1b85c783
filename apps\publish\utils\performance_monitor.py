"""
Performance monitoring utilities for tracking operation timing and progress.

This module provides decorators and context managers for monitoring
performance of long-running operations and reporting progress.
"""

import time
import logging
import threading
from contextlib import contextmanager
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from functools import wraps


@dataclass
class PerformanceMetric:
    """Performance metric data structure."""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def complete(self, success: bool = True, error_message: str = None):
        """Mark the metric as completed."""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
    
    def __str__(self):
        status = "SUCCESS" if self.success else "FAILED"
        duration_str = f"{self.duration:.2f}s" if self.duration else "RUNNING"
        return f"{self.operation_name}: {status} ({duration_str})"


class ProgressReporter:
    """Progress reporter for long-running operations."""
    
    def __init__(self, total_items: int, operation_name: str = "Operation"):
        """
        Initialize progress reporter.
        
        Args:
            total_items: Total number of items to process
            operation_name: Name of the operation
        """
        self.total_items = total_items
        self.operation_name = operation_name
        self.completed_items = 0
        self.start_time = time.time()
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._last_report_time = 0
        self._report_interval = 5.0  # Report every 5 seconds
        
    def update(self, increment: int = 1, item_name: str = None):
        """
        Update progress.
        
        Args:
            increment: Number of items completed
            item_name: Name of the current item (optional)
        """
        with self._lock:
            self.completed_items += increment
            current_time = time.time()
            
            # Report progress at intervals or on completion
            should_report = (
                current_time - self._last_report_time >= self._report_interval or
                self.completed_items >= self.total_items
            )
            
            if should_report:
                self._report_progress(item_name)
                self._last_report_time = current_time
    
    def _report_progress(self, item_name: str = None):
        """Report current progress."""
        percentage = (self.completed_items / self.total_items) * 100
        elapsed_time = time.time() - self.start_time
        
        # Estimate remaining time
        if self.completed_items > 0:
            avg_time_per_item = elapsed_time / self.completed_items
            remaining_items = self.total_items - self.completed_items
            estimated_remaining = avg_time_per_item * remaining_items
        else:
            estimated_remaining = 0
        
        progress_msg = (
            f"{self.operation_name}: {self.completed_items}/{self.total_items} "
            f"({percentage:.1f}%) - Elapsed: {elapsed_time:.1f}s"
        )
        
        if estimated_remaining > 0:
            progress_msg += f", ETA: {estimated_remaining:.1f}s"
        
        if item_name:
            progress_msg += f" - Current: {item_name}"
        
        self.logger.info(progress_msg)
    
    def complete(self):
        """Mark progress as completed."""
        with self._lock:
            self.completed_items = self.total_items
            elapsed_time = time.time() - self.start_time
            self.logger.info(
                f"{self.operation_name}: Completed {self.total_items} items "
                f"in {elapsed_time:.1f}s ({self.total_items/elapsed_time:.1f} items/sec)"
            )


class PerformanceMonitor:
    """Central performance monitoring system."""
    
    def __init__(self):
        """Initialize performance monitor."""
        self.metrics: List[PerformanceMetric] = []
        self.active_metrics: Dict[str, PerformanceMetric] = {}
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        
    def start_operation(self, operation_name: str, metadata: Dict[str, Any] = None) -> str:
        """
        Start monitoring an operation.
        
        Args:
            operation_name: Name of the operation
            metadata: Additional metadata
            
        Returns:
            Operation ID for tracking
        """
        with self._lock:
            # Create unique operation ID
            operation_id = f"{operation_name}_{int(time.time() * 1000)}"
            
            metric = PerformanceMetric(
                operation_name=operation_name,
                start_time=time.time(),
                metadata=metadata or {}
            )
            
            self.active_metrics[operation_id] = metric
            self.logger.debug(f"Started monitoring operation: {operation_name} (ID: {operation_id})")
            
            return operation_id
    
    def complete_operation(self, operation_id: str, success: bool = True, 
                          error_message: str = None):
        """
        Complete monitoring an operation.
        
        Args:
            operation_id: Operation ID from start_operation
            success: Whether the operation succeeded
            error_message: Error message if failed
        """
        with self._lock:
            if operation_id in self.active_metrics:
                metric = self.active_metrics.pop(operation_id)
                metric.complete(success, error_message)
                self.metrics.append(metric)
                
                # Log completion
                if success:
                    self.logger.info(f"Operation completed: {metric}")
                else:
                    self.logger.error(f"Operation failed: {metric} - {error_message}")
            else:
                self.logger.warning(f"Unknown operation ID: {operation_id}")
    
    def get_metrics(self, operation_name: str = None) -> List[PerformanceMetric]:
        """
        Get performance metrics.
        
        Args:
            operation_name: Filter by operation name (optional)
            
        Returns:
            List of performance metrics
        """
        with self._lock:
            if operation_name:
                return [m for m in self.metrics if m.operation_name == operation_name]
            return self.metrics.copy()
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Get performance summary.
        
        Returns:
            Dictionary with performance statistics
        """
        with self._lock:
            if not self.metrics:
                return {"total_operations": 0}
            
            successful_ops = [m for m in self.metrics if m.success]
            failed_ops = [m for m in self.metrics if not m.success]
            
            durations = [m.duration for m in self.metrics if m.duration is not None]
            
            summary = {
                "total_operations": len(self.metrics),
                "successful_operations": len(successful_ops),
                "failed_operations": len(failed_ops),
                "success_rate": len(successful_ops) / len(self.metrics) * 100,
                "active_operations": len(self.active_metrics)
            }
            
            if durations:
                summary.update({
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "total_duration": sum(durations)
                })
            
            return summary
    
    def log_summary(self):
        """Log performance summary."""
        summary = self.get_summary()
        
        if summary["total_operations"] == 0:
            self.logger.info("No operations recorded")
            return
        
        self.logger.info(
            f"Performance Summary: {summary['total_operations']} operations, "
            f"{summary['success_rate']:.1f}% success rate"
        )
        
        if "avg_duration" in summary:
            self.logger.info(
                f"Timing: Avg={summary['avg_duration']:.2f}s, "
                f"Min={summary['min_duration']:.2f}s, "
                f"Max={summary['max_duration']:.2f}s"
            )
        
        if summary["active_operations"] > 0:
            self.logger.info(f"Active operations: {summary['active_operations']}")
    
    def clear_metrics(self):
        """Clear all stored metrics."""
        with self._lock:
            self.metrics.clear()
            self.logger.info("Performance metrics cleared")


# Global performance monitor instance
_global_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = PerformanceMonitor()
    return _global_performance_monitor


@contextmanager
def timed_operation(operation_name: str, metadata: Dict[str, Any] = None):
    """
    Context manager for timing operations.
    
    Args:
        operation_name: Name of the operation
        metadata: Additional metadata
        
    Yields:
        PerformanceMetric object
    """
    monitor = get_performance_monitor()
    operation_id = monitor.start_operation(operation_name, metadata)
    
    try:
        # Get the metric object for yielding
        metric = monitor.active_metrics[operation_id]
        yield metric
        monitor.complete_operation(operation_id, success=True)
    except Exception as e:
        monitor.complete_operation(operation_id, success=False, error_message=str(e))
        raise


def timed_function(operation_name: str = None):
    """
    Decorator for timing function execution.
    
    Args:
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            with timed_operation(op_name):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


@contextmanager
def progress_tracking(total_items: int, operation_name: str = "Operation"):
    """
    Context manager for progress tracking.
    
    Args:
        total_items: Total number of items to process
        operation_name: Name of the operation
        
    Yields:
        ProgressReporter instance
    """
    reporter = ProgressReporter(total_items, operation_name)
    
    try:
        yield reporter
    finally:
        reporter.complete()


class FileIOOptimizer:
    """Utilities for optimizing file I/O operations."""
    
    @staticmethod
    def batch_file_operations(file_operations: List[Callable], batch_size: int = 10):
        """
        Execute file operations in batches to optimize I/O.
        
        Args:
            file_operations: List of file operation functions
            batch_size: Number of operations per batch
        """
        logger = logging.getLogger(__name__)
        
        with progress_tracking(len(file_operations), "File Operations") as progress:
            for i in range(0, len(file_operations), batch_size):
                batch = file_operations[i:i + batch_size]
                
                with timed_operation(f"File Batch {i//batch_size + 1}"):
                    for operation in batch:
                        try:
                            operation()
                            progress.update(1)
                        except Exception as e:
                            logger.error(f"File operation failed: {e}")
                            progress.update(1)  # Still count as processed
    
    @staticmethod
    def optimize_json_writes(data_list: List[Dict], output_files: List[str]):
        """
        Optimize multiple JSON file writes.
        
        Args:
            data_list: List of data to write
            output_files: List of output file paths
        """
        if len(data_list) != len(output_files):
            raise ValueError("Data list and output files must have same length")
        
        logger = logging.getLogger(__name__)
        
        with progress_tracking(len(data_list), "JSON File Writes") as progress:
            for data, output_file in zip(data_list, output_files):
                try:
                    with timed_operation(f"Write JSON: {output_file}"):
                        import json
                        with open(output_file, 'w') as f:
                            json.dump(data, f, indent=2)
                        progress.update(1, output_file)
                except Exception as e:
                    logger.error(f"Failed to write JSON file {output_file}: {e}")
                    progress.update(1, output_file)


def log_performance_summary():
    """Log performance summary for all operations."""
    monitor = get_performance_monitor()
    monitor.log_summary()


def clear_performance_metrics():
    """Clear all performance metrics."""
    monitor = get_performance_monitor()
    monitor.clear_metrics()