"""
Unit tests for the error handling system.
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from apps.publish.error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GUIError<PERSON>andler, ErrorMiddleware, ErrorRecovery,
    error_handler_decorator, gui_error_handler
)
from apps.publish.exceptions import (
    PublishError, ValidationError, E3ConnectionError, ExportError,
    ConfigurationError, FileOperationError, ServiceError
)


class TestErrorHandler:
    """Test the ErrorHandler class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_logger = Mock(spec=logging.Logger)
        self.error_handler = ErrorHandler(self.mock_logger)
    
    def test_initialization(self):
        """Test error handler initialization."""
        handler = ErrorHandler()
        assert handler.logger is not None
        assert handler._error_message_map is not None
        assert len(handler._error_message_map) > 0
    
    def test_handle_validation_error(self):
        """Test handling validation errors."""
        error = ValidationError("Invalid field value", field="username", value="")
        message = self.error_handler.handle_error(error)
        
        assert "invalid" in message.lower()
        self.mock_logger.warning.assert_called_once()
    
    def test_handle_e3_connection_error(self):
        """Test handling E3 connection errors."""
        error = E3ConnectionError("Failed to connect", operation="GetApplication")
        message = self.error_handler.handle_error(error)
        
        assert "e3" in message.lower()
        self.mock_logger.error.assert_called_once()
    
    def test_handle_error_with_context(self):
        """Test handling errors with additional context."""
        error = ExportError("Export failed")
        context = {"file_path": "/test/path", "operation": "pdf_export"}
        
        message = self.error_handler.handle_error(error, context)
        
        # Check that context was logged
        log_call = self.mock_logger.error.call_args[0][0]
        assert "file_path=/test/path" in log_call
        assert "operation=pdf_export" in log_call
    
    def test_handle_publish_error_with_context(self):
        """Test handling PublishError with its own context."""
        error = PublishError("Test error")
        error.add_context("test_key", "test_value")
        
        message = self.error_handler.handle_error(error)
        
        # Check that error context was logged
        log_call = self.mock_logger.error.call_args[0][0]
        assert "test_key=test_value" in log_call
    
    def test_get_user_message_for_known_error(self):
        """Test getting user message for known error types."""
        error = ValidationError("Test validation error")
        message = self.error_handler._get_user_message(error)
        
        assert "invalid" in message.lower()
        assert "check your input" in message.lower()
    
    def test_get_user_message_for_unknown_error(self):
        """Test getting user message for unknown error types."""
        error = RuntimeError("Unknown error")
        message = self.error_handler._get_user_message(error)
        
        assert "unexpected error" in message.lower()
    
    def test_wrap_with_error_handling_success(self):
        """Test wrapping function with error handling - success case."""
        def test_function(x, y):
            return x + y
        
        wrapped = self.error_handler.wrap_with_error_handling(test_function)
        result = wrapped(2, 3)
        
        assert result == 5
        self.mock_logger.error.assert_not_called()
    
    def test_wrap_with_error_handling_with_exception(self):
        """Test wrapping function with error handling - exception case."""
        def test_function():
            raise ValueError("Test error")
        
        error_callback = Mock()
        wrapped = self.error_handler.wrap_with_error_handling(test_function, error_callback)
        result = wrapped()
        
        assert result is None
        error_callback.assert_called_once()
        self.mock_logger.error.assert_called()
    
    def test_wrap_with_error_handling_no_callback(self):
        """Test wrapping function without error callback."""
        def test_function():
            raise ValueError("Test error")
        
        wrapped = self.error_handler.wrap_with_error_handling(test_function)
        result = wrapped()
        
        assert result is None
        # Should log the unhandled error
        assert self.mock_logger.error.call_count >= 1


class TestGUIErrorHandler:
    """Test the GUIErrorHandler class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_logger = Mock(spec=logging.Logger)
        self.mock_show_dialog = Mock()
        self.gui_handler = GUIErrorHandler(self.mock_logger, self.mock_show_dialog)
    
    def test_initialization(self):
        """Test GUI error handler initialization."""
        handler = GUIErrorHandler()
        assert handler.logger is not None
        assert handler.show_error_dialog is None
    
    def test_handle_gui_error_with_dialog(self):
        """Test handling GUI error with dialog callback."""
        error = ValidationError("Test error")
        self.gui_handler.handle_gui_error(error, "Test Title")
        
        self.mock_show_dialog.assert_called_once()
        call_args = self.mock_show_dialog.call_args[0]
        assert call_args[0] == "Test Title"
        assert "invalid" in call_args[1].lower()
    
    def test_handle_gui_error_without_dialog(self):
        """Test handling GUI error without dialog callback."""
        handler = GUIErrorHandler(self.mock_logger)
        error = ValidationError("Test error")
        
        handler.handle_gui_error(error, "Test Title")
        
        # Should log warning about missing dialog
        self.mock_logger.warning.assert_called()
    
    def test_wrap_gui_operation_success(self):
        """Test wrapping GUI operation - success case."""
        def test_operation():
            return "success"
        
        wrapped = self.gui_handler.wrap_gui_operation(test_operation, "Test Operation")
        result = wrapped()
        
        assert result == "success"
        self.mock_show_dialog.assert_not_called()
    
    def test_wrap_gui_operation_with_error(self):
        """Test wrapping GUI operation - error case."""
        def test_operation():
            raise ValidationError("Test error")
        
        wrapped = self.gui_handler.wrap_gui_operation(test_operation, "Test Operation")
        result = wrapped()
        
        assert result is None
        self.mock_show_dialog.assert_called_once()


class TestErrorMiddleware:
    """Test the ErrorMiddleware class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_logger = Mock(spec=logging.Logger)
        self.middleware = ErrorMiddleware(self.mock_logger)
        self.mock_gui_handler = Mock(spec=GUIErrorHandler)
        self.middleware.set_gui_error_handler(self.mock_gui_handler)
    
    def test_initialization(self):
        """Test error middleware initialization."""
        middleware = ErrorMiddleware()
        assert middleware.logger is not None
        assert middleware.error_handler is not None
        assert middleware.gui_error_handler is None
    
    def test_set_gui_error_handler(self):
        """Test setting GUI error handler."""
        handler = Mock(spec=GUIErrorHandler)
        self.middleware.set_gui_error_handler(handler)
        assert self.middleware.gui_error_handler == handler
    
    def test_handle_operation_error_with_dialog(self):
        """Test handling operation error with dialog display."""
        error = ValidationError("Test error")
        
        self.middleware.handle_operation_error(error, "Test Operation", show_dialog=True)
        
        # Should call GUI error handler
        self.mock_gui_handler.handle_gui_error.assert_called_once()
    
    def test_handle_operation_error_without_dialog(self):
        """Test handling operation error without dialog display."""
        error = ValidationError("Test error")
        
        self.middleware.handle_operation_error(error, "Test Operation", show_dialog=False)
        
        # Should not call GUI error handler
        self.mock_gui_handler.handle_gui_error.assert_not_called()
    
    def test_handle_publish_error_with_specialized_dialog(self):
        """Test handling PublishError with specialized dialog."""
        error = PublishError("Test publish error")
        
        # This should not raise an exception even if dialog import fails
        self.middleware.handle_operation_error(error, "Test Operation")
        
        # Should have attempted to handle the error
        assert self.mock_logger.error.called or self.mock_logger.warning.called
    
    def test_wrap_gui_method_success(self):
        """Test wrapping GUI method - success case."""
        def test_method(self, x):
            return x * 2
        
        wrapped = self.middleware.wrap_gui_method(test_method, "Test Method")
        result = wrapped(None, 5)
        
        assert result == 10
        self.mock_gui_handler.handle_gui_error.assert_not_called()
    
    def test_wrap_gui_method_with_error(self):
        """Test wrapping GUI method - error case."""
        def test_method(self):
            raise ValidationError("Test error")
        
        wrapped = self.middleware.wrap_gui_method(test_method, "Test Method")
        result = wrapped(None)
        
        assert result is None
        # Should handle the error through middleware
        assert self.mock_logger.error.called or self.mock_logger.warning.called


class TestErrorRecovery:
    """Test the ErrorRecovery class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_logger = Mock(spec=logging.Logger)
        self.recovery = ErrorRecovery(self.mock_logger)
    
    def test_initialization(self):
        """Test error recovery initialization."""
        recovery = ErrorRecovery()
        assert recovery.logger is not None
    
    @patch('time.sleep')
    def test_retry_operation_success_first_try(self, mock_sleep):
        """Test retry operation that succeeds on first try."""
        operation = Mock(return_value="success")
        
        result = self.recovery.retry_operation(operation, max_retries=3)
        
        assert result == "success"
        operation.assert_called_once()
        mock_sleep.assert_not_called()
    
    @patch('time.sleep')
    def test_retry_operation_success_after_retries(self, mock_sleep):
        """Test retry operation that succeeds after retries."""
        operation = Mock(side_effect=[ValueError("Error 1"), ValueError("Error 2"), "success"])
        
        result = self.recovery.retry_operation(operation, max_retries=3, delay=0.1)
        
        assert result == "success"
        assert operation.call_count == 3
        assert mock_sleep.call_count == 2
        self.mock_logger.warning.assert_called()
    
    @patch('time.sleep')
    def test_retry_operation_all_retries_fail(self, mock_sleep):
        """Test retry operation that fails all retries."""
        operation = Mock(side_effect=ValueError("Persistent error"))
        
        with pytest.raises(ValueError, match="Persistent error"):
            self.recovery.retry_operation(operation, max_retries=2, delay=0.1)
        
        assert operation.call_count == 3  # Initial + 2 retries
        assert mock_sleep.call_count == 2
        self.mock_logger.error.assert_called()
    
    def test_safe_cleanup_all_succeed(self):
        """Test safe cleanup when all operations succeed."""
        cleanup1 = Mock()
        cleanup2 = Mock()
        cleanup3 = Mock()
        
        self.recovery.safe_cleanup([cleanup1, cleanup2, cleanup3])
        
        cleanup1.assert_called_once()
        cleanup2.assert_called_once()
        cleanup3.assert_called_once()
        self.mock_logger.warning.assert_not_called()
    
    def test_safe_cleanup_some_fail(self):
        """Test safe cleanup when some operations fail."""
        cleanup1 = Mock()
        cleanup2 = Mock(side_effect=Exception("Cleanup failed"))
        cleanup3 = Mock()
        
        self.recovery.safe_cleanup([cleanup1, cleanup2, cleanup3])
        
        cleanup1.assert_called_once()
        cleanup2.assert_called_once()
        cleanup3.assert_called_once()  # Should continue despite failure
        self.mock_logger.warning.assert_called_once()


class TestDecorators:
    """Test the error handling decorators."""
    
    def test_error_handler_decorator_success(self):
        """Test error handler decorator - success case."""
        @error_handler_decorator()
        def test_function(x, y):
            return x + y
        
        result = test_function(2, 3)
        assert result == 5
    
    def test_error_handler_decorator_with_error(self):
        """Test error handler decorator - error case."""
        error_callback = Mock()
        
        @error_handler_decorator(error_callback)
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        
        assert result is None
        error_callback.assert_called_once()
    
    def test_gui_error_handler_decorator(self):
        """Test GUI error handler decorator."""
        @gui_error_handler("Test Operation")
        def test_method():
            return "success"
        
        # The decorator should wrap the function
        assert callable(test_method)
        
        # Test that it works (basic functionality)
        result = test_method()
        assert result == "success"


class TestIntegration:
    """Integration tests for the error handling system."""
    
    def test_end_to_end_error_handling(self):
        """Test complete error handling flow."""
        # Set up components
        logger = Mock(spec=logging.Logger)
        show_dialog = Mock()
        
        gui_handler = GUIErrorHandler(logger, show_dialog)
        middleware = ErrorMiddleware(logger)
        middleware.set_gui_error_handler(gui_handler)
        
        # Create a function that raises an error
        def failing_operation():
            raise ValidationError("Test validation error", field="test_field")
        
        # Handle the error through middleware
        middleware.handle_operation_error(
            ValidationError("Test validation error", field="test_field"),
            "Test Operation"
        )
        
        # Verify error was logged and dialog was shown
        assert logger.warning.called or logger.error.called
        show_dialog.assert_called_once()
    
    def test_error_context_preservation(self):
        """Test that error context is preserved through handling."""
        logger = Mock(spec=logging.Logger)
        handler = ErrorHandler(logger)
        
        # Create error with context
        error = PublishError("Test error")
        error.add_context("operation", "test_operation")
        error.add_context("file_path", "/test/path")
        
        # Handle error with additional context
        additional_context = {"user_id": "test_user"}
        message = handler.handle_error(error, additional_context)
        
        # Verify both contexts are in log
        log_call = logger.error.call_args[0][0]
        assert "user_id=test_user" in log_call
        assert "operation=test_operation" in log_call
        assert "file_path=/test/path" in log_call